import unittest
import json
from flask import url_for
from app import create_app, db
from app.models.user import User
from app.models.course import Course
from app.models.resource import Resource, Question, Exam, ExamQuestion

class IntegrationTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户
        self.user = User(username='testuser', email='<EMAIL>', password='password', role='teacher')
        db.session.add(self.user)
        db.session.commit()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_user_login_logout(self):
        """测试用户登录登出流程"""
        # 这里只是一个占位测试，实际登录功能尚未实现
        # 将来会实现完整的登录测试
        pass
    
    def test_course_creation(self):
        """测试课程创建流程"""
        # 登录用户
        self.client.post(url_for('auth.login'), data={
            'email': '<EMAIL>',
            'password': 'password'
        })
        
        # 测试API创建课程
        course_data = {
            'name': 'API Integration Test Course',
            'description': 'This is a test course created via API',
            'code_length': 9
        }
        
        response = self.client.post(
            url_for('course.create_course_api'),
            data=json.dumps(course_data),
            content_type='application/json'
        )
        
        # 验证API响应
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['name'], 'API Integration Test Course')
        self.assertEqual(len(data['access_code']), 9)
        
        # 验证API创建的课程
        course = Course.query.filter_by(name='API Integration Test Course').first()
        self.assertIsNotNone(course)
        self.assertEqual(course.description, 'This is a test course created via API')
        self.assertEqual(len(course.access_code), 9)

if __name__ == '__main__':
    unittest.main()