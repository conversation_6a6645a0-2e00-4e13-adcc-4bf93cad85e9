import unittest
import json
from flask import url_for
from app import create_app, db
from app.models.user import User
from app.models.course import Course
from app.models.resource import Resource, Question, Exam, ExamQuestion

class CourseAPITestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户（教师和学生）
        self.teacher = User(username='teacher', email='<EMAIL>', 
                           password='password', role='teacher')
        self.student = User(username='student', email='<EMAIL>', 
                           password='password', role='student')
        db.session.add_all([self.teacher, self.student])
        db.session.commit()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def login(self, email, password):
        """辅助方法：用户登录"""
        return self.client.post(url_for('auth.login'), data={
            'email': email,
            'password': password
        }, follow_redirects=True)
    
    def test_create_course_api_success(self):
        """测试成功创建课程API"""
        # 教师登录
        self.login('<EMAIL>', 'password')
        
        # 创建课程数据
        course_data = {
            'name': 'Test Course API',
            'description': 'This is a test course created via API',
            'code_length': 6,
            'start_time': '2025-01-01T09:00:00',
            'end_time': '2025-06-30T18:00:00'
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.create_course_api'),
            data=json.dumps(course_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['name'], 'Test Course API')
        self.assertEqual(data['description'], 'This is a test course created via API')
        self.assertEqual(len(data['access_code']), 6)
        self.assertEqual(data['teacher_id'], self.teacher.id)
        self.assertIn('id', data)
        self.assertIn('created_at', data)
        self.assertIn('message', data)
        
        # 验证数据库中的课程
        course = Course.query.get(data['id'])
        self.assertIsNotNone(course)
        self.assertEqual(course.name, 'Test Course API')
        self.assertEqual(course.description, 'This is a test course created via API')
        self.assertEqual(len(course.access_code), 6)
        self.assertEqual(course.teacher_id, self.teacher.id)
    
    def test_create_course_api_unauthorized(self):
        """测试未授权用户创建课程API"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 创建课程数据
        course_data = {
            'name': 'Unauthorized Course',
            'description': 'This should fail',
            'code_length': 4
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.create_course_api'),
            data=json.dumps(course_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 403)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '只有教师可以创建课程')
        
        # 验证数据库中没有创建课程
        course = Course.query.filter_by(name='Unauthorized Course').first()
        self.assertIsNone(course)
    
    def test_create_course_api_invalid_data(self):
        """测试无效数据创建课程API"""
        # 教师登录
        self.login('<EMAIL>', 'password')
        
        # 缺少课程名称
        course_data = {
            'description': 'Missing name',
            'code_length': 4
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.create_course_api'),
            data=json.dumps(course_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '课程名称不能为空')
        
        # 无效的日期格式
        course_data = {
            'name': 'Invalid Date Course',
            'description': 'Course with invalid date',
            'start_time': 'not-a-date'
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.create_course_api'),
            data=json.dumps(course_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertIn('日期格式无效', data['error'])
    
    def test_create_course_api_code_length(self):
        """测试不同长度的课程码"""
        # 教师登录
        self.login('<EMAIL>', 'password')
        
        # 测试4位课程码
        course_data = {
            'name': 'Course with 4-digit code',
            'code_length': 4
        }
        
        response = self.client.post(
            url_for('course.create_course_api'),
            data=json.dumps(course_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(len(data['access_code']), 4)
        
        # 测试6位课程码
        course_data = {
            'name': 'Course with 6-digit code',
            'code_length': 6
        }
        
        response = self.client.post(
            url_for('course.create_course_api'),
            data=json.dumps(course_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(len(data['access_code']), 6)
        
        # 测试9位课程码
        course_data = {
            'name': 'Course with 9-digit code',
            'code_length': 9
        }
        
        response = self.client.post(
            url_for('course.create_course_api'),
            data=json.dumps(course_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(len(data['access_code']), 9)
        
        # 测试无效长度，应默认为4位
        course_data = {
            'name': 'Course with invalid code length',
            'code_length': 5  # 不是4、6或9
        }
        
        response = self.client.post(
            url_for('course.create_course_api'),
            data=json.dumps(course_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(len(data['access_code']), 4)  # 默认为4位

if __name__ == '__main__':
    unittest.main()