import unittest
from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent
from app.models.resource import Resource
from datetime import datetime
import os
import tempfile

class TestResourceSearch(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client()
        
        # 创建测试用户
        self.teacher = User(username='teacher', email='<EMAIL>', password='password', role='teacher')
        self.student = User(username='student', email='<EMAIL>', password='password', role='student')
        db.session.add_all([self.teacher, self.student])
        db.session.commit()
        
        # 创建测试课程
        self.course = Course(name='测试课程', description='这是一个测试课程', teacher_id=self.teacher.id)
        self.course.generate_access_code()
        db.session.add(self.course)
        db.session.commit()
        
        # 学生加入课程
        self.course_student = CourseStudent(course_id=self.course.id, student_id=self.student.id)
        db.session.add(self.course_student)
        db.session.commit()
        
        # 创建测试资源
        self.resources = [
            Resource(
                name='测试文档.pdf',
                type='document',
                url='test_document.pdf',
                size=1024,
                format='pdf',
                owner_id=self.teacher.id,
                course_id=self.course.id,
                uploaded_at=datetime.utcnow()
            ),
            Resource(
                name='测试图片.jpg',
                type='image',
                url='test_image.jpg',
                size=2048,
                format='jpg',
                owner_id=self.teacher.id,
                course_id=self.course.id,
                uploaded_at=datetime.utcnow()
            ),
            Resource(
                name='个人笔记.txt',
                type='document',
                url='personal_note.txt',
                size=512,
                format='txt',
                owner_id=self.student.id,
                course_id=None,
                uploaded_at=datetime.utcnow()
            )
        ]
        db.session.add_all(self.resources)
        db.session.commit()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_search_page(self):
        """测试资源搜索页面"""
        # 登录学生账号
        response = self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 访问搜索页面
        response = self.client.get('/search')
        self.assertEqual(response.status_code, 200)
        
        # 检查页面内容
        self.assertIn('搜索资源', response.get_data(as_text=True))
        self.assertIn('关键词', response.get_data(as_text=True))
        self.assertIn('资源类型', response.get_data(as_text=True))
        self.assertIn('课程', response.get_data(as_text=True))
    
    def test_search_by_keyword(self):
        """测试按关键词搜索资源"""
        # 登录学生账号
        response = self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 按关键词搜索
        response = self.client.get('/search?q=测试')
        self.assertEqual(response.status_code, 200)
        
        # 检查搜索结果
        content = response.get_data(as_text=True)
        self.assertIn('测试文档.pdf', content)
        self.assertIn('测试图片.jpg', content)
        self.assertNotIn('个人笔记.txt', content)  # 不匹配关键词
    
    def test_search_by_type(self):
        """测试按资源类型搜索资源"""
        # 登录学生账号
        response = self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 按资源类型搜索
        response = self.client.get('/search?type=document')
        self.assertEqual(response.status_code, 200)
        
        # 检查搜索结果
        content = response.get_data(as_text=True)
        self.assertIn('测试文档.pdf', content)
        self.assertNotIn('测试图片.jpg', content)  # 不是文档类型
        self.assertIn('个人笔记.txt', content)
    
    def test_search_by_course(self):
        """测试按课程搜索资源"""
        # 登录学生账号
        response = self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 按课程搜索
        response = self.client.get(f'/search?course_id={self.course.id}')
        self.assertEqual(response.status_code, 200)
        
        # 检查搜索结果
        content = response.get_data(as_text=True)
        self.assertIn('测试文档.pdf', content)
        self.assertIn('测试图片.jpg', content)
        self.assertNotIn('个人笔记.txt', content)  # 不属于课程
    
    def test_search_sort_by_name(self):
        """测试按名称排序搜索结果"""
        # 登录学生账号
        response = self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 按名称升序排序
        response = self.client.get('/search?sort_by=name&sort_order=asc')
        self.assertEqual(response.status_code, 200)
        
        # 检查搜索结果顺序
        content = response.get_data(as_text=True)
        name_index1 = content.find('个人笔记.txt')
        name_index2 = content.find('测试图片.jpg')
        name_index3 = content.find('测试文档.pdf')
        
        # 验证排序顺序：个人笔记 < 测试图片 < 测试文档
        self.assertTrue(0 < name_index1 < name_index2 < name_index3)
    
    def test_search_sort_by_size(self):
        """测试按大小排序搜索结果"""
        # 登录学生账号
        response = self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 按大小降序排序
        response = self.client.get('/search?sort_by=size&sort_order=desc')
        self.assertEqual(response.status_code, 200)
        
        # 检查搜索结果顺序
        content = response.get_data(as_text=True)
        size_index1 = content.find('测试图片.jpg')  # 2048
        size_index2 = content.find('测试文档.pdf')  # 1024
        size_index3 = content.find('个人笔记.txt')  # 512
        
        # 验证排序顺序：测试图片 > 测试文档 > 个人笔记
        self.assertTrue(0 < size_index1 < size_index2 < size_index3)

if __name__ == '__main__':
    unittest.main()