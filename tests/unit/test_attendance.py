import unittest
import json
from datetime import datetime, date
import sys
import os
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent, Attendance

class AttendanceTestCase(unittest.TestCase):
    def setUp(self):
        """测试前的设置"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户（教师）
        self.teacher = User(
            username='测试教师',
            email='<EMAIL>',
            password='password',
            role='teacher'
        )
        db.session.add(self.teacher)
        
        # 创建测试用户（学生）
        self.students = []
        for i in range(5):
            student = User(
                username=f'学生{i+1}',
                email=f'student{i+1}@example.com',
                password='password',
                role='student'
            )
            self.students.append(student)
            db.session.add(student)
        
        db.session.commit()
        
        # 创建测试课程
        self.course = Course(
            name='测试课程',
            description='这是一个测试课程',
            teacher_id=self.teacher.id
        )
        self.course.generate_access_code()
        db.session.add(self.course)
        db.session.commit()
        
        # 添加学生到课程
        self.course_students = []
        for student in self.students:
            cs = CourseStudent(
                course_id=self.course.id,
                student_id=student.id
            )
            self.course_students.append(cs)
            db.session.add(cs)
        
        db.session.commit()
        
        # 创建测试考勤记录
        today = date.today()
        
        # 学生1：出勤
        attendance1 = Attendance(
            course_student_id=self.course_students[0].id,
            status='present',
            date=today,
            check_in_time=datetime.now(),
            note='准时到达'
        )
        db.session.add(attendance1)
        
        # 学生2：缺勤
        attendance2 = Attendance(
            course_student_id=self.course_students[1].id,
            status='absent',
            date=today,
            note='未到'
        )
        db.session.add(attendance2)
        
        # 学生3：迟到
        attendance3 = Attendance(
            course_student_id=self.course_students[2].id,
            status='late',
            date=today,
            check_in_time=datetime.now(),
            note='迟到10分钟'
        )
        db.session.add(attendance3)
        
        db.session.commit()
        
        # 登录教师账号
        self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
    
    def tearDown(self):
        """测试后的清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_attendance_page(self):
        """测试考勤管理页面"""
        response = self.client.get(f'/group/attendance/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        self.assertIn('考勤管理'.encode(), response.data)
    
    def test_get_attendance_api(self):
        """测试获取考勤记录API"""
        today = date.today().isoformat()
        response = self.client.get(f'/group/api/attendance/{self.course.id}?date={today}')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('date', data)
        self.assertIn('attendance', data)
        
        attendance = data['attendance']
        self.assertEqual(len(attendance), 5)  # 5个学生
        
        # 验证考勤状态
        statuses = [a['status'] for a in attendance]
        self.assertEqual(statuses.count('present'), 1)  # 1个出勤
        self.assertEqual(statuses.count('absent'), 2)  # 2个缺勤（1个记录，1个默认）
        self.assertEqual(statuses.count('late'), 1)  # 1个迟到
    
    def test_update_attendance_api(self):
        """测试更新考勤记录API"""
        today = date.today().isoformat()
        
        # 更新考勤记录
        attendance_data = [
            {
                'student_id': self.students[0].id,
                'status': 'present',
                'note': '准时到达，表现良好'
            },
            {
                'student_id': self.students[1].id,
                'status': 'present',  # 从缺勤改为出勤
                'note': '原来记错了'
            },
            {
                'student_id': self.students[2].id,
                'status': 'late',
                'note': '迟到15分钟'  # 更新备注
            },
            {
                'student_id': self.students[3].id,
                'status': 'absent',
                'note': '请假'
            },
            {
                'student_id': self.students[4].id,
                'status': 'late',
                'note': '迟到5分钟'
            }
        ]
        
        response = self.client.post(
            f'/group/api/attendance/{self.course.id}',
            json={
                'date': today,
                'attendance': attendance_data
            }
        )
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('message', data)
        self.assertIn('5', data['message'])  # 更新了5条记录
        
        # 验证数据库中的考勤记录是否更新
        # 学生1：出勤
        cs1 = CourseStudent.query.filter_by(
            course_id=self.course.id,
            student_id=self.students[0].id
        ).first()
        attendance1 = Attendance.query.filter_by(
            course_student_id=cs1.id,
            date=date.today()
        ).first()
        self.assertEqual(attendance1.status, 'present')
        self.assertEqual(attendance1.note, '准时到达，表现良好')
        
        # 学生2：从缺勤改为出勤
        cs2 = CourseStudent.query.filter_by(
            course_id=self.course.id,
            student_id=self.students[1].id
        ).first()
        attendance2 = Attendance.query.filter_by(
            course_student_id=cs2.id,
            date=date.today()
        ).first()
        self.assertEqual(attendance2.status, 'present')
        self.assertEqual(attendance2.note, '原来记错了')
        
        # 学生3：迟到，更新备注
        cs3 = CourseStudent.query.filter_by(
            course_id=self.course.id,
            student_id=self.students[2].id
        ).first()
        attendance3 = Attendance.query.filter_by(
            course_student_id=cs3.id,
            date=date.today()
        ).first()
        self.assertEqual(attendance3.status, 'late')
        self.assertEqual(attendance3.note, '迟到15分钟')
        
        # 学生4：缺勤
        cs4 = CourseStudent.query.filter_by(
            course_id=self.course.id,
            student_id=self.students[3].id
        ).first()
        attendance4 = Attendance.query.filter_by(
            course_student_id=cs4.id,
            date=date.today()
        ).first()
        self.assertEqual(attendance4.status, 'absent')
        self.assertEqual(attendance4.note, '请假')
        
        # 学生5：迟到
        cs5 = CourseStudent.query.filter_by(
            course_id=self.course.id,
            student_id=self.students[4].id
        ).first()
        attendance5 = Attendance.query.filter_by(
            course_student_id=cs5.id,
            date=date.today()
        ).first()
        self.assertEqual(attendance5.status, 'late')
        self.assertEqual(attendance5.note, '迟到5分钟')

if __name__ == '__main__':
    unittest.main()