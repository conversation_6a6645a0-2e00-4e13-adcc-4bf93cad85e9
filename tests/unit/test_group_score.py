import unittest
import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from app import create_app, db
from app.models.user import User
from app.models.course import Course, Group
from app.models.score import Score
import json
from datetime import datetime

class GroupScoreTestCase(unittest.TestCase):
    def setUp(self):
        """测试前设置"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client()
        
        # 修复路由问题
        from app.controllers.interaction import interaction
        self.app.register_blueprint(interaction, url_prefix='/')
        
        # 创建测试用户（教师）
        self.teacher = User(
            username='teacher',
            email='<EMAIL>',
            role='teacher'
        )
        self.teacher.password = 'password'
        
        # 创建测试用户（学生）
        self.student = User(
            username='student',
            email='<EMAIL>',
            role='student'
        )
        self.student.password = 'password'
        
        # 创建测试课程
        self.course = Course(
            name='测试课程',
            teacher_id=1  # 教师ID为1
        )
        self.course.generate_access_code()
        
        # 创建测试小组
        self.group = Group(
            name='测试小组',
            course_id=1  # 课程ID为1
        )
        
        # 保存到数据库
        db.session.add(self.teacher)
        db.session.add(self.student)
        db.session.add(self.course)
        db.session.add(self.group)
        db.session.commit()
    
    def tearDown(self):
        """测试后清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def login(self, username, password):
        """登录辅助函数"""
        return self.client.post('/auth/login', data={
            'username': username,
            'password': password
        }, follow_redirects=True)
    
    def test_group_score_page(self):
        """测试小组评分页面"""
        # 登录教师账号
        self.login('teacher', 'password')
        
        # 访问小组评分页面
        response = self.client.get('/group-score')
        
        # 验证页面加载成功
        self.assertEqual(response.status_code, 200)
    
    def test_group_score_submission(self):
        """测试提交小组评分"""
        # 登录教师账号
        self.login('teacher', 'password')
        
        # 提交评分
        response = self.client.post('/group-score', data={
            'course_id': 1,
            'group_id': 1,
            'score': 5,
            'reason': '表现优秀'
        }, follow_redirects=True)
        
        # 验证评分成功
        self.assertEqual(response.status_code, 200)
        
        # 验证数据库中有评分记录
        score = Score.query.filter_by(target_id=1, target_type='group').first()
        self.assertIsNotNone(score)
        self.assertEqual(score.score, 5)
        self.assertEqual(score.reason, '表现优秀')
        self.assertEqual(score.target_type, 'group')
    
    def test_group_score_api(self):
        """测试小组评分API"""
        # 登录教师账号
        self.login('teacher', 'password')
        
        # 提交评分
        response = self.client.post('/api/group-score', 
                                   json={
                                       'course_id': 1,
                                       'group_id': 1,
                                       'score': -3,
                                       'reason': '表现不佳'
                                   },
                                   content_type='application/json')
        
        # 验证API响应
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # 验证数据库中有评分记录
        score = Score.query.filter_by(target_id=1, target_type='group').first()
        self.assertIsNotNone(score)
        self.assertEqual(score.score, -3)
        self.assertEqual(score.reason, '表现不佳')
        self.assertEqual(score.target_type, 'group')
    
    def test_invalid_group(self):
        """测试评分无效小组的情况"""
        # 登录教师账号
        self.login('teacher', 'password')
        
        # 创建另一个课程
        course2 = Course(
            name='测试课程2',
            teacher_id=1  # 教师ID为1
        )
        course2.generate_access_code()
        db.session.add(course2)
        
        # 创建另一个小组（属于另一个课程）
        group2 = Group(
            name='测试小组2',
            course_id=2  # 课程ID为2
        )
        db.session.add(group2)
        db.session.commit()
        
        # 提交评分（小组不属于所选课程）
        response = self.client.post('/api/group-score', 
                                   json={
                                       'course_id': 1,
                                       'group_id': 2,  # 小组ID为2，但属于课程2
                                       'score': 5,
                                       'reason': '表现优秀'
                                   },
                                   content_type='application/json')
        
        # 验证API响应
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        
        # 验证数据库中没有评分记录
        score = Score.query.filter_by(target_id=2, target_type='group').first()
        self.assertIsNone(score)
    
    def test_group_score_permission(self):
        """测试学生无权使用评分功能"""
        # 登录学生账号
        self.login('student', 'password')
        
        # 尝试访问评分页面
        response = self.client.get('/group-score', follow_redirects=True)
        
        # 验证被重定向并显示错误消息
        self.assertEqual(response.status_code, 200)
        
        # 尝试使用评分API
        response = self.client.post('/api/group-score', 
                                   json={
                                       'course_id': 1,
                                       'group_id': 1,
                                       'score': 5,
                                       'reason': '表现优秀'
                                   },
                                   content_type='application/json')
        
        # 验证API响应
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertFalse(data['success'])

if __name__ == '__main__':
    unittest.main()