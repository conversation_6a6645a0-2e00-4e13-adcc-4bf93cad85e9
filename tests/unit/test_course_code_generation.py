import unittest
from app import create_app, db
from app.models.user import User
from app.models.course import Course

class CourseCodeGenerationTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        
        # 创建测试教师
        self.teacher = User(username='teacher', email='<EMAIL>', 
                           password='password', role='teacher')
        db.session.add(self.teacher)
        db.session.commit()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_code_generation_4_digits(self):
        """测试4位数字码生成"""
        course = Course(name='Test Course 4', teacher_id=self.teacher.id)
        code = course.generate_access_code(4)
        
        # 验证长度
        self.assertEqual(len(code), 4)
        # 验证只包含数字
        self.assertTrue(code.isdigit())
        # 验证课程对象的access_code被设置
        self.assertEqual(course.access_code, code)
    
    def test_code_generation_6_digits(self):
        """测试6位数字码生成"""
        course = Course(name='Test Course 6', teacher_id=self.teacher.id)
        code = course.generate_access_code(6)
        
        # 验证长度
        self.assertEqual(len(code), 6)
        # 验证只包含数字
        self.assertTrue(code.isdigit())
        # 验证课程对象的access_code被设置
        self.assertEqual(course.access_code, code)
    
    def test_code_generation_9_digits(self):
        """测试9位数字码生成"""
        course = Course(name='Test Course 9', teacher_id=self.teacher.id)
        code = course.generate_access_code(9)
        
        # 验证长度
        self.assertEqual(len(code), 9)
        # 验证只包含数字
        self.assertTrue(code.isdigit())
        # 验证课程对象的access_code被设置
        self.assertEqual(course.access_code, code)
    
    def test_code_generation_invalid_length(self):
        """测试无效长度默认为4位"""
        course = Course(name='Test Course Invalid', teacher_id=self.teacher.id)
        
        # 测试无效长度（如5位）
        code = course.generate_access_code(5)
        self.assertEqual(len(code), 4)  # 应该默认为4位
        
        # 测试无效长度（如0位）
        code = course.generate_access_code(0)
        self.assertEqual(len(code), 4)  # 应该默认为4位
        
        # 测试无效长度（如负数）
        code = course.generate_access_code(-1)
        self.assertEqual(len(code), 4)  # 应该默认为4位
    
    def test_code_uniqueness(self):
        """测试生成的访问码唯一性"""
        codes = set()
        courses = []
        
        # 生成多个课程并检查访问码唯一性
        for i in range(50):
            course = Course(name=f'Test Course {i}', teacher_id=self.teacher.id)
            code = course.generate_access_code(4)
            
            # 验证访问码不重复
            self.assertNotIn(code, codes, f"访问码 {code} 重复了")
            codes.add(code)
            courses.append(course)
            
            # 保存到数据库以确保数据库级别的唯一性检查
            db.session.add(course)
        
        db.session.commit()
        
        # 验证所有课程都有不同的访问码
        self.assertEqual(len(codes), 50)
    
    def test_code_generation_with_existing_codes(self):
        """测试在已有访问码的情况下生成新的唯一访问码"""
        # 先创建一个课程
        existing_course = Course(name='Existing Course', teacher_id=self.teacher.id)
        existing_code = existing_course.generate_access_code(4)
        db.session.add(existing_course)
        db.session.commit()
        
        # 创建新课程，应该生成不同的访问码
        new_course = Course(name='New Course', teacher_id=self.teacher.id)
        new_code = new_course.generate_access_code(4)
        
        # 验证新访问码与已存在的不同
        self.assertNotEqual(existing_code, new_code)
        self.assertEqual(len(new_code), 4)
        self.assertTrue(new_code.isdigit())
    
    def test_code_generation_fallback_mechanism(self):
        """测试访问码生成的回退机制"""
        # 这个测试比较难模拟，因为需要模拟所有可能的4位数字都被占用的情况
        # 但我们可以测试时间戳后缀的逻辑
        
        # 创建一个课程并手动设置其访问码生成逻辑
        course = Course(name='Fallback Test Course', teacher_id=self.teacher.id)
        
        # 模拟多次尝试后的回退机制
        # 通过直接调用方法并检查结果
        code = course.generate_access_code(4)
        
        # 基本验证
        self.assertEqual(len(code), 4)
        self.assertTrue(code.isdigit())
        self.assertEqual(course.access_code, code)
    
    def test_multiple_courses_different_lengths(self):
        """测试多个课程使用不同长度的访问码"""
        courses_data = [
            ('Course 4-digit', 4),
            ('Course 6-digit', 6),
            ('Course 9-digit', 9),
            ('Course default', 4),  # 默认长度
        ]
        
        generated_codes = []
        
        for name, length in courses_data:
            course = Course(name=name, teacher_id=self.teacher.id)
            if length == 4 and name == 'Course default':
                # 测试默认长度（不传参数）
                code = course.generate_access_code()
            else:
                code = course.generate_access_code(length)
            
            # 验证长度
            expected_length = 4 if length == 4 or name == 'Course default' else length
            self.assertEqual(len(code), expected_length)
            
            # 验证只包含数字
            self.assertTrue(code.isdigit())
            
            # 收集所有生成的访问码
            generated_codes.append(code)
            
            db.session.add(course)
        
        db.session.commit()
        
        # 验证所有访问码都是唯一的
        self.assertEqual(len(generated_codes), len(set(generated_codes)))

if __name__ == '__main__':
    unittest.main()