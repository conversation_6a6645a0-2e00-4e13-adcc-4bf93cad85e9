import unittest
from unittest.mock import patch, MagicMock
import json
import sys
import os
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from app import create_app, db
from app.models.device import DeviceStatus
from app.services.device_monitor import get_all_device_status

class DeviceMonitorTestCase(unittest.TestCase):
    def setUp(self):
        """测试前的设置"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
    
    def tearDown(self):
        """测试后的清理"""
        self.app_context.pop()
    
    @patch('app.services.device_monitor.DeviceStatus')
    def test_get_all_device_status(self, mock_device_status):
        """测试获取所有设备状态"""
        # 模拟设备状态
        mock_device1 = MagicMock()
        mock_device1.to_dict.return_value = {
            'device_id': 'teacher-device-001',
            'type': 'teacher',
            'owner_id': 1,
            'status': 'online',
            'ip_address': '*************',
            'last_ping': '2023-07-23T10:00:00'
        }
        
        mock_device2 = MagicMock()
        mock_device2.to_dict.return_value = {
            'device_id': 'group-device-001',
            'type': 'group',
            'owner_id': 1,
            'status': 'online',
            'ip_address': '*************',
            'last_ping': '2023-07-23T10:00:00'
        }
        
        mock_device3 = MagicMock()
        mock_device3.to_dict.return_value = {
            'device_id': 'student-device-001',
            'type': 'student',
            'owner_id': 2,
            'status': 'offline',
            'ip_address': '*************',
            'last_ping': '2023-07-23T10:00:00'
        }
        
        # 设置模拟查询结果
        mock_device_status.query.all.return_value = [mock_device1, mock_device2, mock_device3]
        
        # 调用函数
        result = get_all_device_status()
        
        # 验证结果
        self.assertEqual(len(result), 3)
        self.assertEqual(result[0]['device_id'], 'teacher-device-001')
        self.assertEqual(result[1]['device_id'], 'group-device-001')
        self.assertEqual(result[2]['device_id'], 'student-device-001')
        
        # 验证设备状态
        online_devices = [d for d in result if d['status'] == 'online']
        offline_devices = [d for d in result if d['status'] == 'offline']
        self.assertEqual(len(online_devices), 2)  # 教师设备和小组设备在线
        self.assertEqual(len(offline_devices), 1)  # 学生设备离线
    
    @patch('app.services.device_monitor.DeviceStatus')
    def test_device_status_update(self, mock_device_status):
        """测试设备状态更新"""
        # 模拟设备状态
        mock_device = MagicMock()
        mock_device.to_dict.return_value = {
            'device_id': 'test-device-001',
            'type': 'student',
            'owner_id': 1,
            'status': 'online',
            'ip_address': '*************',
            'last_ping': '2023-07-23T10:00:00'
        }
        
        # 设置模拟查询结果
        mock_device_status.query.filter_by.return_value.first.return_value = mock_device
        
        # 模拟设备状态更新
        from app.services.device_monitor import device_status_cache
        
        # 更新缓存
        device_status_cache['test-device-001'] = mock_device.to_dict()
        
        # 验证缓存
        self.assertIn('test-device-001', device_status_cache)
        self.assertEqual(device_status_cache['test-device-001']['status'], 'online')

if __name__ == '__main__':
    unittest.main()