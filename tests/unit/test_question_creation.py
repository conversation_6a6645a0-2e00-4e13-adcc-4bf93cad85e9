import unittest
from app import create_app, db
from app.models.resource import Question
from app.models.user import User
from app.models.course import Course
from app.models.interaction import QuestionSession, SessionQuestion, TemporaryQuestion, ScreenshotQuestion
import json
from datetime import datetime, timedelta
import base64
import os

class QuestionCreationTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户（教师）
        self.teacher = User(
            username='teacher',
            email='<EMAIL>',
            role='teacher'
        )
        self.teacher.password = 'password'
        
        # 创建测试用户（学生）
        self.student = User(
            username='student',
            email='<EMAIL>',
            role='student'
        )
        self.student.password = 'password'
        
        # 创建测试课程
        self.course = Course(
            name='测试课程',
            teacher_id=1,  # 教师ID为1
            description='这是一个测试课程'
        )
        self.course.generate_access_code()
        
        # 添加到数据库
        db.session.add(self.teacher)
        db.session.add(self.student)
        db.session.add(self.course)
        db.session.commit()
        
        # 创建测试题目
        self.question = Question(
            type='single',
            content=json.dumps({'ops': [{'insert': '这是一道测试题目'}]}),
            options=['选项A', '选项B', '选项C', '选项D'],
            answer=1,  # 正确答案是B
            score=2.0,
            created_by=1  # 教师ID为1
        )
        
        db.session.add(self.question)
        db.session.commit()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def login(self, email, password):
        return self.client.post('/auth/login', data={
            'email': email,
            'password': password
        }, follow_redirects=True)
    
    def logout(self):
        return self.client.get('/auth/logout', follow_redirects=True)
    
    def test_create_question(self):
        """测试创建习题"""
        # 登录教师账号
        self.login('<EMAIL>', 'password')
        
        # 创建习题
        response = self.client.post('/interaction/questions/create', data={
            'type': 'single',
            'content': json.dumps({'ops': [{'insert': '这是一道新的测试题目'}]}),
            'options': json.dumps(['选项A', '选项B', '选项C', '选项D']),
            'answer': json.dumps(2),  # 正确答案是C
            'score': 3.0
        })
        
        # 检查响应
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # 检查数据库
        question = Question.query.filter_by(created_by=1).order_by(Question.id.desc()).first()
        self.assertIsNotNone(question)
        self.assertEqual(question.type, 'single')
        self.assertEqual(question.score, 3.0)
        self.assertEqual(question.answer, 2)
    
    def test_send_question_from_library(self):
        """测试从题库发送题目"""
        # 登录教师账号
        self.login('<EMAIL>', 'password')
        
        # 发送题目
        response = self.client.post('/interaction/send-question', data={
            'course_id': self.course.id,
            'question_type': 'existing',
            'title': '测试题目会话',
            'question_ids': [self.question.id]
        }, follow_redirects=True)
        
        # 检查响应
        self.assertEqual(response.status_code, 200)
        self.assertIn('题目发送成功', response.get_data(as_text=True))
        
        # 检查数据库
        session = QuestionSession.query.filter_by(course_id=self.course.id).first()
        self.assertIsNotNone(session)
        self.assertEqual(session.title, '测试题目会话')
        
        # 检查会话题目
        session_question = SessionQuestion.query.filter_by(session_id=session.id).first()
        self.assertIsNotNone(session_question)
        self.assertEqual(session_question.question_id, self.question.id)
    
    def test_send_temporary_question(self):
        """测试发送临时创建的题目"""
        # 登录教师账号
        self.login('<EMAIL>', 'password')
        
        # 发送临时题目
        response = self.client.post('/interaction/send-question', data={
            'course_id': self.course.id,
            'question_type': 'temporary',
            'title': '临时题目会话',
            'temp_question_type': 'multiple',
            'content': json.dumps({'ops': [{'insert': '这是一道临时多选题'}]}),
            'options': json.dumps(['选项A', '选项B', '选项C', '选项D']),
            'answer': json.dumps([0, 2]),  # 正确答案是A和C
            'score': 4.0
        }, follow_redirects=True)
        
        # 检查响应
        self.assertEqual(response.status_code, 200)
        self.assertIn('题目发送成功', response.get_data(as_text=True))
        
        # 检查数据库
        session = QuestionSession.query.filter_by(title='临时题目会话').first()
        self.assertIsNotNone(session)
        
        # 检查临时题目
        temp_question = TemporaryQuestion.query.filter_by(session_id=session.id).first()
        self.assertIsNotNone(temp_question)
        self.assertEqual(temp_question.type, 'multiple')
        self.assertEqual(temp_question.score, 4.0)
        self.assertEqual(temp_question.options, ['选项A', '选项B', '选项C', '选项D'])
        self.assertEqual(temp_question.answer, [0, 2])
    
    def test_send_screenshot_question(self):
        """测试发送截屏题目"""
        # 登录教师账号
        self.login('<EMAIL>', 'password')
        
        # 创建测试图片数据
        with open('qr_code_test.png', 'rb') as f:
            image_data = f.read()
        
        # 转换为base64
        base64_data = base64.b64encode(image_data).decode('utf-8')
        screenshot_data = f"data:image/png;base64,{base64_data}"
        
        # 发送截屏题目
        response = self.client.post('/interaction/send-question', data={
            'course_id': self.course.id,
            'question_type': 'screenshot',
            'title': '截屏题目会话',
            'screenshot_data': screenshot_data,
            'description': '这是一道截屏题目',
            'answer': '这是参考答案',
            'score': 5.0
        }, follow_redirects=True)
        
        # 检查响应
        self.assertEqual(response.status_code, 200)
        self.assertIn('题目发送成功', response.get_data(as_text=True))
        
        # 检查数据库
        session = QuestionSession.query.filter_by(title='截屏题目会话').first()
        self.assertIsNotNone(session)
        
        # 检查截屏题目
        screenshot_question = ScreenshotQuestion.query.filter_by(session_id=session.id).first()
        self.assertIsNotNone(screenshot_question)
        self.assertEqual(screenshot_question.description, '这是一道截屏题目')
        self.assertEqual(screenshot_question.answer, '这是参考答案')
        self.assertEqual(screenshot_question.score, 5.0)
        self.assertTrue(screenshot_question.image_url.startswith('/static/uploads/screenshots/'))
    
    def test_student_submit_answer(self):
        """测试学生提交答案"""
        # 登录教师账号
        self.login('<EMAIL>', 'password')
        
        # 发送题目
        self.client.post('/interaction/send-question', data={
            'course_id': self.course.id,
            'question_type': 'existing',
            'title': '测试题目会话',
            'question_ids': [self.question.id]
        })
        
        # 获取会话ID
        session = QuestionSession.query.filter_by(course_id=self.course.id).first()
        session_question = SessionQuestion.query.filter_by(session_id=session.id).first()
        
        # 登出教师账号
        self.logout()
        
        # 登录学生账号
        self.login('<EMAIL>', 'password')
        
        # 将学生添加到课程
        from app.models.course import CourseStudent
        course_student = CourseStudent(
            course_id=self.course.id,
            student_id=self.student.id
        )
        db.session.add(course_student)
        db.session.commit()
        
        # 提交答案
        response = self.client.post(f'/interaction/question-sessions/{session.id}/submit-answer', 
                                   json={
                                       'question_type': 'session',
                                       'question_id': session_question.id,
                                       'answer': 1  # 选择B
                                   })
        
        # 检查响应
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # 检查数据库
        from app.models.interaction import StudentAnswer
        answer = StudentAnswer.query.filter_by(
            student_id=self.student.id,
            session_question_id=session_question.id
        ).first()
        
        self.assertIsNotNone(answer)
        self.assertEqual(answer.answer, 1)
        self.assertTrue(answer.is_correct)  # 答案正确
        self.assertEqual(answer.score, 2.0)  # 得分为2.0
    
    def test_end_question_session(self):
        """测试结束题目会话"""
        # 登录教师账号
        self.login('<EMAIL>', 'password')
        
        # 发送题目
        self.client.post('/interaction/send-question', data={
            'course_id': self.course.id,
            'question_type': 'existing',
            'title': '测试题目会话',
            'question_ids': [self.question.id]
        })
        
        # 获取会话ID
        session = QuestionSession.query.filter_by(course_id=self.course.id).first()
        
        # 结束会话
        response = self.client.post(f'/interaction/question-sessions/{session.id}/end')
        
        # 检查响应
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # 检查数据库
        session = QuestionSession.query.get(session.id)
        self.assertEqual(session.status, 'ended')
        self.assertIsNotNone(session.end_time)

if __name__ == '__main__':
    unittest.main()