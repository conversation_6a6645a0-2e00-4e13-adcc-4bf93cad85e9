import unittest
from flask import url_for
from app import create_app, db
from app.models.user import User
from app.models.course import Course, Group, GroupMember
from app.services.whiteboard import WhiteboardNamespace
import json
from unittest.mock import patch, MagicMock

class WhiteboardTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client()
        
        # 创建测试用户
        self.teacher = User(username='teacher', email='<EMAIL>', role='teacher')
        self.teacher.set_password('password')
        
        self.student1 = User(username='student1', email='<EMAIL>', role='student')
        self.student1.set_password('password')
        
        self.student2 = User(username='student2', email='<EMAIL>', role='student')
        self.student2.set_password('password')
        
        db.session.add_all([self.teacher, self.student1, self.student2])
        db.session.commit()
        
        # 创建测试课程
        self.course = Course(name='Test Course', teacher_id=self.teacher.id)
        db.session.add(self.course)
        db.session.commit()
        
        # 创建测试小组
        self.group = Group(name='Test Group', course_id=self.course.id)
        db.session.add(self.group)
        db.session.commit()
        
        # 添加学生到小组
        self.group_member = GroupMember(group_id=self.group.id, student_id=self.student1.id)
        db.session.add(self.group_member)
        db.session.commit()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def login(self, email, password):
        return self.client.post('/auth/login', data={
            'email': email,
            'password': password
        }, follow_redirects=True)
    
    def test_whiteboard_access_teacher(self):
        """测试教师访问白板页面"""
        self.login('<EMAIL>', 'password')
        response = self.client.get(f'/group/whiteboard/{self.group.id}')
        self.assertEqual(response.status_code, 200)
        self.assertIn('小组白板协作', response.data.decode())
    
    def test_whiteboard_access_group_member(self):
        """测试小组成员访问白板页面"""
        self.login('<EMAIL>', 'password')
        response = self.client.get(f'/group/whiteboard/{self.group.id}')
        self.assertEqual(response.status_code, 200)
        self.assertIn('小组白板协作', response.data.decode())
    
    def test_whiteboard_access_denied(self):
        """测试非小组成员访问白板页面被拒绝"""
        self.login('<EMAIL>', 'password')
        response = self.client.get(f'/group/whiteboard/{self.group.id}')
        self.assertEqual(response.status_code, 302)  # 重定向到首页
    
    def test_whiteboard_namespace_connect(self):
        """测试白板WebSocket命名空间连接"""
        namespace = WhiteboardNamespace('/whiteboard')
        
        # 模拟请求对象
        namespace.request = MagicMock()
        namespace.request.args = {
            'user_id': str(self.student1.id),
            'user_name': self.student1.username,
            'group_id': str(self.group.id)
        }
        
        # 模拟房间加入
        namespace.join_room = MagicMock()
        
        # 模拟发送事件
        namespace.emit = MagicMock()
        
        # 测试连接
        result = namespace.on_connect()
        self.assertTrue(result)
        namespace.join_room.assert_called_once()
        
        # 验证用户被添加到小组
        self.assertIn(str(self.group.id), namespace.group_users)
        self.assertIn(str(self.student1.id), namespace.group_users[str(self.group.id)])
    
    def test_whiteboard_operation_transform(self):
        """测试白板操作转换算法"""
        namespace = WhiteboardNamespace('/whiteboard')
        
        # 初始化小组历史记录
        group_id = str(self.group.id)
        namespace.operation_history[group_id] = []
        
        # 创建两个操作
        operation1 = {
            'id': 'op1',
            'userId': str(self.student1.id),
            'elements': [
                {'id': 'elem1', 'type': 'rectangle', 'version': 1},
                {'id': 'elem2', 'type': 'ellipse', 'version': 1}
            ],
            'timestamp': 1000
        }
        
        operation2 = {
            'id': 'op2',
            'userId': str(self.student2.id),
            'elements': [
                {'id': 'elem1', 'type': 'rectangle', 'version': 2},  # 更新的元素
                {'id': 'elem3', 'type': 'text', 'version': 1}  # 新元素
            ],
            'timestamp': 1100
        }
        
        # 添加第一个操作到历史记录
        namespace.operation_history[group_id].append(operation1)
        
        # 转换第二个操作
        transformed = namespace._transform_operation(group_id, operation2)
        
        # 验证转换结果
        self.assertEqual(transformed['id'], 'op2')
        self.assertEqual(transformed['userId'], str(self.student2.id))
        
        # 验证元素合并
        elements = transformed['elements']
        self.assertEqual(len(elements), 3)  # 应该有3个元素
        
        # 验证元素ID和版本
        element_map = {elem['id']: elem for elem in elements}
        self.assertIn('elem1', element_map)
        self.assertIn('elem2', element_map)
        self.assertIn('elem3', element_map)
        self.assertEqual(element_map['elem1']['version'], 2)  # 应该使用更新的版本
    
    def test_whiteboard_clear(self):
        """测试清除白板功能"""
        namespace = WhiteboardNamespace('/whiteboard')
        
        # 初始化白板状态
        group_id = str(self.group.id)
        namespace.whiteboard_states[group_id] = [
            {'id': 'elem1', 'type': 'rectangle'},
            {'id': 'elem2', 'type': 'ellipse'}
        ]
        namespace.operation_history[group_id] = ['operation1', 'operation2']
        
        # 模拟发送事件
        namespace.emit = MagicMock()
        
        # 测试清除白板
        namespace.on_whiteboard_clear({
            'group_id': group_id,
            'user_id': str(self.student1.id)
        })
        
        # 验证白板状态被清除
        self.assertEqual(namespace.whiteboard_states[group_id], [])
        self.assertEqual(namespace.operation_history[group_id], [])
        
        # 验证清除事件被广播
        namespace.emit.assert_called_once()
        args, kwargs = namespace.emit.call_args
        self.assertEqual(args[0], 'whiteboard_state')
        self.assertEqual(args[1]['elements'], [])
        self.assertEqual(kwargs['room'], f'group_{group_id}')

if __name__ == '__main__':
    unittest.main()