import unittest
from datetime import datetime
from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent, Group, GroupMember
from app.models.resource import Resource, Question, Exam, ExamQuestion

class ModelsTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_user_password_setter(self):
        """测试用户密码设置"""
        u = User(username='test', email='<EMAIL>', password='cat')
        self.assertTrue(u.password_hash is not None)
    
    def test_user_password_verification(self):
        """测试用户密码验证"""
        u = User(username='test', email='<EMAIL>', password='cat')
        self.assertTrue(u.verify_password('cat'))
        self.assertFalse(u.verify_password('dog'))
    
    def test_user_password_salts_are_random(self):
        """测试用户密码盐值随机性"""
        u1 = User(username='test1', email='<EMAIL>', password='cat')
        u2 = User(username='test2', email='<EMAIL>', password='cat')
        self.assertNotEqual(u1.password_hash, u2.password_hash)
    
    def test_user_password_no_getter(self):
        """测试密码属性不可读"""
        u = User(username='test', email='<EMAIL>', password='cat')
        with self.assertRaises(AttributeError):
            u.password
    
    def test_user_role_methods(self):
        """测试用户角色方法"""
        teacher = User(username='teacher', email='<EMAIL>', password='password', role='teacher')
        student = User(username='student', email='<EMAIL>', password='password', role='student')
        
        self.assertTrue(teacher.is_teacher())
        self.assertFalse(teacher.is_student())
        self.assertTrue(student.is_student())
        self.assertFalse(student.is_teacher())
    
    def test_user_to_dict(self):
        """测试用户转字典方法"""
        u = User(username='test', email='<EMAIL>', password='password', role='student')
        db.session.add(u)
        db.session.commit()
        
        user_dict = u.to_dict()
        self.assertEqual(user_dict['username'], 'test')
        self.assertEqual(user_dict['email'], '<EMAIL>')
        self.assertEqual(user_dict['role'], 'student')
        self.assertIsNotNone(user_dict['created_at'])
    
    def test_user_creation_defaults(self):
        """测试用户创建默认值"""
        u = User(username='test', email='<EMAIL>', password='password')
        db.session.add(u)
        db.session.commit()
        
        self.assertEqual(u.role, 'student')  # 默认角色为学生
        self.assertIsNotNone(u.created_at)
        self.assertIsNone(u.last_login)
        self.assertIsNone(u.avatar)
    
    def test_user_unique_constraints(self):
        """测试用户唯一性约束"""
        u1 = User(username='test', email='<EMAIL>', password='password')
        db.session.add(u1)
        db.session.commit()
        
        # 测试用户名唯一性
        u2 = User(username='test', email='<EMAIL>', password='password')
        db.session.add(u2)
        with self.assertRaises(Exception):
            db.session.commit()
        
        db.session.rollback()
        
        # 测试邮箱唯一性
        u3 = User(username='test2', email='<EMAIL>', password='password')
        db.session.add(u3)
        with self.assertRaises(Exception):
            db.session.commit()
    
    def test_course_access_code_generation(self):
        """测试课程访问码生成"""
        teacher = User(username='teacher', email='<EMAIL>', password='password', role='teacher')
        db.session.add(teacher)
        db.session.commit()
        
        course = Course(name='Test Course', teacher_id=teacher.id)
        code = course.generate_access_code(4)
        self.assertEqual(len(code), 4)
        self.assertEqual(course.access_code, code)
        
        # 测试不同长度
        code6 = course.generate_access_code(6)
        self.assertEqual(len(code6), 6)
        
        code9 = course.generate_access_code(9)
        self.assertEqual(len(code9), 9)

if __name__ == '__main__':
    unittest.main()