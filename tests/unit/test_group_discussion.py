import unittest
from datetime import datetime, timedelta
import json
import base64
from io import BytesIO
from PIL import Image
from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent
from app.models.interaction import GroupDiscussion

class GroupDiscussionTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client()
        
        # 创建测试用户（教师）
        self.teacher = User(name='测试教师', email='<EMAIL>', role='teacher')
        self.teacher.set_password('password')
        
        # 创建测试用户（学生）
        self.student = User(name='测试学生', email='<EMAIL>', role='student')
        self.student.set_password('password')
        
        # 创建测试课程
        self.course = Course(name='测试课程', teacher_id=1, access_code='123456')
        
        # 添加学生到课程
        self.course_student = CourseStudent(course_id=1, student_id=2)
        
        db.session.add_all([self.teacher, self.student, self.course, self.course_student])
        db.session.commit()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def login(self, email, password):
        return self.client.post('/auth/login', data={
            'email': email,
            'password': password
        }, follow_redirects=True)
    
    def logout(self):
        return self.client.get('/auth/logout', follow_redirects=True)
    
    def test_teacher_access_group_discussion(self):
        """测试教师访问分组讨论页面"""
        # 教师登录
        self.login('<EMAIL>', 'password')
        
        # 访问分组讨论页面
        response = self.client.get('/interaction/group-discussion')
        self.assertEqual(response.status_code, 200)
        self.assertIn('发起讨论'.encode('utf-8'), response.data)
    
    def test_student_access_group_discussion(self):
        """测试学生访问分组讨论页面（应被重定向）"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 尝试访问分组讨论页面
        response = self.client.get('/interaction/group-discussion')
        self.assertEqual(response.status_code, 302)  # 应该被重定向
    
    def test_student_access_student_discussion(self):
        """测试学生访问学生讨论页面"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 访问学生讨论页面
        response = self.client.get('/interaction/student-discussion')
        self.assertEqual(response.status_code, 200)
        self.assertIn('当前没有活跃的讨论'.encode('utf-8'), response.data)
    
    def test_teacher_create_text_discussion(self):
        """测试教师创建文本讨论"""
        # 教师登录
        self.login('<EMAIL>', 'password')
        
        # 创建文本讨论
        response = self.client.post('/interaction/group-discussion', data={
            'course_id': '1',
            'title': '测试讨论',
            'description': '这是一个测试讨论',
            'duration': '10',
            'topic_type': 'text',
            'topic_content': '这是讨论的主题内容'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('讨论已发起'.encode('utf-8'), response.data)
        
        # 验证数据库中是否创建了讨论
        discussion = GroupDiscussion.query.first()
        self.assertIsNotNone(discussion)
        self.assertEqual(discussion.title, '测试讨论')
        self.assertEqual(discussion.topic_content, '这是讨论的主题内容')
        self.assertEqual(discussion.status, 'active')
    
    def test_teacher_create_screenshot_discussion(self):
        """测试教师创建截屏讨论"""
        # 教师登录
        self.login('<EMAIL>', 'password')
        
        # 创建一个测试图片
        img = Image.new('RGB', (100, 100), color='red')
        img_io = BytesIO()
        img.save(img_io, 'PNG')
        img_io.seek(0)
        img_base64 = base64.b64encode(img_io.getvalue()).decode('utf-8')
        img_data = f'data:image/png;base64,{img_base64}'
        
        # 创建截屏讨论
        response = self.client.post('/interaction/group-discussion', data={
            'course_id': '1',
            'title': '截屏讨论',
            'description': '这是一个截屏讨论',
            'duration': '15',
            'topic_type': 'screenshot',
            'screenshot_data': img_data
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('讨论已发起'.encode('utf-8'), response.data)
        
        # 验证数据库中是否创建了讨论
        discussion = GroupDiscussion.query.first()
        self.assertIsNotNone(discussion)
        self.assertEqual(discussion.title, '截屏讨论')
        self.assertIsNotNone(discussion.image_url)
        self.assertTrue(discussion.image_url.startswith('/static/uploads/screenshots/'))
    
    def test_end_discussion(self):
        """测试结束讨论"""
        # 教师登录
        self.login('<EMAIL>', 'password')
        
        # 创建讨论
        discussion = GroupDiscussion(
            course_id=1,
            title='测试讨论',
            description='这是一个测试讨论',
            topic_content='讨论主题',
            created_by=1,
            end_time=datetime.utcnow() + timedelta(minutes=10),
            status='active'
        )
        db.session.add(discussion)
        db.session.commit()
        
        # 结束讨论
        response = self.client.post(f'/interaction/end-discussion/{discussion.id}')
        data = json.loads(response.data)
        
        self.assertTrue(data['success'])
        self.assertEqual(data['message'], '讨论已结束')
        
        # 验证讨论状态是否已更新
        updated_discussion = GroupDiscussion.query.get(discussion.id)
        self.assertEqual(updated_discussion.status, 'ended')
    
    def test_student_view_discussions(self):
        """测试学生查看讨论"""
        # 创建活跃讨论
        active_discussion = GroupDiscussion(
            course_id=1,
            title='活跃讨论',
            description='这是一个活跃讨论',
            topic_content='活跃讨论主题',
            created_by=1,
            end_time=datetime.utcnow() + timedelta(minutes=10),
            status='active'
        )
        
        # 创建已结束讨论
        ended_discussion = GroupDiscussion(
            course_id=1,
            title='已结束讨论',
            description='这是一个已结束讨论',
            topic_content='已结束讨论主题',
            created_by=1,
            end_time=datetime.utcnow() - timedelta(minutes=10),
            status='ended'
        )
        
        db.session.add_all([active_discussion, ended_discussion])
        db.session.commit()
        
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 访问学生讨论页面
        response = self.client.get('/interaction/student-discussion')
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('活跃讨论'.encode('utf-8'), response.data)
        self.assertIn('活跃讨论主题'.encode('utf-8'), response.data)
        self.assertIn('已结束讨论'.encode('utf-8'), response.data)

if __name__ == '__main__':
    unittest.main()