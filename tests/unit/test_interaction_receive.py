"""
互动接收功能单元测试
"""
import unittest
import sys
import os
import json
from unittest.mock import patch, MagicMock
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app import create_app
from app.models.course import Course
from app.models.activity import ClassActivity

class InteractionReceiveTestCase(unittest.TestCase):
    """互动接收功能测试用例"""
    
    def setUp(self):
        """测试前设置"""
        self.app = create_app('testing')
        self.app.config['LOGIN_DISABLED'] = True  # 禁用登录要求
        self.app.config['TESTING'] = True
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
    
    def tearDown(self):
        """测试后清理"""
        self.app_context.pop()
    
    @patch('flask_login.utils._get_user')
    @patch('app.controllers.interaction.Course')
    @patch('app.controllers.interaction.ClassActivity')
    def test_receive_interaction_page_access(self, mock_activity, mock_course, mock_get_user):
        """测试学生访问互动接收页面"""
        # 模拟当前用户为学生
        mock_student = MagicMock()
        mock_student.is_teacher.return_value = False
        mock_student.id = 2
        mock_get_user.return_value = mock_student
        
        # 模拟课程
        mock_course_instance = MagicMock()
        mock_course_instance.id = 1
        mock_course_instance.name = "Test Course"
        mock_course.query.filter_by.return_value.first_or_404.return_value = mock_course_instance
        
        # 模拟学生已加入课程
        mock_course_instance.has_student.return_value = True
        
        # 模拟互动内容
        mock_activity_instance = MagicMock()
        mock_activity_instance.id = 1
        mock_activity_instance.title = "Test Interaction"
        mock_activity_instance.description = "Test Description"
        mock_activity_instance.content = "Test Content"
        mock_activity_instance.type = "text"
        mock_activity_instance.created_at = datetime.utcnow()
        mock_activity.query.filter_by.return_value.order_by.return_value.limit.return_value.all.return_value = [mock_activity_instance]
        
        # 访问互动接收页面
        with self.client as c:
            response = c.get('/interaction/receive/1')
            self.assertEqual(response.status_code, 200)
    
    @patch('flask_login.utils._get_user')
    @patch('app.controllers.interaction.Course')
    def test_unauthorized_student_access(self, mock_course, mock_get_user):
        """测试未授权学生访问互动接收页面"""
        # 模拟当前用户为学生
        mock_student = MagicMock()
        mock_student.is_teacher.return_value = False
        mock_student.id = 3
        mock_get_user.return_value = mock_student
        
        # 模拟课程
        mock_course_instance = MagicMock()
        mock_course_instance.id = 1
        mock_course_instance.name = "Test Course"
        mock_course.query.filter_by.return_value.first_or_404.return_value = mock_course_instance
        
        # 模拟学生未加入课程
        mock_course_instance.has_student.return_value = False
        
        # 访问互动接收页面
        with self.client as c:
            response = c.get('/interaction/receive/1')
            self.assertEqual(response.status_code, 403)
    
    @patch('flask_login.utils._get_user')
    @patch('app.controllers.interaction.Course')
    @patch('app.controllers.interaction.ClassActivity')
    @patch('app.controllers.interaction.db.session')
    @patch('app.controllers.interaction.socketio')
    def test_send_interaction_api(self, mock_socketio, mock_session, mock_activity, mock_course, mock_get_user):
        """测试发送互动内容API"""
        # 模拟当前用户为教师
        mock_teacher = MagicMock()
        mock_teacher.is_teacher.return_value = True
        mock_teacher.id = 1
        mock_get_user.return_value = mock_teacher
        
        # 模拟课程
        mock_course_instance = MagicMock()
        mock_course_instance.id = 1
        mock_course_instance.name = "Test Course"
        mock_course.query.filter_by.return_value.first_or_404.return_value = mock_course_instance
        
        # 模拟活动记录
        mock_activity_instance = MagicMock()
        mock_activity_instance.id = 1
        mock_activity_instance.created_at = datetime.utcnow()
        mock_activity.return_value = mock_activity_instance
        
        # 发送互动内容
        with self.client as c:
            response = c.post('/interaction/api/send-interaction/1', 
                             json={
                                 'type': 'text',
                                 'title': 'Test Interaction',
                                 'description': 'Test Description',
                                 'content': 'Test Content'
                             },
                             content_type='application/json')
            
            data = json.loads(response.data)
            self.assertEqual(response.status_code, 200)
            self.assertTrue(data['success'])
            self.assertEqual(data['message'], '互动内容已发送')
            
            # 验证WebSocket通知
            mock_socketio.emit.assert_called_once()

if __name__ == '__main__':
    unittest.main()