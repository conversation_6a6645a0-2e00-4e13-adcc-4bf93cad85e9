import unittest
from flask import url_for
from app import create_app, db
from app.models.user import User
from app.models.course import Course, Group, GroupMember
from app.services.webrtc import active_rooms, active_connections, group_broadcasts
import json

class GroupBroadcastTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户
        self.teacher = User(username='teacher', email='<EMAIL>', role='teacher')
        self.teacher.set_password('password')
        
        self.student1 = User(username='student1', email='<EMAIL>', role='student')
        self.student1.set_password('password')
        
        self.student2 = User(username='student2', email='<EMAIL>', role='student')
        self.student2.set_password('password')
        
        # 创建测试课程
        self.course = Course(name='Test Course', teacher_id=1)
        self.course.generate_access_code()
        
        # 添加到数据库
        db.session.add_all([self.teacher, self.student1, self.student2, self.course])
        db.session.commit()
        
        # 学生加入课程
        from app.controllers.course import add_student_to_course
        add_student_to_course(self.course.id, self.student1.id)
        add_student_to_course(self.course.id, self.student2.id)
        
        # 创建小组
        self.group = Group(name='Test Group', course_id=self.course.id)
        db.session.add(self.group)
        db.session.commit()
        
        # 学生加入小组
        self.group_member = GroupMember(group_id=self.group.id, student_id=self.student1.id)
        db.session.add(self.group_member)
        db.session.commit()
        
        # 清空WebRTC服务状态
        active_rooms.clear()
        active_connections.clear()
        group_broadcasts.clear()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def login(self, username, password):
        return self.client.post(url_for('auth.login'), data={
            'username': username,
            'password': password
        }, follow_redirects=True)
    
    def logout(self):
        return self.client.get(url_for('auth.logout'), follow_redirects=True)
    
    def test_group_broadcast_page_access(self):
        """测试小组广播页面访问权限"""
        # 未登录用户无法访问
        response = self.client.get(url_for('broadcast.group_broadcast', course_id=self.course.id))
        self.assertEqual(response.status_code, 302)  # 重定向到登录页面
        
        # 教师可以访问
        self.login('teacher', 'password')
        response = self.client.get(url_for('broadcast.group_broadcast', course_id=self.course.id, group_id=self.group.id))
        self.assertEqual(response.status_code, 200)
        self.logout()
        
        # 小组成员可以访问
        self.login('student1', 'password')
        response = self.client.get(url_for('broadcast.group_broadcast', course_id=self.course.id))
        self.assertEqual(response.status_code, 200)
        self.logout()
        
        # 非小组成员无法访问
        self.login('student2', 'password')
        response = self.client.get(url_for('broadcast.group_broadcast', course_id=self.course.id))
        self.assertEqual(response.status_code, 403)
        self.logout()
    
    def test_start_group_broadcast_api(self):
        """测试开始小组广播API"""
        self.login('student1', 'password')
        
        # 发送开始广播请求
        response = self.client.post(
            url_for('broadcast.start_group_broadcast', course_id=self.course.id),
            data=json.dumps({'target': 'teacher'}),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['group_id'], self.group.id)
        self.assertEqual(data['target'], 'teacher')
        
        self.logout()
    
    def test_stop_group_broadcast_api(self):
        """测试停止小组广播API"""
        self.login('student1', 'password')
        
        # 发送停止广播请求
        response = self.client.post(
            url_for('broadcast.stop_group_broadcast', course_id=self.course.id),
            data=json.dumps({}),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['group_id'], self.group.id)
        
        self.logout()
    
    def test_get_group_broadcast_status_api(self):
        """测试获取小组广播状态API"""
        self.login('teacher', 'password')
        
        # 获取广播状态
        response = self.client.get(
            url_for('broadcast.get_group_broadcast_status', course_id=self.course.id)
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertIn('group_broadcasts', data)
        
        self.logout()

if __name__ == '__main__':
    unittest.main()