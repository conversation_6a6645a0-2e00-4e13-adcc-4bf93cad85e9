import unittest
from flask import url_for
from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent, Group, GroupMember
from app.models.interaction import RandomPickRecord

class TestRandomPick(unittest.TestCase):
    def setUp(self):
        """设置测试环境"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户（教师和学生）
        self.teacher = User(name='测试教师', email='<EMAIL>', password='password', role='teacher')
        self.student1 = User(name='测试学生1', email='<EMAIL>', password='password', role='student')
        self.student2 = User(name='测试学生2', email='<EMAIL>', password='password', role='student')
        
        db.session.add_all([self.teacher, self.student1, self.student2])
        db.session.commit()
        
        # 创建测试课程
        self.course = Course(name='测试课程', teacher_id=self.teacher.id)
        self.course.generate_access_code()
        db.session.add(self.course)
        db.session.commit()
        
        # 添加学生到课程
        self.course_student1 = CourseStudent(course_id=self.course.id, student_id=self.student1.id)
        self.course_student2 = CourseStudent(course_id=self.course.id, student_id=self.student2.id)
        db.session.add_all([self.course_student1, self.course_student2])
        
        # 创建测试小组
        self.group = Group(name='测试小组', course_id=self.course.id)
        db.session.add(self.group)
        db.session.commit()
        
        # 添加学生到小组
        self.group_member = GroupMember(group_id=self.group.id, student_id=self.student1.id)
        db.session.add(self.group_member)
        db.session.commit()
    
    def tearDown(self):
        """清理测试环境"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def login(self, email, password):
        """用户登录"""
        return self.client.post(
            '/auth/login',
            data={'email': email, 'password': password},
            follow_redirects=True
        )
    
    def test_random_pick_page_access(self):
        """测试随机点名页面访问权限"""
        # 未登录用户应被重定向到登录页面
        response = self.client.get('/interaction/random-pick', follow_redirects=True)
        self.assertIn('请登录', response.get_data(as_text=True))
        
        # 学生用户应被拒绝访问
        self.login('<EMAIL>', 'password')
        response = self.client.get('/interaction/random-pick', follow_redirects=True)
        self.assertIn('只有教师可以使用随机点名功能', response.get_data(as_text=True))
        
        # 教师用户应能访问
        self.login('<EMAIL>', 'password')
        response = self.client.get('/interaction/random-pick')
        self.assertEqual(response.status_code, 200)
        self.assertIn('随机点名', response.get_data(as_text=True))
    
    def test_random_pick_student(self):
        """测试随机点名学生"""
        self.login('<EMAIL>', 'password')
        
        # 随机点名学生
        response = self.client.post(
            '/interaction/random-pick',
            data={
                'course_id': self.course.id,
                'pick_type': 'student',
                'exclude_picked': 'false'
            },
            follow_redirects=True
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('点名结果', response.get_data(as_text=True))
        
        # 验证是否创建了点名记录
        record = RandomPickRecord.query.filter_by(
            course_id=self.course.id,
            pick_type='student'
        ).first()
        
        self.assertIsNotNone(record)
        self.assertTrue(record.target_id in [self.student1.id, self.student2.id])
    
    def test_random_pick_group(self):
        """测试随机点名小组"""
        self.login('<EMAIL>', 'password')
        
        # 随机点名小组
        response = self.client.post(
            '/interaction/random-pick',
            data={
                'course_id': self.course.id,
                'pick_type': 'group',
                'exclude_picked': 'false'
            },
            follow_redirects=True
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('点名结果', response.get_data(as_text=True))
        
        # 验证是否创建了点名记录
        record = RandomPickRecord.query.filter_by(
            course_id=self.course.id,
            pick_type='group'
        ).first()
        
        self.assertIsNotNone(record)
        self.assertEqual(record.target_id, self.group.id)
    
    def test_add_pick_score(self):
        """测试为点名添加评分"""
        self.login('<EMAIL>', 'password')
        
        # 先创建一个点名记录
        record = RandomPickRecord(
            course_id=self.course.id,
            pick_type='student',
            target_id=self.student1.id
        )
        db.session.add(record)
        db.session.commit()
        
        # 添加评分
        response = self.client.post(
            '/interaction/add-pick-score',
            data={
                'course_id': self.course.id,
                'pick_type': 'student',
                'target_id': self.student1.id,
                'score': 5,
                'note': '表现优秀'
            },
            follow_redirects=True
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('评分已保存', response.get_data(as_text=True))
        
        # 验证评分是否已保存
        updated_record = RandomPickRecord.query.filter_by(
            course_id=self.course.id,
            pick_type='student',
            target_id=self.student1.id
        ).first()
        
        self.assertIsNotNone(updated_record)
        self.assertEqual(updated_record.score, 5)
        self.assertEqual(updated_record.note, '表现优秀')
    
    def test_exclude_picked_students(self):
        """测试排除已点过的学生"""
        self.login('<EMAIL>', 'password')
        
        # 先创建一个点名记录
        record = RandomPickRecord(
            course_id=self.course.id,
            pick_type='student',
            target_id=self.student1.id
        )
        db.session.add(record)
        db.session.commit()
        
        # 随机点名并排除已点过的学生
        response = self.client.post(
            '/interaction/random-pick',
            data={
                'course_id': self.course.id,
                'pick_type': 'student',
                'exclude_picked': 'on'
            },
            follow_redirects=True
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('点名结果', response.get_data(as_text=True))
        
        # 验证是否选择了未点过的学生
        new_record = RandomPickRecord.query.filter_by(
            course_id=self.course.id,
            pick_type='student'
        ).order_by(RandomPickRecord.picked_at.desc()).first()
        
        self.assertIsNotNone(new_record)
        self.assertEqual(new_record.target_id, self.student2.id)

if __name__ == '__main__':
    unittest.main()