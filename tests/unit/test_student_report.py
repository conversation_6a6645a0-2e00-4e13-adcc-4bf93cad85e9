import unittest
from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent
from app.models.activity import ClassReport
from app.models.interaction import StudentAnswer, RandomPickRecord
from app.models.score import Score
from datetime import datetime, timedelta
import json

class TestStudentReport(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client()
        
        # 创建测试用户
        self.teacher = User(username='teacher', email='<EMAIL>', password='password', role='teacher')
        self.student = User(username='student', email='<EMAIL>', password='password', role='student')
        db.session.add_all([self.teacher, self.student])
        db.session.commit()
        
        # 创建测试课程
        self.course = Course(name='测试课程', description='这是一个测试课程', teacher_id=self.teacher.id)
        self.course.generate_access_code()
        db.session.add(self.course)
        db.session.commit()
        
        # 学生加入课程
        self.course_student = CourseStudent(course_id=self.course.id, student_id=self.student.id)
        db.session.add(self.course_student)
        db.session.commit()
        
        # 创建测试报告
        self.report = ClassReport(
            course_id=self.course.id,
            title='测试报告',
            date=datetime.utcnow().date(),
            attendance_count=1,
            question_count=2,
            interaction_count=3,
            created_by=self.teacher.id
        )
        db.session.add(self.report)
        db.session.commit()
        
        # 创建测试答题记录
        self.answer = StudentAnswer(
            student_id=self.student.id,
            answer=json.dumps({'answer': 'A'}),
            score=1.0,
            is_correct=True,
            submitted_at=datetime.utcnow()
        )
        db.session.add(self.answer)
        db.session.commit()
        
        # 创建测试随机点名记录
        self.random_pick = RandomPickRecord(
            course_id=self.course.id,
            pick_type='student',
            target_id=self.student.id,
            picked_at=datetime.utcnow(),
            score=2.0
        )
        db.session.add(self.random_pick)
        db.session.commit()
        
        # 创建测试评分记录
        self.score = Score(
            course_id=self.course.id,
            target_type='student',
            target_id=self.student.id,
            score=3.0,
            reason='表现优秀',
            created_by=self.teacher.id
        )
        db.session.add(self.score)
        db.session.commit()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_student_report_page(self):
        """测试学生报告页面"""
        # 登录学生账号
        response = self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 访问学生报告页面
        response = self.client.get(f'/student-report/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        
        # 检查页面内容
        self.assertIn('个人学习报告', response.get_data(as_text=True))
        self.assertIn('测试课程', response.get_data(as_text=True))
        self.assertIn('student', response.get_data(as_text=True))
    
    def test_student_report_api(self):
        """测试学生报告API"""
        # 登录学生账号
        response = self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 访问学生报告API
        response = self.client.get(f'/api/student-report/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        
        # 解析JSON响应
        data = json.loads(response.get_data(as_text=True))
        
        # 检查API响应
        self.assertTrue(data['success'])
        self.assertEqual(data['course']['name'], '测试课程')
        self.assertEqual(data['scores']['total_score'], 3.0)
        self.assertEqual(data['random_picks']['total'], 1)
        self.assertEqual(data['answers']['correct'], 1)

if __name__ == '__main__':
    unittest.main()