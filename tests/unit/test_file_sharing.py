import unittest
from flask import url_for
from io import BytesIO
from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent, Group, GroupMember
from app.models.resource import Resource
import os
import json

class TestFileSharing(unittest.TestCase):
    def setUp(self):
        """设置测试环境"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户（教师和学生）
        self.teacher = User(name='测试教师', email='<EMAIL>', password='password', role='teacher')
        self.student1 = User(name='测试学生1', email='<EMAIL>', password='password', role='student')
        self.student2 = User(name='测试学生2', email='<EMAIL>', password='password', role='student')
        
        db.session.add_all([self.teacher, self.student1, self.student2])
        db.session.commit()
        
        # 创建测试课程
        self.course = Course(name='测试课程', teacher_id=self.teacher.id)
        self.course.generate_access_code()
        db.session.add(self.course)
        db.session.commit()
        
        # 添加学生到课程
        self.course_student1 = CourseStudent(course_id=self.course.id, student_id=self.student1.id)
        self.course_student2 = CourseStudent(course_id=self.course.id, student_id=self.student2.id)
        db.session.add_all([self.course_student1, self.course_student2])
        
        # 创建测试小组
        self.group = Group(name='测试小组', course_id=self.course.id)
        db.session.add(self.group)
        db.session.commit()
        
        # 添加学生到小组
        self.group_member = GroupMember(group_id=self.group.id, student_id=self.student1.id)
        db.session.add(self.group_member)
        
        # 创建测试资源
        self.resource = Resource(
            name='测试文档.txt',
            type='document',
            url='test_doc.txt',
            size=1024,
            format='txt',
            owner_id=self.teacher.id
        )
        db.session.add(self.resource)
        db.session.commit()
        
        # 确保上传目录存在
        os.makedirs(self.app.config['UPLOAD_FOLDER'], exist_ok=True)
        
        # 创建测试文件
        with open(os.path.join(self.app.config['UPLOAD_FOLDER'], 'test_doc.txt'), 'w') as f:
            f.write('这是一个测试文档')
    
    def tearDown(self):
        """清理测试环境"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        
        # 删除测试文件
        try:
            os.remove(os.path.join(self.app.config['UPLOAD_FOLDER'], 'test_doc.txt'))
        except:
            pass
    
    def login(self, email, password):
        """用户登录"""
        return self.client.post(
            '/auth/login',
            data={'email': email, 'password': password},
            follow_redirects=True
        )
    
    def test_share_file_page_access(self):
        """测试文件分享页面访问权限"""
        # 未登录用户应被重定向到登录页面
        response = self.client.get('/interaction/share-file', follow_redirects=True)
        self.assertIn('请登录', response.get_data(as_text=True))
        
        # 学生用户应被拒绝访问
        self.login('<EMAIL>', 'password')
        response = self.client.get('/interaction/share-file', follow_redirects=True)
        self.assertIn('只有教师可以分享文件', response.get_data(as_text=True))
        
        # 教师用户应能访问
        self.login('<EMAIL>', 'password')
        response = self.client.get('/interaction/share-file')
        self.assertEqual(response.status_code, 200)
        self.assertIn('文件分享', response.get_data(as_text=True))
    
    def test_share_existing_resource(self):
        """测试分享现有资源"""
        self.login('<EMAIL>', 'password')
        
        # 分享现有资源给所有学生
        response = self.client.post(
            '/interaction/share-file',
            data={
                'course_id': self.course.id,
                'share_type': 'resource',
                'target_type': 'all',
                'resource_ids': self.resource.id
            },
            follow_redirects=True
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('资源分享成功', response.get_data(as_text=True))
    
    def test_share_local_file(self):
        """测试分享本地文件"""
        self.login('<EMAIL>', 'password')
        
        # 创建测试文件数据
        test_file = (BytesIO(b'这是一个测试文件内容'), 'test_share.txt')
        
        # 分享本地文件给指定小组
        response = self.client.post(
            '/interaction/share-file',
            data={
                'course_id': self.course.id,
                'share_type': 'local',
                'target_type': 'group',
                'target_ids': self.group.id,
                'file': test_file
            },
            content_type='multipart/form-data',
            follow_redirects=True
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('文件分享成功', response.get_data(as_text=True))
        
        # 验证文件是否已上传并创建了资源记录
        resource = Resource.query.filter_by(name='test_share.txt').first()
        self.assertIsNotNone(resource)
        self.assertEqual(resource.course_id, self.course.id)
        self.assertEqual(resource.owner_id, self.teacher.id)
        self.assertTrue(resource.file_metadata.get('shared'))
    
    def test_share_to_specific_student(self):
        """测试分享文件给特定学生"""
        self.login('<EMAIL>', 'password')
        
        # 分享现有资源给特定学生
        response = self.client.post(
            '/interaction/share-file',
            data={
                'course_id': self.course.id,
                'share_type': 'resource',
                'target_type': 'student',
                'target_ids': self.student1.id,
                'resource_ids': self.resource.id
            },
            follow_redirects=True
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('资源分享成功', response.get_data(as_text=True))

if __name__ == '__main__':
    unittest.main()