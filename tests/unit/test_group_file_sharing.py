import unittest
from flask import url_for
from io import BytesIO
import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent, Group, GroupMember
from app.models.resource import Resource

class TestGroupFileSharing(unittest.TestCase):
    def setUp(self):
        """设置测试环境"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户（教师和学生）
        self.teacher = User(username='测试教师', email='<EMAIL>', password='password', role='teacher')
        self.student1 = User(username='测试学生1', email='<EMAIL>', password='password', role='student')
        self.student2 = User(username='测试学生2', email='<EMAIL>', password='password', role='student')
        
        db.session.add_all([self.teacher, self.student1, self.student2])
        db.session.commit()
        
        # 创建测试课程
        self.course = Course(name='测试课程', teacher_id=self.teacher.id)
        self.course.generate_access_code()
        db.session.add(self.course)
        db.session.commit()
        
        # 添加学生到课程
        self.course_student1 = CourseStudent(course_id=self.course.id, student_id=self.student1.id)
        self.course_student2 = CourseStudent(course_id=self.course.id, student_id=self.student2.id)
        db.session.add_all([self.course_student1, self.course_student2])
        
        # 创建测试小组
        self.group = Group(name='测试小组', course_id=self.course.id)
        db.session.add(self.group)
        db.session.commit()
        
        # 添加学生到小组
        self.group_member1 = GroupMember(group_id=self.group.id, student_id=self.student1.id)
        self.group_member2 = GroupMember(group_id=self.group.id, student_id=self.student2.id)
        db.session.add_all([self.group_member1, self.group_member2])
        
        # 创建测试资源
        self.teacher_resource = Resource(
            name='教师文档.txt',
            type='document',
            url='teacher_doc.txt',
            size=1024,
            format='txt',
            owner_id=self.teacher.id
        )
        
        self.student_resource = Resource(
            name='学生文档.txt',
            type='document',
            url='student_doc.txt',
            size=1024,
            format='txt',
            owner_id=self.student1.id
        )
        
        db.session.add_all([self.teacher_resource, self.student_resource])
        db.session.commit()
        
        # 确保上传目录存在
        os.makedirs(self.app.config['UPLOAD_FOLDER'], exist_ok=True)
        
        # 创建测试文件
        with open(os.path.join(self.app.config['UPLOAD_FOLDER'], 'teacher_doc.txt'), 'w') as f:
            f.write('这是教师的测试文档')
        
        with open(os.path.join(self.app.config['UPLOAD_FOLDER'], 'student_doc.txt'), 'w') as f:
            f.write('这是学生的测试文档')
    
    def tearDown(self):
        """清理测试环境"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        
        # 删除测试文件
        try:
            os.remove(os.path.join(self.app.config['UPLOAD_FOLDER'], 'teacher_doc.txt'))
            os.remove(os.path.join(self.app.config['UPLOAD_FOLDER'], 'student_doc.txt'))
        except:
            pass
    
    def login(self, email, password):
        """用户登录"""
        return self.client.post(
            '/auth/login',
            data={'email': email, 'password': password},
            follow_redirects=True
        )
    
    def test_group_share_file_page_access(self):
        """测试小组文件分享页面访问权限"""
        # 未登录用户应被重定向到登录页面
        response = self.client.get('/group/share-file', follow_redirects=True)
        self.assertIn('请登录', response.get_data(as_text=True))
        
        # 教师用户应能访问
        self.login('<EMAIL>', 'password')
        response = self.client.get('/group/share-file')
        self.assertEqual(response.status_code, 200)
        self.assertIn('小组文件分享', response.get_data(as_text=True))
        
        # 学生用户应能访问
        self.login('<EMAIL>', 'password')
        response = self.client.get('/group/share-file')
        self.assertEqual(response.status_code, 200)
        self.assertIn('小组文件分享', response.get_data(as_text=True))
    
    def test_teacher_share_existing_resource_to_group(self):
        """测试教师分享现有资源到小组"""
        self.login('<EMAIL>', 'password')
        
        # 分享现有资源到小组
        response = self.client.post(
            '/group/share-file',
            data={
                'group_id': self.group.id,
                'share_type': 'resource',
                'resource_ids': self.teacher_resource.id
            },
            follow_redirects=True
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('文件分享成功', response.get_data(as_text=True))
        
        # 验证资源是否已标记为分享
        resource = Resource.query.get(self.teacher_resource.id)
        self.assertTrue(resource.file_metadata.get('shared'))
        self.assertEqual(resource.file_metadata.get('shared_to_group'), self.group.id)
    
    def test_student_share_existing_resource_to_group(self):
        """测试学生分享现有资源到小组"""
        self.login('<EMAIL>', 'password')
        
        # 分享现有资源到小组
        response = self.client.post(
            '/group/share-file',
            data={
                'group_id': self.group.id,
                'share_type': 'resource',
                'resource_ids': self.student_resource.id
            },
            follow_redirects=True
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('文件分享成功', response.get_data(as_text=True))
        
        # 验证资源是否已标记为分享
        resource = Resource.query.get(self.student_resource.id)
        self.assertTrue(resource.file_metadata.get('shared'))
        self.assertEqual(resource.file_metadata.get('shared_to_group'), self.group.id)
    
    def test_student_share_local_file_to_group(self):
        """测试学生分享本地文件到小组"""
        self.login('<EMAIL>', 'password')
        
        # 创建测试文件数据
        test_file = (BytesIO(b'This is a test file content'), 'test_group_share.txt')
        
        # 分享本地文件到小组
        response = self.client.post(
            '/group/share-file',
            data={
                'group_id': self.group.id,
                'share_type': 'local',
                'file': test_file
            },
            content_type='multipart/form-data',
            follow_redirects=True
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('文件分享成功', response.get_data(as_text=True))
        
        # 验证文件是否已上传并创建了资源记录
        resource = Resource.query.filter_by(name='test_group_share.txt').first()
        self.assertIsNotNone(resource)
        self.assertEqual(resource.course_id, self.course.id)
        self.assertEqual(resource.owner_id, self.student1.id)
        self.assertTrue(resource.file_metadata.get('shared'))
        self.assertEqual(resource.file_metadata.get('shared_to_group'), self.group.id)
    
    def test_student_share_to_unauthorized_group(self):
        """测试学生分享到未授权的小组"""
        # 创建另一个小组，学生不是其成员
        other_group = Group(name='其他小组', course_id=self.course.id)
        db.session.add(other_group)
        db.session.commit()
        
        self.login('<EMAIL>', 'password')
        
        # 尝试分享到未授权的小组
        response = self.client.post(
            '/group/share-file',
            data={
                'group_id': other_group.id,
                'share_type': 'resource',
                'resource_ids': self.student_resource.id
            },
            follow_redirects=True
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('您没有权限向此小组分享文件', response.get_data(as_text=True))
        
        # 验证资源未被标记为分享
        resource = Resource.query.get(self.student_resource.id)
        self.assertFalse(resource.file_metadata.get('shared') if resource.file_metadata else False)

if __name__ == '__main__':
    unittest.main()