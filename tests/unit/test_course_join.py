import unittest
import json
from flask import url_for
from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent

class CourseJoinTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户（教师和学生）
        self.teacher = User(username='teacher', email='<EMAIL>', 
                           password='password', role='teacher')
        self.student = User(username='student', email='<EMAIL>', 
                           password='password', role='student')
        db.session.add_all([self.teacher, self.student])
        db.session.commit()
        
        # 创建测试课程
        self.course = Course(
            name='Test Course',
            description='This is a test course',
            teacher_id=self.teacher.id
        )
        self.course.generate_access_code(length=4)
        db.session.add(self.course)
        db.session.commit()
        
        # 生成二维码
        self.course.generate_qr_code()
        db.session.commit()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def login(self, email, password):
        """辅助方法：用户登录"""
        return self.client.post(url_for('auth.login'), data={
            'email': email,
            'password': password
        }, follow_redirects=True)
    
    def test_join_by_code_api_success(self):
        """测试通过数字码成功加入课程"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 准备请求数据
        join_data = {
            'access_code': self.course.access_code
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.join_by_code_api'),
            data=json.dumps(join_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('message', data)
        self.assertIn('成功加入课程', data['message'])
        self.assertIn('course', data)
        self.assertEqual(data['course']['id'], self.course.id)
        self.assertEqual(data['course']['name'], self.course.name)
        
        # 验证数据库中的关联记录
        enrollment = CourseStudent.query.filter_by(
            course_id=self.course.id,
            student_id=self.student.id
        ).first()
        self.assertIsNotNone(enrollment)
    
    def test_join_by_code_api_invalid_code(self):
        """测试使用无效数字码加入课程"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 准备请求数据（无效的访问码）
        join_data = {
            'access_code': '0000'  # 假设这是一个无效的访问码
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.join_by_code_api'),
            data=json.dumps(join_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 404)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '课程不存在或访问码无效')
        
        # 验证数据库中没有关联记录
        enrollment = CourseStudent.query.filter_by(
            student_id=self.student.id
        ).first()
        self.assertIsNone(enrollment)
    
    def test_join_by_code_api_already_joined(self):
        """测试重复加入课程"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 先加入课程
        enrollment = CourseStudent(
            course_id=self.course.id,
            student_id=self.student.id
        )
        db.session.add(enrollment)
        db.session.commit()
        
        # 准备请求数据
        join_data = {
            'access_code': self.course.access_code
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.join_by_code_api'),
            data=json.dumps(join_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('message', data)
        self.assertEqual(data['message'], '您已经加入了此课程')
        
        # 验证数据库中只有一条关联记录
        enrollments = CourseStudent.query.filter_by(
            course_id=self.course.id,
            student_id=self.student.id
        ).count()
        self.assertEqual(enrollments, 1)
    
    def test_join_by_qr_api_success(self):
        """测试通过二维码成功加入课程"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 准备二维码数据
        qr_data = json.dumps({
            'course_id': self.course.id,
            'access_code': self.course.access_code,
            'join_url': f"http://localhost:5000/course/join/{self.course.access_code}"
        })
        
        # 准备请求数据
        join_data = {
            'qr_data': qr_data
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.join_by_qr_api'),
            data=json.dumps(join_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('message', data)
        self.assertIn('成功加入课程', data['message'])
        self.assertIn('course', data)
        self.assertEqual(data['course']['id'], self.course.id)
        self.assertEqual(data['course']['name'], self.course.name)
        
        # 验证数据库中的关联记录
        enrollment = CourseStudent.query.filter_by(
            course_id=self.course.id,
            student_id=self.student.id
        ).first()
        self.assertIsNotNone(enrollment)
    
    def test_join_by_qr_api_invalid_data(self):
        """测试使用无效二维码数据加入课程"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 准备无效的二维码数据
        invalid_qr_data = json.dumps({
            'course_id': 999,  # 假设这是一个不存在的课程ID
            'access_code': '0000'
        })
        
        # 准备请求数据
        join_data = {
            'qr_data': invalid_qr_data
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.join_by_qr_api'),
            data=json.dumps(join_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 404)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '课程不存在或访问码无效')
        
        # 验证数据库中没有关联记录
        enrollment = CourseStudent.query.filter_by(
            student_id=self.student.id
        ).first()
        self.assertIsNone(enrollment)
    
    def test_join_by_qr_api_malformed_data(self):
        """测试使用格式错误的二维码数据加入课程"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 准备格式错误的二维码数据
        malformed_qr_data = "这不是有效的JSON数据"
        
        # 准备请求数据
        join_data = {
            'qr_data': malformed_qr_data
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.join_by_qr_api'),
            data=json.dumps(join_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '二维码数据格式无效')
        
        # 验证数据库中没有关联记录
        enrollment = CourseStudent.query.filter_by(
            student_id=self.student.id
        ).first()
        self.assertIsNone(enrollment)
    
    def test_join_by_qr_api_missing_fields(self):
        """测试使用缺少字段的二维码数据加入课程"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 准备缺少字段的二维码数据
        incomplete_qr_data = json.dumps({
            'course_id': self.course.id
            # 缺少 access_code 字段
        })
        
        # 准备请求数据
        join_data = {
            'qr_data': incomplete_qr_data
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.join_by_qr_api'),
            data=json.dumps(join_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '二维码数据格式无效')
        
        # 验证数据库中没有关联记录
        enrollment = CourseStudent.query.filter_by(
            student_id=self.student.id
        ).first()
        self.assertIsNone(enrollment)

if __name__ == '__main__':
    unittest.main()