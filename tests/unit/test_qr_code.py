import unittest
import json
import base64
from flask import url_for
from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent
import qrcode
import io
from PIL import Image

class QRCodeTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户（教师和学生）
        self.teacher = User(username='teacher', email='<EMAIL>', 
                           password='password', role='teacher')
        self.student = User(username='student', email='<EMAIL>', 
                           password='password', role='student')
        db.session.add_all([self.teacher, self.student])
        db.session.commit()
        
        # 创建测试课程
        self.course = Course(
            name='Test Course',
            description='Test course for QR code testing',
            teacher_id=self.teacher.id
        )
        self.course.generate_access_code(length=6)
        db.session.add(self.course)
        db.session.commit()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def login(self, email, password):
        """辅助方法：用户登录"""
        return self.client.post(url_for('auth.login'), data={
            'email': email,
            'password': password
        }, follow_redirects=True)
    
    def test_generate_qr_code_model_method(self):
        """测试Course模型的generate_qr_code方法"""
        # 测试生成二维码
        base_url = "http://test.example.com"
        qr_code_data = self.course.generate_qr_code(base_url=base_url)
        
        # 验证返回的数据格式
        self.assertTrue(qr_code_data.startswith('data:image/png;base64,'))
        
        # 验证base64数据可以解码
        base64_data = qr_code_data.split(',')[1]
        try:
            decoded_data = base64.b64decode(base64_data)
            # 验证是否为有效的PNG图片
            img = Image.open(io.BytesIO(decoded_data))
            self.assertEqual(img.format, 'PNG')
        except Exception as e:
            self.fail(f"二维码数据解码失败: {str(e)}")
        
        # 验证数据库中保存了二维码数据
        self.assertEqual(self.course.qr_code, qr_code_data)
    
    def test_generate_qr_code_without_access_code(self):
        """测试没有访问码时生成二维码"""
        # 创建没有访问码的课程
        course_without_code = Course(
            name='Course Without Code',
            description='Test course without access code',
            teacher_id=self.teacher.id
        )
        db.session.add(course_without_code)
        db.session.commit()
        
        # 尝试生成二维码应该抛出异常
        with self.assertRaises(ValueError) as context:
            course_without_code.generate_qr_code()
        
        self.assertIn('课程访问码不存在', str(context.exception))
    
    def test_qr_code_content_structure(self):
        """测试二维码内容结构"""
        base_url = "http://test.example.com"
        qr_code_data = self.course.generate_qr_code(base_url=base_url)
        
        # 解码二维码图片并读取内容
        base64_data = qr_code_data.split(',')[1]
        decoded_data = base64.b64decode(base64_data)
        img = Image.open(io.BytesIO(decoded_data))
        
        # 使用qrcode库解码二维码内容
        # 注意：这里我们主要验证数据结构，实际解码需要专门的库
        # 我们通过验证生成的数据结构来确保正确性
        expected_data = {
            "course_id": self.course.id,
            "access_code": self.course.access_code,
            "join_url": f"{base_url}/course/join/{self.course.access_code}"
        }
        
        # 验证课程对象包含正确的信息
        self.assertEqual(self.course.id, expected_data["course_id"])
        self.assertEqual(self.course.access_code, expected_data["access_code"])
    
    def test_generate_qr_api_success(self):
        """测试成功生成二维码API"""
        # 教师登录
        self.login('<EMAIL>', 'password')
        
        # 生成二维码请求数据
        qr_request_data = {
            'course_id': self.course.id,
            'base_url': 'http://test.example.com'
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.generate_qr'),
            data=json.dumps(qr_request_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        
        self.assertIn('qr_code', data)
        self.assertIn('access_code', data)
        self.assertIn('course_id', data)
        self.assertIn('course_name', data)
        self.assertIn('message', data)
        
        self.assertEqual(data['access_code'], self.course.access_code)
        self.assertEqual(data['course_id'], self.course.id)
        self.assertEqual(data['course_name'], self.course.name)
        self.assertTrue(data['qr_code'].startswith('data:image/png;base64,'))
        self.assertEqual(data['message'], '二维码生成成功')
    
    def test_generate_qr_api_unauthorized(self):
        """测试未授权用户生成二维码API"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 生成二维码请求数据
        qr_request_data = {
            'course_id': self.course.id
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.generate_qr'),
            data=json.dumps(qr_request_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 403)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '只有教师可以生成二维码')
    
    def test_generate_qr_api_invalid_course(self):
        """测试为不存在的课程生成二维码API"""
        # 教师登录
        self.login('<EMAIL>', 'password')
        
        # 生成二维码请求数据（使用不存在的课程ID）
        qr_request_data = {
            'course_id': 99999
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.generate_qr'),
            data=json.dumps(qr_request_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 404)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '课程不存在')
    
    def test_generate_qr_api_wrong_teacher(self):
        """测试教师为其他教师的课程生成二维码API"""
        # 创建另一个教师
        other_teacher = User(username='other_teacher', email='<EMAIL>', 
                            password='password', role='teacher')
        db.session.add(other_teacher)
        db.session.commit()
        
        # 其他教师登录
        self.login('<EMAIL>', 'password')
        
        # 尝试为原教师的课程生成二维码
        qr_request_data = {
            'course_id': self.course.id
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.generate_qr'),
            data=json.dumps(qr_request_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 403)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '您没有权限为此课程生成二维码')
    
    def test_generate_qr_api_missing_data(self):
        """测试缺少必要数据的二维码生成API"""
        # 教师登录
        self.login('<EMAIL>', 'password')
        
        # 发送空数据
        response = self.client.post(
            url_for('course.generate_qr'),
            data=json.dumps({}),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '课程ID不能为空')
        
        # 发送无效的请求格式
        response = self.client.post(
            url_for('course.generate_qr'),
            data='invalid json',
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '无效的请求数据')
    
    def test_join_by_qr_api_success(self):
        """测试成功通过二维码加入课程API"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 构建二维码数据
        qr_data = {
            "course_id": self.course.id,
            "access_code": self.course.access_code,
            "join_url": f"http://test.example.com/course/join/{self.course.access_code}"
        }
        
        # 加入课程请求数据
        join_request_data = {
            'qr_data': json.dumps(qr_data)
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.join_by_qr_api'),
            data=json.dumps(join_request_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        
        self.assertIn('message', data)
        self.assertIn('course', data)
        self.assertIn(f'成功加入课程: {self.course.name}', data['message'])
        self.assertEqual(data['course']['id'], self.course.id)
        self.assertEqual(data['course']['name'], self.course.name)
        
        # 验证数据库中创建了课程学生关联
        enrollment = CourseStudent.query.filter_by(
            course_id=self.course.id,
            student_id=self.student.id
        ).first()
        self.assertIsNotNone(enrollment)
    
    def test_join_by_qr_api_already_enrolled(self):
        """测试已经加入课程的学生再次通过二维码加入"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 先让学生加入课程
        enrollment = CourseStudent(
            course_id=self.course.id,
            student_id=self.student.id
        )
        db.session.add(enrollment)
        db.session.commit()
        
        # 构建二维码数据
        qr_data = {
            "course_id": self.course.id,
            "access_code": self.course.access_code,
            "join_url": f"http://test.example.com/course/join/{self.course.access_code}"
        }
        
        # 加入课程请求数据
        join_request_data = {
            'qr_data': json.dumps(qr_data)
        }
        
        # 发送API请求
        response = self.client.post(
            url_for('course.join_by_qr_api'),
            data=json.dumps(join_request_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        
        self.assertIn('message', data)
        self.assertEqual(data['message'], '您已经加入了此课程')
    
    def test_join_by_qr_api_invalid_qr_data(self):
        """测试无效二维码数据的加入课程API"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 测试无效的JSON数据
        join_request_data = {
            'qr_data': 'invalid json data'
        }
        
        response = self.client.post(
            url_for('course.join_by_qr_api'),
            data=json.dumps(join_request_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '二维码数据格式无效')
        
        # 测试缺少必要字段的数据
        invalid_qr_data = {
            "course_id": self.course.id
            # 缺少access_code
        }
        
        join_request_data = {
            'qr_data': json.dumps(invalid_qr_data)
        }
        
        response = self.client.post(
            url_for('course.join_by_qr_api'),
            data=json.dumps(join_request_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '二维码数据格式无效')
    
    def test_join_by_qr_api_nonexistent_course(self):
        """测试通过二维码加入不存在的课程"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 构建不存在课程的二维码数据
        qr_data = {
            "course_id": 99999,
            "access_code": "999999",
            "join_url": "http://test.example.com/course/join/999999"
        }
        
        join_request_data = {
            'qr_data': json.dumps(qr_data)
        }
        
        response = self.client.post(
            url_for('course.join_by_qr_api'),
            data=json.dumps(join_request_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 404)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '课程不存在或访问码无效')
    
    def test_join_by_qr_api_wrong_access_code(self):
        """测试通过错误访问码的二维码加入课程"""
        # 学生登录
        self.login('<EMAIL>', 'password')
        
        # 构建错误访问码的二维码数据
        qr_data = {
            "course_id": self.course.id,
            "access_code": "wrong_code",
            "join_url": "http://test.example.com/course/join/wrong_code"
        }
        
        join_request_data = {
            'qr_data': json.dumps(qr_data)
        }
        
        response = self.client.post(
            url_for('course.join_by_qr_api'),
            data=json.dumps(join_request_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 404)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '课程不存在或访问码无效')

if __name__ == '__main__':
    unittest.main()