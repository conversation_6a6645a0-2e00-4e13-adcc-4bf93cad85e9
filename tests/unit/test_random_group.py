import unittest
import sys
import os
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from app.utils.group_utils import random_group_equal, random_group_random, random_group_by_attribute

class RandomGroupTestCase(unittest.TestCase):
    def setUp(self):
        """测试前的设置"""
        # 创建测试学生ID列表
        self.student_ids = list(range(1, 21))  # 20个学生
        
        # 创建带属性的学生数据
        self.student_data = []
        for i in range(1, 21):
            gender = 'male' if i % 3 == 0 else 'female'
            grade = 'A' if i % 5 == 0 else ('B' if i % 5 == 1 else ('C' if i % 5 == 2 else 'D'))
            self.student_data.append({
                'id': i,
                'gender': gender,
                'grade': grade
            })
    
    def test_random_group_equal(self):
        """测试均等分配随机分组算法"""
        # 测试5个小组
        group_count = 5
        result = random_group_equal(self.student_ids, group_count)
        
        # 验证分组数量
        self.assertEqual(len(result), group_count)
        
        # 验证所有学生都被分配
        all_students = []
        for group_idx, students in result.items():
            all_students.extend(students)
        self.assertEqual(sorted(all_students), sorted(self.student_ids))
        
        # 验证分组均衡性
        group_sizes = [len(students) for students in result.values()]
        self.assertTrue(max(group_sizes) - min(group_sizes) <= 1)
        
        # 测试边界情况：学生数量小于分组数量
        result = random_group_equal(self.student_ids[:3], 5)
        self.assertEqual(len(result), 3)
        
        # 测试边界情况：分组数量为1
        result = random_group_equal(self.student_ids, 1)
        self.assertEqual(len(result), 1)
        self.assertEqual(len(result[1]), len(self.student_ids))
        
        # 测试边界情况：空学生列表
        result = random_group_equal([], 5)
        self.assertEqual(len(result), 0)
    
    def test_random_group_random(self):
        """测试完全随机分组算法"""
        # 测试5个小组
        group_count = 5
        result = random_group_random(self.student_ids, group_count)
        
        # 验证分组数量
        self.assertEqual(len(result), group_count)
        
        # 验证所有学生都被分配
        all_students = []
        for group_idx, students in result.items():
            all_students.extend(students)
        self.assertEqual(sorted(all_students), sorted(self.student_ids))
        
        # 验证分组均衡性（完全随机算法也应该保持基本均衡）
        group_sizes = [len(students) for students in result.values()]
        self.assertTrue(max(group_sizes) - min(group_sizes) <= 1)
        
        # 测试边界情况：学生数量小于分组数量
        result = random_group_random(self.student_ids[:3], 5)
        self.assertEqual(len(result), 3)
        
        # 测试边界情况：分组数量为1
        result = random_group_random(self.student_ids, 1)
        self.assertEqual(len(result), 1)
        self.assertEqual(len(result[1]), len(self.student_ids))
        
        # 测试边界情况：空学生列表
        result = random_group_random([], 5)
        self.assertEqual(len(result), 0)
    
    def test_random_group_by_attribute_balanced(self):
        """测试按属性分组算法（平衡模式）"""
        # 测试按性别分组，平衡模式
        group_count = 4
        result = random_group_by_attribute(self.student_data, group_count, 'gender', balanced=True)
        
        # 验证分组数量
        self.assertEqual(len(result), group_count)
        
        # 验证所有学生都被分配
        all_students = []
        for group_idx, students in result.items():
            all_students.extend(students)
        self.assertEqual(sorted(all_students), sorted([s['id'] for s in self.student_data]))
        
        # 验证分组均衡性
        group_sizes = [len(students) for students in result.values()]
        self.assertTrue(max(group_sizes) - min(group_sizes) <= 1)
        
        # 验证性别分布均衡性
        male_counts = []
        female_counts = []
        for group_idx, students in result.items():
            male_count = sum(1 for s in self.student_data if s['id'] in students and s['gender'] == 'male')
            female_count = sum(1 for s in self.student_data if s['id'] in students and s['gender'] == 'female')
            male_counts.append(male_count)
            female_counts.append(female_count)
        
        # 验证每个小组的男女生数量差异不大
        self.assertTrue(max(male_counts) - min(male_counts) <= 2)
        self.assertTrue(max(female_counts) - min(female_counts) <= 2)
    
    def test_random_group_by_attribute_unbalanced(self):
        """测试按属性分组算法（非平衡模式）"""
        # 测试按成绩分组，非平衡模式
        group_count = 4
        result = random_group_by_attribute(self.student_data, group_count, 'grade', balanced=False)
        
        # 验证分组数量
        self.assertEqual(len(result), group_count)
        
        # 验证所有学生都被分配
        all_students = []
        for group_idx, students in result.items():
            all_students.extend(students)
        self.assertEqual(sorted(all_students), sorted([s['id'] for s in self.student_data]))
        
        # 验证分组均衡性（总人数应该大致平均）
        group_sizes = [len(students) for students in result.values()]
        self.assertTrue(max(group_sizes) - min(group_sizes) <= 2)

if __name__ == '__main__':
    unittest.main()