import unittest
import json
from app import create_app, db
from app.models.user import User
from app.models.resource import Question, Exam, ExamQuestion
from flask_login import current_user

class ExamCreationTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户
        user = User(username='testuser', email='<EMAIL>', password='password')
        db.session.add(user)
        db.session.commit()
        
        # 创建测试题目
        self.create_test_questions()
        
        # 登录
        response = self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def create_test_questions(self):
        """创建测试题目"""
        # 单选题
        q1 = Question(
            type='single',
            content=json.dumps({"ops": [{"insert": "这是一道单选题\n"}]}),
            options=["选项A", "选项B", "选项C", "选项D"],
            answer=1,
            score=2.0,
            created_by=1
        )
        
        # 多选题
        q2 = Question(
            type='multiple',
            content=json.dumps({"ops": [{"insert": "这是一道多选题\n"}]}),
            options=["选项A", "选项B", "选项C", "选项D"],
            answer=[0, 2],
            score=3.0,
            created_by=1
        )
        
        # 判断题
        q3 = Question(
            type='truefalse',
            content=json.dumps({"ops": [{"insert": "这是一道判断题\n"}]}),
            answer=True,
            score=1.0,
            created_by=1
        )
        
        db.session.add_all([q1, q2, q3])
        db.session.commit()
    
    def test_exam_list_page(self):
        """测试试卷列表页面"""
        response = self.client.get('/interaction/exams')
        self.assertEqual(response.status_code, 200)
        self.assertIn('试卷管理', response.data.decode('utf-8'))
    
    def test_create_exam_page(self):
        """测试创建试卷页面"""
        response = self.client.get('/interaction/exams/create')
        self.assertEqual(response.status_code, 200)
        self.assertIn('创建试卷', response.data.decode('utf-8'))
    
    def test_api_questions(self):
        """测试获取题目列表API"""
        response = self.client.get('/interaction/api/questions')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(len(data['questions']), 3)  # 应该有3个测试题目
    
    def test_create_exam(self):
        """测试创建试卷"""
        # 准备试卷数据
        exam_data = {
            'name': '测试试卷',
            'time_limit': 60,
            'total_score': 6.0,
            'questions': [
                {'id': 1, 'order': 0},
                {'id': 2, 'order': 1},
                {'id': 3, 'order': 2}
            ]
        }
        
        # 发送创建试卷请求
        response = self.client.post('/interaction/exams/create', 
                                   data=json.dumps(exam_data),
                                   content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # 验证数据库中是否创建了试卷
        exam = Exam.query.filter_by(name='测试试卷').first()
        self.assertIsNotNone(exam)
        self.assertEqual(exam.time_limit, 60)
        self.assertEqual(exam.total_score, 6.0)
        
        # 验证试卷题目关联
        exam_questions = ExamQuestion.query.filter_by(exam_id=exam.id).order_by(ExamQuestion.order).all()
        self.assertEqual(len(exam_questions), 3)
        self.assertEqual(exam_questions[0].question_id, 1)
        self.assertEqual(exam_questions[1].question_id, 2)
        self.assertEqual(exam_questions[2].question_id, 3)
    
    def test_view_exam(self):
        """测试查看试卷"""
        # 先创建一个试卷
        exam = Exam(
            name='测试试卷',
            time_limit=60,
            total_score=6.0,
            created_by=1
        )
        db.session.add(exam)
        db.session.flush()
        
        # 添加试卷题目关联
        eq1 = ExamQuestion(exam_id=exam.id, question_id=1, order=0)
        eq2 = ExamQuestion(exam_id=exam.id, question_id=2, order=1)
        db.session.add_all([eq1, eq2])
        db.session.commit()
        
        # 查看试卷
        response = self.client.get(f'/interaction/exams/{exam.id}')
        self.assertEqual(response.status_code, 200)
        self.assertIn('测试试卷', response.data.decode('utf-8'))
    
    def test_edit_exam(self):
        """测试编辑试卷"""
        # 先创建一个试卷
        exam = Exam(
            name='原始试卷',
            time_limit=60,
            total_score=6.0,
            created_by=1
        )
        db.session.add(exam)
        db.session.flush()
        
        # 添加试卷题目关联
        eq1 = ExamQuestion(exam_id=exam.id, question_id=1, order=0)
        eq2 = ExamQuestion(exam_id=exam.id, question_id=2, order=1)
        db.session.add_all([eq1, eq2])
        db.session.commit()
        
        # 准备更新数据
        update_data = {
            'name': '更新后的试卷',
            'time_limit': 90,
            'total_score': 4.0,
            'questions': [
                {'id': 3, 'order': 0},
                {'id': 1, 'order': 1}
            ]
        }
        
        # 发送编辑请求
        response = self.client.post(f'/interaction/exams/{exam.id}/edit',
                                   data=json.dumps(update_data),
                                   content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # 验证数据库中的试卷是否已更新
        updated_exam = Exam.query.get(exam.id)
        self.assertEqual(updated_exam.name, '更新后的试卷')
        self.assertEqual(updated_exam.time_limit, 90)
        self.assertEqual(updated_exam.total_score, 4.0)
        
        # 验证试卷题目关联是否已更新
        exam_questions = ExamQuestion.query.filter_by(exam_id=exam.id).order_by(ExamQuestion.order).all()
        self.assertEqual(len(exam_questions), 2)
        self.assertEqual(exam_questions[0].question_id, 3)
        self.assertEqual(exam_questions[1].question_id, 1)
    
    def test_delete_exam(self):
        """测试删除试卷"""
        # 先创建一个试卷
        exam = Exam(
            name='要删除的试卷',
            time_limit=60,
            total_score=6.0,
            created_by=1
        )
        db.session.add(exam)
        db.session.flush()
        
        # 添加试卷题目关联
        eq1 = ExamQuestion(exam_id=exam.id, question_id=1, order=0)
        db.session.add(eq1)
        db.session.commit()
        
        exam_id = exam.id
        
        # 删除试卷
        response = self.client.get(f'/interaction/exams/{exam_id}/delete', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn('试卷已删除', response.data.decode('utf-8'))
        
        # 验证数据库中的试卷是否已删除
        deleted_exam = Exam.query.get(exam_id)
        self.assertIsNone(deleted_exam)
        
        # 验证试卷题目关联是否已删除
        exam_questions = ExamQuestion.query.filter_by(exam_id=exam_id).all()
        self.assertEqual(len(exam_questions), 0)

if __name__ == '__main__':
    unittest.main()