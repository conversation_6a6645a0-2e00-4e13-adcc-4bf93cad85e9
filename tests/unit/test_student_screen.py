"""
学生投屏功能单元测试
"""
import unittest
import sys
import os
from unittest.mock import patch, MagicMock
from flask import url_for

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app import create_app
from app.models.user import User
from app.models.course import Course

class StudentScreenTestCase(unittest.TestCase):
    """学生投屏功能测试用例"""
    
    def setUp(self):
        """测试前设置"""
        self.app = create_app('testing')
        self.app.config['LOGIN_DISABLED'] = True  # 禁用登录要求
        self.app.config['TESTING'] = True
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
    
    def tearDown(self):
        """测试后清理"""
        self.app_context.pop()
    
    @patch('flask_login.utils._get_user')
    @patch('app.controllers.broadcast.Course')
    def test_student_screen_page_access(self, mock_course, mock_get_user):
        """测试学生访问投屏页面"""
        # 模拟当前用户为学生
        mock_student = MagicMock()
        mock_student.is_teacher.return_value = False
        mock_student.id = 2
        mock_get_user.return_value = mock_student
        
        # 模拟课程
        mock_course_instance = MagicMock()
        mock_course_instance.id = 1
        mock_course_instance.name = "Test Course"
        mock_course.query.filter_by.return_value.first_or_404.return_value = mock_course_instance
        
        # 模拟学生已加入课程
        mock_course_instance.has_student.return_value = True
        
        # 访问投屏页面
        with self.client as c:
            response = c.get('/broadcast/student/screen/1')
            self.assertEqual(response.status_code, 200)
    
    @patch('flask_login.utils._get_user')
    @patch('app.controllers.broadcast.Course')
    def test_unauthorized_student_access(self, mock_course, mock_get_user):
        """测试未授权学生访问投屏页面"""
        # 模拟当前用户为学生
        mock_student = MagicMock()
        mock_student.is_teacher.return_value = False
        mock_student.id = 3
        mock_get_user.return_value = mock_student
        
        # 模拟课程
        mock_course_instance = MagicMock()
        mock_course_instance.id = 1
        mock_course_instance.name = "Test Course"
        mock_course.query.filter_by.return_value.first_or_404.return_value = mock_course_instance
        
        # 模拟学生未加入课程
        mock_course_instance.has_student.return_value = False
        
        # 访问投屏页面
        with self.client as c:
            response = c.get('/broadcast/student/screen/1')
            self.assertEqual(response.status_code, 403)
    
    @patch('flask_login.utils._get_user')
    @patch('app.controllers.broadcast.Course')
    def test_teacher_access_student_screen(self, mock_course, mock_get_user):
        """测试教师访问学生投屏页面"""
        # 模拟当前用户为教师
        mock_teacher = MagicMock()
        mock_teacher.is_teacher.return_value = True
        mock_teacher.id = 1
        mock_get_user.return_value = mock_teacher
        
        # 模拟课程
        mock_course_instance = MagicMock()
        mock_course_instance.id = 1
        mock_course_instance.name = "Test Course"
        mock_course.query.filter_by.return_value.first_or_404.return_value = mock_course_instance
        
        # 访问投屏页面
        with self.client as c:
            response = c.get('/broadcast/student/screen/1')
            self.assertEqual(response.status_code, 200)

if __name__ == '__main__':
    unittest.main()