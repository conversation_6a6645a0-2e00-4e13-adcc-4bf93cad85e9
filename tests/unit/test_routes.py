import unittest
from flask import url_for
from app import create_app, db
from app.models.user import User

class RoutesTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_home_page(self):
        """测试首页"""
        response = self.client.get(url_for('main.index'))
        self.assertEqual(response.status_code, 200)
        self.assertIn('智慧教室系统'.encode('utf-8'), response.data)
    
    def test_404_page(self):
        """测试404页面"""
        response = self.client.get('/nonexistent-page')
        self.assertEqual(response.status_code, 404)
        self.assertIn(b'404', response.data)
    
    def test_login_page(self):
        """测试登录页面"""
        response = self.client.get(url_for('auth.login'))
        self.assertEqual(response.status_code, 200)
        self.assertIn('登录'.encode('utf-8'), response.data)
    
    def test_register_page(self):
        """测试注册页面"""
        response = self.client.get(url_for('auth.register'))
        self.assertEqual(response.status_code, 200)
        self.assertIn('注册'.encode('utf-8'), response.data)
    
    def test_user_registration(self):
        """测试用户注册"""
        response = self.client.post(url_for('auth.register'), data={
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'password123',
            'password2': 'password123',
            'role': 'student'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn('注册成功'.encode('utf-8'), response.data)
        
        # 验证用户已创建
        user = User.query.filter_by(email='<EMAIL>').first()
        self.assertIsNotNone(user)
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.role, 'student')
    
    def test_user_login(self):
        """测试用户登录"""
        # 先创建用户
        user = User(username='testuser', email='<EMAIL>', password='password123', role='student')
        db.session.add(user)
        db.session.commit()
        
        # 测试登录
        response = self.client.post(url_for('auth.login'), data={
            'email': '<EMAIL>',
            'password': 'password123'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn('欢迎回来'.encode('utf-8'), response.data)
    
    def test_user_logout(self):
        """测试用户登出"""
        # 先创建并登录用户
        user = User(username='testuser', email='<EMAIL>', password='password123', role='student')
        db.session.add(user)
        db.session.commit()
        
        self.client.post(url_for('auth.login'), data={
            'email': '<EMAIL>',
            'password': 'password123'
        })
        
        # 测试登出
        response = self.client.get(url_for('auth.logout'), follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn('再见'.encode('utf-8'), response.data)
    
    def test_invalid_login(self):
        """测试无效登录"""
        response = self.client.post(url_for('auth.login'), data={
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        })
        self.assertEqual(response.status_code, 200)
        self.assertIn('邮箱或密码错误'.encode('utf-8'), response.data)
    
    def test_duplicate_registration(self):
        """测试重复注册"""
        # 先创建用户
        user = User(username='testuser', email='<EMAIL>', password='password123', role='student')
        db.session.add(user)
        db.session.commit()
        
        # 尝试用相同邮箱注册
        response = self.client.post(url_for('auth.register'), data={
            'username': 'testuser2',
            'email': '<EMAIL>',
            'password': 'password123',
            'password2': 'password123',
            'role': 'student'
        })
        self.assertEqual(response.status_code, 200)
        self.assertIn('邮箱已被注册'.encode('utf-8'), response.data)

if __name__ == '__main__':
    unittest.main()