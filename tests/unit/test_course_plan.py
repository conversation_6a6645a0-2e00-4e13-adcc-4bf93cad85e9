import unittest
import json
from datetime import datetime, timedelta
from app import create_app, db
from app.models.user import User
from app.models.course import Course
from app.models.resource import Resource, Exam, CoursePlan
from flask_login import current_user

class CoursePlanTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户
        user = User(username='testuser', email='<EMAIL>', password='password', role='teacher')
        db.session.add(user)
        db.session.commit()
        
        # 创建测试课程
        course = Course(
            name='测试课程',
            description='这是一个测试课程',
            teacher_id=1
        )
        db.session.add(course)
        
        # 创建测试资源
        resource = Resource(
            name='测试资源',
            type='document',
            url='test.pdf',
            owner_id=1,
            course_id=1
        )
        db.session.add(resource)
        
        # 创建测试试卷
        exam = Exam(
            name='测试试卷',
            total_score=100,
            time_limit=60,
            created_by=1
        )
        db.session.add(exam)
        db.session.commit()
        
        # 登录
        response = self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_course_plans_page(self):
        """测试课程安排页面"""
        response = self.client.get('/interaction/course_plans')
        self.assertEqual(response.status_code, 200)
        self.assertIn('课程安排', response.data.decode('utf-8'))
    
    def test_api_course_plans(self):
        """测试获取课程安排API"""
        # 先创建一个课程安排
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=2)
        
        plan = CoursePlan(
            title='测试课程安排',
            description='这是一个测试课程安排',
            start_time=start_time,
            end_time=end_time,
            location='测试地点',
            course_id=1,
            created_by=1,
            resources=[
                {'id': 1, 'type': 'resource'},
                {'id': 1, 'type': 'exam'}
            ]
        )
        db.session.add(plan)
        db.session.commit()
        
        # 获取课程安排列表
        response = self.client.get('/interaction/api/course_plans')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['title'], '测试课程安排')
    
    def test_api_course_plan_detail(self):
        """测试获取课程安排详情API"""
        # 先创建一个课程安排
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=2)
        
        plan = CoursePlan(
            title='测试课程安排',
            description='这是一个测试课程安排',
            start_time=start_time,
            end_time=end_time,
            location='测试地点',
            course_id=1,
            created_by=1,
            resources=[
                {'id': 1, 'type': 'resource'},
                {'id': 1, 'type': 'exam'}
            ]
        )
        db.session.add(plan)
        db.session.commit()
        
        # 获取课程安排详情
        response = self.client.get(f'/interaction/api/course_plans/{plan.id}')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['plan']['title'], '测试课程安排')
        self.assertEqual(len(data['plan']['resources']), 2)
    
    def test_create_course_plan(self):
        """测试创建课程安排"""
        # 准备课程安排数据
        start_time = datetime.now().isoformat()
        end_time = (datetime.now() + timedelta(hours=2)).isoformat()
        
        plan_data = {
            'title': '新课程安排',
            'description': '这是一个新的课程安排',
            'start_time': start_time,
            'end_time': end_time,
            'location': '新地点',
            'course_id': 1,
            'resources': [
                {'id': 1, 'type': 'resource'},
                {'id': 1, 'type': 'exam'}
            ]
        }
        
        # 发送创建请求
        response = self.client.post('/interaction/course_plans/create',
                                   data=json.dumps(plan_data),
                                   content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # 验证数据库中是否创建了课程安排
        plan = CoursePlan.query.filter_by(title='新课程安排').first()
        self.assertIsNotNone(plan)
        self.assertEqual(plan.description, '这是一个新的课程安排')
        self.assertEqual(plan.location, '新地点')
        self.assertEqual(len(plan.resources), 2)
    
    def test_edit_course_plan(self):
        """测试编辑课程安排"""
        # 先创建一个课程安排
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=2)
        
        plan = CoursePlan(
            title='原始课程安排',
            description='这是一个原始课程安排',
            start_time=start_time,
            end_time=end_time,
            location='原始地点',
            course_id=1,
            created_by=1,
            resources=[
                {'id': 1, 'type': 'resource'}
            ]
        )
        db.session.add(plan)
        db.session.commit()
        
        # 准备更新数据
        new_start_time = (datetime.now() + timedelta(days=1)).isoformat()
        new_end_time = (datetime.now() + timedelta(days=1, hours=2)).isoformat()
        
        update_data = {
            'title': '更新后的课程安排',
            'description': '这是更新后的课程安排',
            'start_time': new_start_time,
            'end_time': new_end_time,
            'location': '新地点',
            'course_id': 1,
            'resources': [
                {'id': 1, 'type': 'exam'}
            ]
        }
        
        # 发送更新请求
        response = self.client.post(f'/interaction/course_plans/{plan.id}/edit',
                                   data=json.dumps(update_data),
                                   content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # 验证数据库中的课程安排是否已更新
        updated_plan = CoursePlan.query.get(plan.id)
        self.assertEqual(updated_plan.title, '更新后的课程安排')
        self.assertEqual(updated_plan.description, '这是更新后的课程安排')
        self.assertEqual(updated_plan.location, '新地点')
        self.assertEqual(len(updated_plan.resources), 1)
        self.assertEqual(updated_plan.resources[0]['type'], 'exam')
    
    def test_delete_course_plan(self):
        """测试删除课程安排"""
        # 先创建一个课程安排
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=2)
        
        plan = CoursePlan(
            title='要删除的课程安排',
            description='这是一个要删除的课程安排',
            start_time=start_time,
            end_time=end_time,
            location='测试地点',
            course_id=1,
            created_by=1
        )
        db.session.add(plan)
        db.session.commit()
        
        plan_id = plan.id
        
        # 发送删除请求
        response = self.client.post(f'/interaction/course_plans/{plan_id}/delete')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # 验证数据库中的课程安排是否已删除
        deleted_plan = CoursePlan.query.get(plan_id)
        self.assertIsNone(deleted_plan)

if __name__ == '__main__':
    unittest.main()