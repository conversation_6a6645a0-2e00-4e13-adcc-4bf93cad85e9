import unittest
from app import create_app, db
from app.models.user import User
from app.models.course import Course
from app.models.activity import ClassActivity, ClassReport, ReportNote
from app.models.interaction import GroupDiscussion
from datetime import datetime, timedelta
import json

class ActivityRecordTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client()
        
        # 创建测试用户（教师）
        self.teacher = User(username='teacher', email='<EMAIL>', password='password', role='teacher')
        db.session.add(self.teacher)
        
        # 创建测试用户（学生）
        self.student = User(username='student', email='<EMAIL>', password='password', role='student')
        db.session.add(self.student)
        
        # 创建测试课程
        self.course = Course(name='测试课程', teacher_id=self.teacher.id)
        self.course.generate_access_code()
        db.session.add(self.course)
        
        db.session.commit()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def login(self, email, password):
        return self.client.post('/auth/login', data={
            'email': email,
            'password': password
        }, follow_redirects=True)
    
    def test_record_activity(self):
        """测试记录课堂活动"""
        # 登录教师账号
        self.login('<EMAIL>', 'password')
        
        # 创建活动记录
        activity = ClassActivity(
            course_id=self.course.id,
            type='test',
            content_id=1,
            title='测试活动',
            description='测试活动描述',
            created_by=self.teacher.id
        )
        db.session.add(activity)
        db.session.commit()
        
        # 验证活动记录是否创建成功
        saved_activity = ClassActivity.query.filter_by(title='测试活动').first()
        self.assertIsNotNone(saved_activity)
        self.assertEqual(saved_activity.course_id, self.course.id)
        self.assertEqual(saved_activity.type, 'test')
        self.assertEqual(saved_activity.content_id, 1)
        self.assertEqual(saved_activity.title, '测试活动')
        self.assertEqual(saved_activity.description, '测试活动描述')
        self.assertEqual(saved_activity.created_by, self.teacher.id)
    
    def test_view_activities(self):
        """测试查看课堂活动记录"""
        # 创建活动记录
        activity = ClassActivity(
            course_id=self.course.id,
            type='test',
            content_id=1,
            title='测试活动',
            description='测试活动描述',
            created_by=self.teacher.id
        )
        db.session.add(activity)
        db.session.commit()
        
        # 登录教师账号
        self.login('<EMAIL>', 'password')
        
        # 访问活动记录页面
        response = self.client.get(f'/report/activities/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'\xe6\xb5\x8b\xe8\xaf\x95\xe6\xb4\xbb\xe5\x8a\xa8', response.data)  # '测试活动' in UTF-8
    
    def test_generate_report(self):
        """测试生成课堂报告"""
        # 创建活动记录
        activity = ClassActivity(
            course_id=self.course.id,
            type='test',
            content_id=1,
            title='测试活动',
            description='测试活动描述',
            created_by=self.teacher.id
        )
        db.session.add(activity)
        db.session.commit()
        
        # 登录教师账号
        self.login('<EMAIL>', 'password')
        
        # 生成报告
        response = self.client.post(f'/report/generate-report/{self.course.id}', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 验证报告是否生成成功
        report = ClassReport.query.filter_by(course_id=self.course.id).first()
        self.assertIsNotNone(report)
        self.assertEqual(report.course_id, self.course.id)
        self.assertEqual(report.created_by, self.teacher.id)
    
    def test_add_report_note(self):
        """测试添加报告备注"""
        # 创建报告
        report = ClassReport(
            course_id=self.course.id,
            title='测试报告',
            created_by=self.teacher.id
        )
        db.session.add(report)
        db.session.commit()
        
        # 登录教师账号
        self.login('<EMAIL>', 'password')
        
        # 添加备注
        response = self.client.post(f'/report/add-note/{report.id}', data={
            'content': '测试备注内容'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 验证备注是否添加成功
        note = ReportNote.query.filter_by(report_id=report.id).first()
        self.assertIsNotNone(note)
        self.assertEqual(note.content, '测试备注内容')
        self.assertEqual(note.created_by, self.teacher.id)
    
    def test_discussion_creates_activity(self):
        """测试创建讨论时自动记录活动"""
        # 登录教师账号
        self.login('<EMAIL>', 'password')
        
        # 创建讨论
        end_time = datetime.utcnow() + timedelta(minutes=10)
        response = self.client.post('/interaction/group-discussion', data={
            'course_id': self.course.id,
            'title': '测试讨论',
            'description': '测试讨论描述',
            'duration': 10,
            'topic_type': 'text',
            'topic_content': '测试讨论内容'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 验证讨论是否创建成功
        discussion = GroupDiscussion.query.filter_by(title='测试讨论').first()
        self.assertIsNotNone(discussion)
        
        # 验证活动记录是否创建成功
        activity = ClassActivity.query.filter_by(
            course_id=self.course.id,
            type='discussion',
            content_id=discussion.id
        ).first()
        self.assertIsNotNone(activity)
        self.assertEqual(activity.title, '分组讨论: 测试讨论')
        self.assertEqual(activity.description, '测试讨论描述')
        self.assertEqual(activity.created_by, self.teacher.id)

if __name__ == '__main__':
    unittest.main()