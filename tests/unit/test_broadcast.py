"""
屏幕广播功能单元测试
"""
import unittest
import sys
import os
from flask import url_for
from flask_login import login_user

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent

class BroadcastTestCase(unittest.TestCase):
    """屏幕广播功能测试用例"""
    
    def setUp(self):
        """测试前设置"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client()
        
        # 创建测试用户
        self.teacher = User(
            username='teacher',
            email='<EMAIL>',
            role='teacher',
            password='password'
        )
        
        self.student = User(
            username='student',
            email='<EMAIL>',
            role='student',
            password='password'
        )
        
        # 创建测试课程
        self.course = Course(
            name='Test Course',
            description='Test Course Description',
            teacher_id=1,
            access_code='123456'
        )
        
        db.session.add(self.teacher)
        db.session.add(self.student)
        db.session.add(self.course)
        db.session.commit()
        
        # 学生加入课程
        course_student = CourseStudent(
            course_id=self.course.id,
            student_id=self.student.id
        )
        db.session.add(course_student)
        db.session.commit()
    
    def tearDown(self):
        """测试后清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_teacher_broadcast_page(self):
        """测试教师广播页面"""
        # 登录教师
        with self.client.session_transaction() as session:
            session['_user_id'] = self.teacher.id
        
        # 访问教师广播页面
        response = self.client.get(f'/broadcast/teacher/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        self.assertIn('broadcast/teacher', response.request.path)
    
    def test_student_broadcast_page(self):
        """测试学生广播页面"""
        # 登录学生
        with self.client.session_transaction() as session:
            session['_user_id'] = self.student.id
        
        # 访问学生广播页面
        response = self.client.get(f'/broadcast/student/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        self.assertIn('broadcast/student', response.request.path)
    
    def test_unauthorized_access(self):
        """测试未授权访问"""
        # 创建另一个学生
        other_student = User(
            username='other_student',
            email='<EMAIL>',
            role='student',
            password='password'
        )
        db.session.add(other_student)
        db.session.commit()
        
        # 登录未加入课程的学生
        with self.client.session_transaction() as session:
            session['_user_id'] = other_student.id
        
        # 尝试访问学生广播页面
        response = self.client.get(f'/broadcast/student/{self.course.id}')
        self.assertEqual(response.status_code, 403)
        self.assertIn('403', response.get_data(as_text=True))
    
    def test_student_access_teacher_page(self):
        """测试学生访问教师广播页面"""
        # 登录学生
        with self.client.session_transaction() as session:
            session['_user_id'] = self.student.id
        
        # 尝试访问教师广播页面
        response = self.client.get(f'/broadcast/teacher/{self.course.id}')
        self.assertEqual(response.status_code, 403)
        self.assertIn('403', response.get_data(as_text=True))
    
    def test_broadcast_status_api(self):
        """测试广播状态API"""
        # 登录教师
        with self.client.session_transaction() as session:
            session['_user_id'] = self.teacher.id
        
        # 获取广播状态
        response = self.client.get(f'/broadcast/api/broadcast/status/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        
        # 验证返回的JSON数据
        data = response.get_json()
        self.assertIn('status', data)
        self.assertIn('message', data)
        self.assertIn('clients_count', data)

if __name__ == '__main__':
    unittest.main()