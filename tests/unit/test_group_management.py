import unittest
import json
import sys
import os
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent, Group, GroupMember
from flask_login import current_user
from datetime import datetime

class GroupManagementTestCase(unittest.TestCase):
    def setUp(self):
        """测试前的设置"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户（教师）
        self.teacher = User(
            username='测试教师',
            email='<EMAIL>',
            password='password',
            role='teacher'
        )
        db.session.add(self.teacher)
        
        # 创建测试用户（学生）
        self.students = []
        for i in range(10):
            student = User(
                username=f'学生{i+1}',
                email=f'student{i+1}@example.com',
                password='password',
                role='student'
            )
            self.students.append(student)
            db.session.add(student)
        
        db.session.commit()
        
        # 创建测试课程
        self.course = Course(
            name='测试课程',
            description='这是一个测试课程',
            teacher_id=self.teacher.id
        )
        self.course.generate_access_code()
        db.session.add(self.course)
        db.session.commit()
        
        # 添加学生到课程
        for student in self.students:
            cs = CourseStudent(
                course_id=self.course.id,
                student_id=student.id
            )
            db.session.add(cs)
        
        db.session.commit()
        
        # 登录教师账号
        self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
    
    def tearDown(self):
        """测试后的清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_group_management_page(self):
        """测试分组管理页面"""
        response = self.client.get(f'/group/manage/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        self.assertIn('分组管理'.encode(), response.data)  # "分组管理"的UTF-8编码
    
    def test_get_groups(self):
        """测试获取分组信息API"""
        response = self.client.get(f'/group/api/groups/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('groups', data)
        self.assertIn('ungrouped_students', data)
        self.assertEqual(len(data['ungrouped_students']), 10)  # 所有学生都未分组
    
    def test_create_group(self):
        """测试创建分组API"""
        response = self.client.post(
            f'/group/api/groups/{self.course.id}',
            json={'name': '测试小组1'}
        )
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['name'], '测试小组1')
        
        # 验证数据库中是否创建了分组
        group = Group.query.filter_by(name='测试小组1').first()
        self.assertIsNotNone(group)
        self.assertEqual(group.course_id, self.course.id)
    
    def test_update_group(self):
        """测试更新分组API"""
        # 先创建一个分组
        group = Group(
            name='原始小组名',
            course_id=self.course.id
        )
        db.session.add(group)
        db.session.commit()
        
        # 更新分组名称
        response = self.client.put(
            f'/group/api/groups/{group.id}',
            json={'name': '更新后的小组名'}
        )
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['name'], '更新后的小组名')
        
        # 验证数据库中是否更新了分组名称
        updated_group = Group.query.get(group.id)
        self.assertEqual(updated_group.name, '更新后的小组名')
    
    def test_delete_group(self):
        """测试删除分组API"""
        # 先创建一个分组
        group = Group(
            name='待删除小组',
            course_id=self.course.id
        )
        db.session.add(group)
        db.session.commit()
        
        # 添加学生到分组
        member = GroupMember(
            group_id=group.id,
            student_id=self.students[0].id
        )
        db.session.add(member)
        db.session.commit()
        
        # 删除分组
        response = self.client.delete(f'/group/api/groups/{group.id}')
        self.assertEqual(response.status_code, 200)
        
        # 验证数据库中是否删除了分组
        deleted_group = Group.query.get(group.id)
        self.assertIsNone(deleted_group)
        
        # 验证分组成员是否也被删除
        deleted_member = GroupMember.query.filter_by(group_id=group.id).first()
        self.assertIsNone(deleted_member)
    
    def test_add_group_member(self):
        """测试添加分组成员API"""
        # 先创建一个分组
        group = Group(
            name='测试小组',
            course_id=self.course.id
        )
        db.session.add(group)
        db.session.commit()
        
        # 添加学生到分组
        response = self.client.post(
            f'/group/api/groups/{group.id}/members',
            json={'student_id': self.students[0].id}
        )
        self.assertEqual(response.status_code, 201)
        
        # 验证数据库中是否添加了分组成员
        member = GroupMember.query.filter_by(
            group_id=group.id,
            student_id=self.students[0].id
        ).first()
        self.assertIsNotNone(member)
    
    def test_remove_group_member(self):
        """测试移除分组成员API"""
        # 先创建一个分组
        group = Group(
            name='测试小组',
            course_id=self.course.id
        )
        db.session.add(group)
        db.session.commit()
        
        # 添加学生到分组
        member = GroupMember(
            group_id=group.id,
            student_id=self.students[0].id
        )
        db.session.add(member)
        db.session.commit()
        
        # 移除分组成员
        response = self.client.delete(f'/group/api/groups/{group.id}/members/{self.students[0].id}')
        self.assertEqual(response.status_code, 200)
        
        # 验证数据库中是否移除了分组成员
        deleted_member = GroupMember.query.filter_by(
            group_id=group.id,
            student_id=self.students[0].id
        ).first()
        self.assertIsNone(deleted_member)
    
    def test_random_group_equal(self):
        """测试均等分配随机分组API"""
        # 执行随机分组
        response = self.client.post(
            f'/group/api/random/{self.course.id}',
            json={'group_count': 3, 'algorithm': 'equal'}
        )
        self.assertEqual(response.status_code, 200)
        
        # 验证是否创建了3个分组
        groups = Group.query.filter_by(course_id=self.course.id).all()
        self.assertEqual(len(groups), 3)
        
        # 验证所有学生是否都被分组
        group_members = GroupMember.query.join(
            Group, Group.id == GroupMember.group_id
        ).filter(Group.course_id == self.course.id).all()
        self.assertEqual(len(group_members), 10)  # 总共10个学生
        
        # 验证分组是否均衡
        group_counts = {}
        for member in group_members:
            group_counts[member.group_id] = group_counts.get(member.group_id, 0) + 1
        
        # 检查每个分组的学生数量是否接近平均值
        for count in group_counts.values():
            self.assertTrue(3 <= count <= 4)  # 10个学生分3组，每组应有3-4个学生
    
    def test_random_group_random(self):
        """测试完全随机分组API"""
        # 执行随机分组
        response = self.client.post(
            f'/group/api/random/{self.course.id}',
            json={'group_count': 5, 'algorithm': 'random'}
        )
        self.assertEqual(response.status_code, 200)
        
        # 验证是否创建了5个分组
        groups = Group.query.filter_by(course_id=self.course.id).all()
        self.assertEqual(len(groups), 5)
        
        # 验证所有学生是否都被分组
        group_members = GroupMember.query.join(
            Group, Group.id == GroupMember.group_id
        ).filter(Group.course_id == self.course.id).all()
        self.assertEqual(len(group_members), 10)  # 总共10个学生
        
        # 验证每个分组是否有学生
        group_counts = {}
        for member in group_members:
            group_counts[member.group_id] = group_counts.get(member.group_id, 0) + 1
        
        self.assertEqual(len(group_counts), 5)  # 应该有5个分组都有学生

if __name__ == '__main__':
    unittest.main()

class DeviceMonitorTestCase(unittest.TestCase):
    def setUp(self):
        """测试前的设置"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户（教师）
        self.teacher = User(
            username='测试教师',
            email='<EMAIL>',
            password='password',
            role='teacher'
        )
        db.session.add(self.teacher)
        
        # 创建测试用户（学生）
        self.students = []
        for i in range(3):
            student = User(
                username=f'学生{i+1}',
                email=f'student{i+1}@example.com',
                password='password',
                role='student'
            )
            self.students.append(student)
            db.session.add(student)
        
        db.session.commit()
        
        # 创建测试课程
        self.course = Course(
            name='测试课程',
            description='这是一个测试课程',
            teacher_id=self.teacher.id
        )
        self.course.generate_access_code()
        db.session.add(self.course)
        db.session.commit()
        
        # 添加学生到课程
        for student in self.students:
            cs = CourseStudent(
                course_id=self.course.id,
                student_id=student.id
            )
            db.session.add(cs)
        
        # 创建测试小组
        self.group = Group(
            name='测试小组',
            course_id=self.course.id,
            device_id='group-device-001'
        )
        db.session.add(self.group)
        db.session.commit()
        
        # 添加学生到小组
        group_member = GroupMember(
            group_id=self.group.id,
            student_id=self.students[0].id
        )
        db.session.add(group_member)
        db.session.commit()
        
        # 创建设备状态记录
        from app.models.device import DeviceStatus
        
        # 教师设备
        teacher_device = DeviceStatus(
            device_id='teacher-device-001',
            type='teacher',
            owner_id=self.teacher.id,
            status='online',
            ip_address='*************'
        )
        db.session.add(teacher_device)
        
        # 小组设备
        group_device = DeviceStatus(
            device_id='group-device-001',
            type='group',
            owner_id=self.group.id,
            status='online',
            ip_address='*************'
        )
        db.session.add(group_device)
        
        # 学生设备
        student_device = DeviceStatus(
            device_id='student-device-001',
            type='student',
            owner_id=self.students[1].id,
            status='offline',
            ip_address='*************'
        )
        db.session.add(student_device)
        
        db.session.commit()
        
        # 登录教师账号
        self.client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        }, follow_redirects=True)
    
    def tearDown(self):
        """测试后的清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_device_monitor_page(self):
        """测试设备状态监控页面"""
        response = self.client.get(f'/group/monitor/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        self.assertIn('设备状态监控'.encode(), response.data)
    
    def test_get_devices_api(self):
        """测试获取设备状态API"""
        response = self.client.get(f'/group/api/devices/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('devices', data)
        
        devices = data['devices']
        self.assertEqual(len(devices), 3)  # 教师设备、小组设备和学生设备
        
        # 验证设备类型
        device_roles = [d['role'] for d in devices]
        self.assertIn('teacher', device_roles)
        self.assertIn('group', device_roles)
        self.assertIn('student', device_roles)
        
        # 验证设备状态
        online_devices = [d for d in devices if d['status'] == 'online']
        offline_devices = [d for d in devices if d['status'] == 'offline']
        self.assertEqual(len(online_devices), 2)  # 教师设备和小组设备在线
        self.assertEqual(len(offline_devices), 1)  # 学生设备离线

if __name__ == '__main__':
    unittest.main()