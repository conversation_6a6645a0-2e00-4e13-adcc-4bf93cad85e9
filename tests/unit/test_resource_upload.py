import os
import sys
import pytest
import io
from flask import url_for

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app import create_app, db
from app.models.resource import Resource
from app.models.user import User

@pytest.fixture
def app():
    app = create_app('testing')
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()

@pytest.fixture
def client(app):
    return app.test_client()

@pytest.fixture
def test_user(app):
    with app.app_context():
        user = User(
            username='testuser',
            email='<EMAIL>',
            role='teacher'
        )
        user.password = 'password'
        db.session.add(user)
        db.session.commit()
        return user

def login(client, email, password):
    response = client.post('/auth/login', data=dict(
        email=email,
        password=password
    ), follow_redirects=True)
    return response

def test_upload_page_requires_login(client):
    """测试上传页面需要登录"""
    response = client.get('/resource/upload', follow_redirects=True)
    assert b'login' in response.data.lower()

def test_upload_page_accessible_after_login(client, test_user):
    """测试登录后可以访问上传页面"""
    login(client, '<EMAIL>', 'password')
    response = client.get('/resource/upload')
    assert response.status_code == 200
    assert b'upload' in response.data.lower()

def test_upload_file(client, test_user, app):
    """测试文件上传功能"""
    login(client, '<EMAIL>', 'password')
    
    # 创建测试文件
    data = dict(
        file=(io.BytesIO(b'test file content'), 'test.txt')
    )
    
    response = client.post('/resource/upload', data=data, follow_redirects=True)
    assert response.status_code == 200
    assert b'success' in response.data.lower()
    
    # 验证数据库中是否有记录
    with app.app_context():
        resource = Resource.query.filter_by(name='test.txt').first()
        assert resource is not None
        assert resource.type == 'document'
        assert resource.format == 'txt'
        # 在测试环境中，我们知道用户ID是1
        assert resource.owner_id == 1
        assert resource.file_metadata is not None
        assert 'md5_checksum' in resource.file_metadata
        assert 'upload_date' in resource.file_metadata
        
        # 验证文件是否存在
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], resource.url)
        assert os.path.exists(file_path)
        
        # 验证文件内容
        with open(file_path, 'rb') as f:
            content = f.read()
            assert content == b'test file content'
        
        # 清理测试文件
        if os.path.exists(file_path):
            os.remove(file_path)

def test_upload_invalid_file_type(client, test_user):
    """测试上传不支持的文件类型"""
    login(client, '<EMAIL>', 'password')
    
    # 创建测试文件，使用不支持的扩展名
    data = dict(
        file=(io.BytesIO(b'test file content'), 'test.exe')
    )
    
    response = client.post('/resource/upload', data=data, follow_redirects=True)
    assert response.status_code == 200
    assert b'error' in response.data.lower()
    assert b'type' in response.data.lower()

def test_upload_different_file_types(client, test_user, app):
    """测试上传不同类型的文件"""
    login(client, '<EMAIL>', 'password')
    
    # 测试不同类型的文件
    test_files = [
        ('document.pdf', b'fake pdf content', 'document'),
        ('image.jpg', b'fake image content', 'image'),
        ('video.mp4', b'fake video content', 'video'),
        ('audio.mp3', b'fake audio content', 'audio'),
        ('archive.zip', b'fake zip content', 'archive'),
        ('code.py', b'print("Hello, World!")', 'code'),
        ('image.heic', b'fake heic content', 'image'),
        ('video.ts', b'fake ts content', 'video'),
        ('audio.opus', b'fake opus content', 'audio'),
        ('archive.bz2', b'fake bz2 content', 'archive'),
        ('code.go', b'package main', 'code')
    ]
    
    for filename, content, expected_type in test_files:
        data = dict(
            file=(io.BytesIO(content), filename)
        )
        
        response = client.post('/resource/upload', data=data, follow_redirects=True)
        assert response.status_code == 200
        assert b'success' in response.data.lower()
        
        # 验证数据库中是否有记录
        with app.app_context():
            resource = Resource.query.filter_by(name=filename).first()
            assert resource is not None
            assert resource.type == expected_type
            assert resource.format == filename.split('.')[-1]
            
            # 验证文件是否存在
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], resource.url)
            assert os.path.exists(file_path)
            
            # 清理测试文件
            if os.path.exists(file_path):
                os.remove(file_path)
            db.session.delete(resource)
            db.session.commit()

def test_download_file(client, test_user, app):
    """测试文件下载功能"""
    login(client, '<EMAIL>', 'password')
    
    # 先上传一个文件
    data = dict(
        file=(io.BytesIO(b'test file content'), 'test_download.txt')
    )
    
    client.post('/resource/upload', data=data, follow_redirects=True)
    
    # 获取资源ID
    with app.app_context():
        resource = Resource.query.filter_by(name='test_download.txt').first()
        assert resource is not None
        
        # 下载文件
        response = client.get(f'/resource/download/{resource.id}')
        assert response.status_code == 200
        assert response.data == b'test file content'
        
        # 清理测试文件
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], resource.url)
        if os.path.exists(file_path):
            os.remove(file_path)

def test_rename_file(client, test_user, app):
    """测试文件重命名功能"""
    login(client, '<EMAIL>', 'password')
    
    # 先上传一个文件
    data = dict(
        file=(io.BytesIO(b'test file content'), 'test_rename.txt')
    )
    
    client.post('/resource/upload', data=data, follow_redirects=True)
    
    # 获取资源ID
    with app.app_context():
        resource = Resource.query.filter_by(name='test_rename.txt').first()
        assert resource is not None
        
        # 重命名文件
        response = client.post(
            f'/resource/{resource.id}/rename',
            data={'new_name': 'renamed.txt'},
            follow_redirects=True
        )
        assert response.status_code == 200
        
        # 验证重命名是否成功
        updated_resource = Resource.query.get(resource.id)
        assert updated_resource.name == 'renamed.txt'
        
        # 清理测试文件
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], resource.url)
        if os.path.exists(file_path):
            os.remove(file_path)

def test_delete_file(client, test_user, app):
    """测试文件删除功能"""
    login(client, '<EMAIL>', 'password')
    
    # 先上传一个文件
    data = dict(
        file=(io.BytesIO(b'test file content'), 'test_delete.txt')
    )
    
    client.post('/resource/upload', data=data, follow_redirects=True)
    
    # 获取资源ID和文件路径
    with app.app_context():
        resource = Resource.query.filter_by(name='test_delete.txt').first()
        assert resource is not None
        resource_id = resource.id
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], resource.url)
        
        # 确认文件存在
        assert os.path.exists(file_path)
        
        # 删除文件
        response = client.post(f'/resource/{resource_id}/delete', follow_redirects=True)
        assert response.status_code == 200
        assert b'success' in response.data.lower()
        
        # 验证数据库记录已删除
        deleted_resource = Resource.query.get(resource_id)
        assert deleted_resource is None
        
        # 验证文件已删除
        assert not os.path.exists(file_path)

def test_file_size_limit(client, test_user, app):
    """测试文件大小限制"""
    login(client, '<EMAIL>', 'password')
    
    # 临时修改配置，设置较小的文件大小限制用于测试
    original_max_size = app.config['MAX_CONTENT_LENGTH']
    app.config['MAX_CONTENT_LENGTH'] = 1024  # 1KB
    
    # 创建一个大于限制的文件
    large_content = b'x' * 1025  # 1025字节
    data = dict(
        file=(io.BytesIO(large_content), 'large_file.txt')
    )
    
    # 尝试上传
    response = client.post('/resource/upload', data=data, follow_redirects=True)
    
    # 恢复原始配置
    app.config['MAX_CONTENT_LENGTH'] = original_max_size
    
    # 验证是否返回错误
    assert response.status_code == 413 or b'error' in response.data.lower()
    
    # 验证数据库中没有记录
    with app.app_context():
        resource = Resource.query.filter_by(name='large_file.txt').first()
        assert resource is None

def test_search_resources(client, test_user, app):
    """测试资源搜索功能"""
    login(client, '<EMAIL>', 'password')
    
    # 先上传几个不同类型的文件
    files = [
        ('test_doc.txt', b'test document content', 'document'),
        ('test_image.jpg', b'fake image content', 'image'),
        ('test_video.mp4', b'fake video content', 'video')
    ]
    
    for filename, content, _ in files:
        data = dict(
            file=(io.BytesIO(content), filename)
        )
        client.post('/resource/upload', data=data, follow_redirects=True)
    
    # 测试基本搜索
    response = client.get('/resource/search?q=test')
    assert response.status_code == 200
    assert b'test_doc.txt' in response.data
    assert b'test_image.jpg' in response.data
    assert b'test_video.mp4' in response.data
    
    # 测试按类型搜索
    response = client.get('/resource/search?type=document')
    assert response.status_code == 200
    assert b'test_doc.txt' in response.data
    assert b'test_image.jpg' not in response.data
    
    # 测试组合搜索
    response = client.get('/resource/search?q=image&type=image')
    assert response.status_code == 200
    assert b'test_doc.txt' not in response.data
    assert b'test_image.jpg' in response.data
    assert b'test_video.mp4' not in response.data
    
    # 清理测试文件
    with app.app_context():
        resources = Resource.query.all()
        for resource in resources:
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], resource.url)
            if os.path.exists(file_path):
                os.remove(file_path)
        db.session.query(Resource).delete()
        db.session.commit()

def test_batch_delete(client, test_user, app):
    """测试批量删除功能"""
    login(client, '<EMAIL>', 'password')
    
    # 先上传几个文件
    files = [
        ('batch_delete_1.txt', b'test content 1'),
        ('batch_delete_2.txt', b'test content 2'),
        ('batch_delete_3.txt', b'test content 3')
    ]
    
    resource_ids = []
    
    for filename, content in files:
        data = dict(
            file=(io.BytesIO(content), filename)
        )
        client.post('/resource/upload', data=data, follow_redirects=True)
        
        # 获取资源ID
        with app.app_context():
            resource = Resource.query.filter_by(name=filename).first()
            if resource:
                resource_ids.append(resource.id)
    
    # 确保上传了所有文件
    assert len(resource_ids) == 3
    
    # 测试批量删除
    response = client.post('/resource/batch-delete', 
                          json={'resource_ids': resource_ids},
                          content_type='application/json')
    
    assert response.status_code == 200
    data = response.get_json()
    assert data['success'] is True
    assert data['deleted_count'] == 3
    
    # 验证文件已被删除
    with app.app_context():
        for resource_id in resource_ids:
            resource = Resource.query.get(resource_id)
            assert resource is None

def test_resource_detail_view(client, test_user, app):
    """测试资源详情页面"""
    login(client, '<EMAIL>', 'password')
    
    # 上传一个文件
    data = dict(
        file=(io.BytesIO(b'test file content'), 'test_detail.txt')
    )
    client.post('/resource/upload', data=data, follow_redirects=True)
    
    # 获取资源ID
    with app.app_context():
        resource = Resource.query.filter_by(name='test_detail.txt').first()
        assert resource is not None
        
        # 访问详情页面
        response = client.get(f'/resource/{resource.id}')
        assert response.status_code == 200
        assert b'test_detail.txt' in response.data
        assert b'document' in response.data.lower()
        
        # 清理测试文件
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], resource.url)
        if os.path.exists(file_path):
            os.remove(file_path)
        db.session.delete(resource)
        db.session.commit()

def test_chunked_upload(client, test_user, app):
    """测试分块上传功能"""
    login(client, '<EMAIL>', 'password')
    
    # 模拟文件ID
    file_id = 'test-file-id'
    filename = 'test_chunked.txt'
    
    # 创建临时目录
    temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'temp', file_id)
    os.makedirs(temp_dir, exist_ok=True)
    
    # 模拟上传两个分块
    chunk1_content = b'This is the first chunk of the file.'
    chunk2_content = b'This is the second chunk of the file.'
    
    # 上传第一个分块
    data1 = dict(
        file=(io.BytesIO(chunk1_content), 'chunk1'),
        filename=filename,
        chunkNumber='0',
        totalChunks='2',
        fileId=file_id
    )
    response1 = client.post('/resource/upload/chunked', data=data1)
    assert response1.status_code == 200
    json_data1 = response1.get_json()
    assert json_data1['success'] is True
    assert 'chunksReceived' in json_data1
    assert 'progress' in json_data1
    
    # 上传第二个分块（最后一个）
    data2 = dict(
        file=(io.BytesIO(chunk2_content), 'chunk2'),
        filename=filename,
        chunkNumber='1',
        totalChunks='2',
        fileId=file_id
    )
    response2 = client.post('/resource/upload/chunked', data=data2)
    assert response2.status_code == 200
    json_data2 = response2.get_json()
    assert json_data2['success'] is True
    assert 'resource_id' in json_data2
    assert 'resource_name' in json_data2
    assert 'resource_type' in json_data2
    
    # 验证数据库中是否有记录
    with app.app_context():
        resource = Resource.query.filter_by(name=filename).first()
        
        # 可能需要等待一下，因为合并文件是在最后一个分块上传后进行的
        if resource is None:
            # 如果资源记录不存在，可能是因为合并过程尚未完成
            # 在实际应用中，我们可能需要一个更可靠的方式来等待合并完成
            import time
            time.sleep(1)  # 等待1秒
            resource = Resource.query.filter_by(name=filename).first()
        
        # 如果资源存在，验证其属性
        if resource is not None:
            assert resource.type == 'document'
            assert resource.format == 'txt'
            # 在测试环境中，我们知道用户ID是1
            assert resource.owner_id == 1
            assert resource.file_metadata is not None
            assert resource.file_metadata.get('chunked_upload') is True
            assert 'md5_checksum' in resource.file_metadata
            assert 'upload_date' in resource.file_metadata
            
            # 验证文件是否存在并且内容正确
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], resource.url)
            if os.path.exists(file_path):
                with open(file_path, 'rb') as f:
                    content = f.read()
                    assert content == chunk1_content + chunk2_content
                
                # 清理测试文件
                os.remove(file_path)
                db.session.delete(resource)
                db.session.commit()
        
        # 清理临时目录
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)