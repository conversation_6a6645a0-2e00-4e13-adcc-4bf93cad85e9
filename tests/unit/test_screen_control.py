"""
画面控制功能单元测试
"""
import unittest
from unittest.mock import patch, MagicMock
from flask import url_for
from app import create_app, db
from app.models.user import User
from app.models.course import Course
from app.services.webrtc import paused_screens, active_rooms, selected_student_screens

class ScreenControlTestCase(unittest.TestCase):
    """画面控制功能测试用例"""

    def setUp(self):
        """测试前准备"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client(use_cookies=True)
        
        # 创建测试用户和课程
        self.teacher = User(username='teacher', email='<EMAIL>', password='password', role='teacher')
        self.student = User(username='student', email='<EMAIL>', password='password', role='student')
        db.session.add_all([self.teacher, self.student])
        db.session.commit()
        
        self.course = Course(name='Test Course', teacher_id=self.teacher.id)
        db.session.add(self.course)
        db.session.commit()
        
        # 模拟WebRTC房间和连接
        room_id = f"course_{self.course.id}"
        active_rooms[room_id] = {
            'clients': ['teacher_client_id', 'student_client_id'],
            'teacher_id': 'teacher_client_id'
        }
        selected_student_screens[room_id] = ['student_client_id']
        paused_screens[room_id] = []

    def tearDown(self):
        """测试后清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        
        # 清理WebRTC状态
        room_id = f"course_{self.course.id}"
        if room_id in active_rooms:
            del active_rooms[room_id]
        if room_id in selected_student_screens:
            del selected_student_screens[room_id]
        if room_id in paused_screens:
            del paused_screens[room_id]

    def test_pause_screen(self):
        """测试暂停画面功能"""
        # 模拟教师登录
        with self.client.session_transaction() as sess:
            sess['_user_id'] = self.teacher.id
        
        # 发送暂停画面请求
        response = self.client.post(
            f'/broadcast/api/broadcast/pause-screen/{self.course.id}',
            json={'student_id': 'student_client_id'},
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 200)
        json_data = response.get_json()
        self.assertEqual(json_data['status'], 'success')
        
        # 验证暂停画面列表
        room_id = f"course_{self.course.id}"
        self.assertIn('student_client_id', paused_screens.get(room_id, []))

    def test_resume_screen(self):
        """测试恢复画面功能"""
        # 模拟教师登录
        with self.client.session_transaction() as sess:
            sess['_user_id'] = self.teacher.id
        
        # 先暂停画面
        room_id = f"course_{self.course.id}"
        if room_id not in paused_screens:
            paused_screens[room_id] = []
        paused_screens[room_id].append('student_client_id')
        
        # 发送恢复画面请求
        response = self.client.post(
            f'/broadcast/api/broadcast/resume-screen/{self.course.id}',
            json={'student_id': 'student_client_id'},
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 200)
        json_data = response.get_json()
        self.assertEqual(json_data['status'], 'success')
        
        # 验证暂停画面列表
        self.assertNotIn('student_client_id', paused_screens.get(room_id, []))

    def test_get_paused_screens(self):
        """测试获取暂停画面列表"""
        # 模拟教师登录
        with self.client.session_transaction() as sess:
            sess['_user_id'] = self.teacher.id
        
        # 先暂停画面
        room_id = f"course_{self.course.id}"
        if room_id not in paused_screens:
            paused_screens[room_id] = []
        paused_screens[room_id].append('student_client_id')
        
        # 获取暂停画面列表
        response = self.client.get(f'/broadcast/api/broadcast/paused-screens/{self.course.id}')
        
        # 验证响应
        self.assertEqual(response.status_code, 200)
        json_data = response.get_json()
        self.assertEqual(json_data['status'], 'success')
        self.assertIn('student_client_id', json_data['paused_screens'])

    @patch('flask_socketio.SocketIO')
    def test_pause_screen_socket_event(self, mock_socketio):
        """测试暂停画面Socket.IO事件"""
        # 模拟Socket.IO实例
        mock_socketio_instance = MagicMock()
        mock_socketio.return_value = mock_socketio_instance
        
        # 模拟教师登录
        with self.client.session_transaction() as sess:
            sess['_user_id'] = self.teacher.id
        
        # 发送暂停画面请求
        self.client.post(
            f'/broadcast/api/broadcast/pause-screen/{self.course.id}',
            json={'student_id': 'student_client_id'},
            content_type='application/json'
        )
        
        # 验证Socket.IO事件
        mock_socketio_instance.emit.assert_called_with(
            'pause_screen',
            {
                'room_id': f"course_{self.course.id}",
                'student_id': 'student_client_id'
            },
            room='teacher_client_id'
        )

    @patch('flask_socketio.SocketIO')
    def test_resume_screen_socket_event(self, mock_socketio):
        """测试恢复画面Socket.IO事件"""
        # 模拟Socket.IO实例
        mock_socketio_instance = MagicMock()
        mock_socketio.return_value = mock_socketio_instance
        
        # 模拟教师登录
        with self.client.session_transaction() as sess:
            sess['_user_id'] = self.teacher.id
        
        # 先暂停画面
        room_id = f"course_{self.course.id}"
        if room_id not in paused_screens:
            paused_screens[room_id] = []
        paused_screens[room_id].append('student_client_id')
        
        # 发送恢复画面请求
        self.client.post(
            f'/broadcast/api/broadcast/resume-screen/{self.course.id}',
            json={'student_id': 'student_client_id'},
            content_type='application/json'
        )
        
        # 验证Socket.IO事件
        mock_socketio_instance.emit.assert_called_with(
            'resume_screen',
            {
                'room_id': f"course_{self.course.id}",
                'student_id': 'student_client_id'
            },
            room='teacher_client_id'
        )

if __name__ == '__main__':
    unittest.main()