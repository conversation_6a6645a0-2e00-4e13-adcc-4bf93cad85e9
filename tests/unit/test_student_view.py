"""
学生画面查看功能单元测试
"""
import unittest
import sys
import os
import json
from flask import url_for
from flask_login import login_user

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent
from app.services.webrtc import get_selected_student_screens, selected_student_screens

class StudentViewTestCase(unittest.TestCase):
    """学生画面查看功能测试用例"""
    
    def setUp(self):
        """测试前设置"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client()
        
        # 创建测试用户
        self.teacher = User(
            username='teacher',
            email='<EMAIL>',
            role='teacher',
            password='password'
        )
        
        self.student1 = User(
            username='student1',
            email='<EMAIL>',
            role='student',
            password='password'
        )
        
        self.student2 = User(
            username='student2',
            email='<EMAIL>',
            role='student',
            password='password'
        )
        
        # 创建测试课程
        self.course = Course(
            name='Test Course',
            description='Test Course Description',
            teacher_id=1,
            access_code='123456'
        )
        
        db.session.add(self.teacher)
        db.session.add(self.student1)
        db.session.add(self.student2)
        db.session.add(self.course)
        db.session.commit()
        
        # 学生加入课程
        course_student1 = CourseStudent(
            course_id=self.course.id,
            student_id=self.student1.id
        )
        course_student2 = CourseStudent(
            course_id=self.course.id,
            student_id=self.student2.id
        )
        db.session.add(course_student1)
        db.session.add(course_student2)
        db.session.commit()
        
        # 模拟WebRTC房间
        room_id = f"course_{self.course.id}"
        selected_student_screens[room_id] = []
    
    def tearDown(self):
        """测试后清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        
        # 清理WebRTC房间
        room_id = f"course_{self.course.id}"
        if room_id in selected_student_screens:
            del selected_student_screens[room_id]
    
    def test_get_student_screens_api(self):
        """测试获取学生画面列表API"""
        # 登录教师
        with self.client.session_transaction() as session:
            session['_user_id'] = self.teacher.id
        
        # 获取学生画面列表
        response = self.client.get(f'/broadcast/api/broadcast/students/{self.course.id}')
        self.assertEqual(response.status_code, 200)
        
        # 验证返回的JSON数据
        data = response.get_json()
        self.assertEqual(data['status'], 'success')
        self.assertIn('students', data)
    
    def test_select_student_screen_api(self):
        """测试选择学生画面API"""
        # 登录教师
        with self.client.session_transaction() as session:
            session['_user_id'] = self.teacher.id
        
        # 选择学生画面
        response = self.client.post(
            f'/broadcast/api/broadcast/select-student/{self.course.id}',
            json={'student_id': 'test_student_id'}
        )
        self.assertEqual(response.status_code, 200)
        
        # 验证返回的JSON数据
        data = response.get_json()
        self.assertEqual(data['status'], 'success')
    
    def test_deselect_student_screen_api(self):
        """测试取消选择学生画面API"""
        # 登录教师
        with self.client.session_transaction() as session:
            session['_user_id'] = self.teacher.id
        
        # 取消选择学生画面
        response = self.client.post(
            f'/broadcast/api/broadcast/deselect-student/{self.course.id}',
            json={'student_id': 'test_student_id'}
        )
        self.assertEqual(response.status_code, 200)
        
        # 验证返回的JSON数据
        data = response.get_json()
        self.assertEqual(data['status'], 'success')
    
    def test_compare_student_screens_api(self):
        """测试比较学生画面API"""
        # 登录教师
        with self.client.session_transaction() as session:
            session['_user_id'] = self.teacher.id
        
        # 比较学生画面
        response = self.client.post(
            f'/broadcast/api/broadcast/compare-students/{self.course.id}',
            json={'student_ids': ['test_student_id1', 'test_student_id2']}
        )
        self.assertEqual(response.status_code, 200)
        
        # 验证返回的JSON数据
        data = response.get_json()
        self.assertEqual(data['status'], 'success')
    
    def test_student_access_denied(self):
        """测试学生访问教师API被拒绝"""
        # 登录学生
        with self.client.session_transaction() as session:
            session['_user_id'] = self.student1.id
        
        # 尝试获取学生画面列表
        response = self.client.get(f'/broadcast/api/broadcast/students/{self.course.id}')
        self.assertEqual(response.status_code, 403)
        
        # 尝试选择学生画面
        response = self.client.post(
            f'/broadcast/api/broadcast/select-student/{self.course.id}',
            json={'student_id': 'test_student_id'}
        )
        self.assertEqual(response.status_code, 403)
    
    def test_get_selected_student_screens(self):
        """测试获取已选择的学生画面列表"""
        room_id = f"course_{self.course.id}"
        
        # 初始状态应为空列表
        screens = get_selected_student_screens(room_id)
        self.assertEqual(screens, [])
        
        # 添加学生画面
        selected_student_screens[room_id] = ['test_student_id1', 'test_student_id2']
        
        # 验证获取结果
        screens = get_selected_student_screens(room_id)
        self.assertEqual(screens, ['test_student_id1', 'test_student_id2'])

if __name__ == '__main__':
    unittest.main()