import unittest
import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from app import create_app, db
from app.models.user import User
from app.models.course import Course, CourseStudent, Group
from app.models.interaction import RandomPickRecord
from app.models.score import Score
import json
from datetime import datetime

class RandomPickScoreTestCase(unittest.TestCase):
    def setUp(self):
        """测试前设置"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        self.client = self.app.test_client()
        
        # 修复路由问题
        from app.controllers.interaction import interaction
        self.app.register_blueprint(interaction, url_prefix='/')
        
        # 创建测试用户（教师）
        self.teacher = User(
            username='teacher',
            email='<EMAIL>',
            role='teacher'
        )
        self.teacher.password = 'password'
        
        # 创建测试用户（学生）
        self.student = User(
            username='student',
            email='<EMAIL>',
            role='student'
        )
        self.student.password = 'password'
        
        # 创建测试课程
        self.course = Course(
            name='测试课程',
            teacher_id=1  # 教师ID为1
        )
        self.course.generate_access_code()
        
        # 添加学生到课程
        self.course_student = CourseStudent(
            course_id=1,  # 课程ID为1
            student_id=2   # 学生ID为2
        )
        
        # 创建测试小组
        self.group = Group(
            name='测试小组',
            course_id=1  # 课程ID为1
        )
        
        # 创建点名记录
        self.student_record = RandomPickRecord(
            course_id=1,
            pick_type='student',
            target_id=2,
            picked_at=datetime.utcnow()
        )
        
        self.group_record = RandomPickRecord(
            course_id=1,
            pick_type='group',
            target_id=1,
            picked_at=datetime.utcnow()
        )
        
        # 保存到数据库
        db.session.add(self.teacher)
        db.session.add(self.student)
        db.session.add(self.course)
        db.session.add(self.course_student)
        db.session.add(self.group)
        db.session.add(self.student_record)
        db.session.add(self.group_record)
        db.session.commit()
    
    def tearDown(self):
        """测试后清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def login(self, username, password):
        """登录辅助函数"""
        return self.client.post('/auth/login', data={
            'username': username,
            'password': password
        }, follow_redirects=True)
    
    def test_add_student_pick_score(self):
        """测试为随机点名的学生评分"""
        # 登录教师账号
        self.login('teacher', 'password')
        
        # 提交评分
        response = self.client.post('/add-pick-score', data={
            'record_id': self.student_record.id,
            'course_id': 1,
            'pick_type': 'student',
            'target_id': 2,
            'score': 5,
            'note': '回答问题正确'
        }, follow_redirects=True)
        
        # 验证评分成功
        self.assertEqual(response.status_code, 200)
        
        # 验证点名记录已更新
        record = RandomPickRecord.query.get(self.student_record.id)
        self.assertIsNotNone(record)
        self.assertEqual(record.score, 5)
        self.assertEqual(record.note, '回答问题正确')
        self.assertIsNotNone(record.scored_at)
        self.assertEqual(record.scored_by, 1)  # 教师ID为1
        
        # 验证评分记录已创建
        score = Score.query.filter_by(target_id=2, target_type='student').first()
        self.assertIsNotNone(score)
        self.assertEqual(score.score, 5)
        self.assertEqual(score.reason, '回答问题正确')
        self.assertEqual(score.created_by, 1)  # 教师ID为1
    
    def test_add_group_pick_score(self):
        """测试为随机点名的小组评分"""
        # 登录教师账号
        self.login('teacher', 'password')
        
        # 提交评分
        response = self.client.post('/add-pick-score', data={
            'record_id': self.group_record.id,
            'course_id': 1,
            'pick_type': 'group',
            'target_id': 1,
            'score': -2,
            'note': '小组表现不佳'
        }, follow_redirects=True)
        
        # 验证评分成功
        self.assertEqual(response.status_code, 200)
        
        # 验证点名记录已更新
        record = RandomPickRecord.query.get(self.group_record.id)
        self.assertIsNotNone(record)
        self.assertEqual(record.score, -2)
        self.assertEqual(record.note, '小组表现不佳')
        self.assertIsNotNone(record.scored_at)
        self.assertEqual(record.scored_by, 1)  # 教师ID为1
        
        # 验证评分记录已创建
        score = Score.query.filter_by(target_id=1, target_type='group').first()
        self.assertIsNotNone(score)
        self.assertEqual(score.score, -2)
        self.assertEqual(score.reason, '小组表现不佳')
        self.assertEqual(score.created_by, 1)  # 教师ID为1
    
    def test_invalid_record(self):
        """测试无效的点名记录"""
        # 登录教师账号
        self.login('teacher', 'password')
        
        # 提交评分（使用不存在的记录ID）
        response = self.client.post('/add-pick-score', data={
            'record_id': 999,
            'course_id': 1,
            'pick_type': 'student',
            'target_id': 2,
            'score': 5,
            'note': '回答问题正确'
        }, follow_redirects=True)
        
        # 验证评分失败
        self.assertEqual(response.status_code, 200)
        
        # 验证没有创建评分记录
        score = Score.query.filter_by(target_id=2, score=5).first()
        self.assertIsNone(score)
    
    def test_permission(self):
        """测试学生无权使用评分功能"""
        # 登录学生账号
        self.login('student', 'password')
        
        # 尝试提交评分
        response = self.client.post('/add-pick-score', data={
            'record_id': self.student_record.id,
            'course_id': 1,
            'pick_type': 'student',
            'target_id': 2,
            'score': 5,
            'note': '回答问题正确'
        }, follow_redirects=True)
        
        # 验证评分失败
        self.assertEqual(response.status_code, 200)
        
        # 验证点名记录未更新
        record = RandomPickRecord.query.get(self.student_record.id)
        self.assertIsNotNone(record)
        self.assertIsNone(record.score)
        self.assertIsNone(record.note)
        self.assertIsNone(record.scored_at)
        self.assertIsNone(record.scored_by)
        
        # 验证没有创建评分记录
        score = Score.query.filter_by(target_id=2, score=5).first()
        self.assertIsNone(score)

if __name__ == '__main__':
    unittest.main()