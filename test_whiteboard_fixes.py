#!/usr/bin/env python3
"""
测试白板修复的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db, socketio
from app.services.whiteboard import WhiteboardNamespace
import unittest
from unittest.mock import Mock, patch

class TestWhiteboardFixes(unittest.TestCase):
    """测试白板修复"""
    
    def setUp(self):
        """设置测试环境"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
        
        # 创建白板命名空间实例
        self.whiteboard_ns = WhiteboardNamespace('/whiteboard')
    
    def tearDown(self):
        """清理测试环境"""
        self.app_context.pop()
    
    def test_on_disconnect_with_reason(self):
        """测试on_disconnect方法接受reason参数"""
        # 模拟session数据
        with patch('app.services.whiteboard.session') as mock_session:
            mock_session.get.side_effect = lambda key: {
                'user_id': 'test_user',
                'group_id': 'test_group'
            }.get(key)
            
            # 初始化测试数据
            self.whiteboard_ns.group_users['test_group'] = {
                'test_user': {'name': 'Test User', 'id': 'test_user'}
            }
            
            # 测试不带reason参数的调用
            try:
                self.whiteboard_ns.on_disconnect()
                print("✓ on_disconnect() 无参数调用成功")
            except Exception as e:
                print(f"✗ on_disconnect() 无参数调用失败: {e}")
            
            # 测试带reason参数的调用
            try:
                self.whiteboard_ns.on_disconnect("client_disconnect")
                print("✓ on_disconnect(reason) 带参数调用成功")
            except Exception as e:
                print(f"✗ on_disconnect(reason) 带参数调用失败: {e}")
    
    def test_whiteboard_operation_processing(self):
        """测试白板操作处理"""
        # 测试freedraw元素处理
        test_operation = {
            'userId': 'test_user',
            'elements': [{
                'id': 'test_element',
                'type': 'freedraw',
                'points': [[10, 20], [30, 40], [50, 60]],
                'version': 1
            }],
            'deletedIds': [],
            'timestamp': 1234567890
        }
        
        # 测试更新白板状态
        try:
            self.whiteboard_ns._update_whiteboard_state('test_group', test_operation)
            
            # 检查状态是否正确更新
            state = self.whiteboard_ns.whiteboard_states.get('test_group', [])
            self.assertEqual(len(state), 1)
            self.assertEqual(state[0]['type'], 'freedraw')
            self.assertEqual(len(state[0]['points']), 3)
            print("✓ 白板操作处理成功")
        except Exception as e:
            print(f"✗ 白板操作处理失败: {e}")
    
    def test_element_data_integrity(self):
        """测试元素数据完整性"""
        # 测试freedraw元素的points数据格式
        test_points = [[10, 20], [30, 40], [50, 60]]
        
        # 模拟处理points数据
        processed_points = []
        for point in test_points:
            if isinstance(point, list) and len(point) >= 2:
                processed_points.append(point)
            else:
                processed_points.append([0, 0])  # 默认值
        
        self.assertEqual(len(processed_points), 3)
        self.assertEqual(processed_points[0], [10, 20])
        print("✓ 元素数据完整性检查通过")

def run_tests():
    """运行测试"""
    print("开始测试白板修复...")
    print("=" * 50)
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == '__main__':
    run_tests()
