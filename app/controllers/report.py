from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.activity import ClassActivity, ClassReport, ReportNote
from app.models.course import Course, CourseStudent, Attendance
from app.models.interaction import QuestionSession, GroupDiscussion, RandomPickRecord
from app.models.user import User
from app.models.score import Score
from datetime import datetime, timedelta
import json

report = Blueprint('report', __name__)

def record_activity(course_id, activity_type, content_id, title, description=None, end_time=None, participants_count=0):
    """
    记录课堂活动
    
    Args:
        course_id: 课程ID
        activity_type: 活动类型
        content_id: 关联的内容ID
        title: 活动标题
        description: 活动描述
        end_time: 结束时间
        participants_count: 参与人数
        
    Returns:
        创建的活动记录
    """
    try:
        activity = ClassActivity(
            course_id=course_id,
            type=activity_type,
            content_id=content_id,
            title=title,
            description=description,
            created_by=current_user.id,
            end_time=end_time,
            participants_count=participants_count
        )
        
        db.session.add(activity)
        db.session.commit()
        return activity
    except Exception as e:
        db.session.rollback()
        print(f"记录活动失败: {str(e)}")
        return None

@report.route('/activities/<int:course_id>')
@login_required
def activities(course_id):
    """查看课堂活动记录"""
    # 检查课程是否存在
    course = Course.query.get_or_404(course_id)
    
    # 检查权限
    if course.teacher_id != current_user.id and not CourseStudent.query.filter_by(
        course_id=course_id, student_id=current_user.id
    ).first():
        flash('您没有权限查看此课程的活动记录', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取活动记录
    activities = ClassActivity.query.filter_by(course_id=course_id).order_by(ClassActivity.created_at.desc()).all()
    
    return render_template('report/activities.html', course=course, activities=activities)

@report.route('/api/activities/<int:course_id>')
@login_required
def api_activities(course_id):
    """获取课堂活动记录API"""
    # 检查课程是否存在
    course = Course.query.get_or_404(course_id)
    
    # 检查权限
    if course.teacher_id != current_user.id and not CourseStudent.query.filter_by(
        course_id=course_id, student_id=current_user.id
    ).first():
        return jsonify({
            'success': False,
            'message': '您没有权限查看此课程的活动记录'
        })
    
    # 获取活动记录
    activities = ClassActivity.query.filter_by(course_id=course_id).order_by(ClassActivity.created_at.desc()).all()
    
    # 转换为字典列表
    activities_data = [activity.to_dict() for activity in activities]
    
    return jsonify({
        'success': True,
        'activities': activities_data
    })

@report.route('/reports/<int:course_id>')
@login_required
def reports(course_id):
    """查看课堂报告"""
    # 检查课程是否存在
    course = Course.query.get_or_404(course_id)
    
    # 检查权限
    if course.teacher_id != current_user.id and not CourseStudent.query.filter_by(
        course_id=course_id, student_id=current_user.id
    ).first():
        flash('您没有权限查看此课程的报告', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取报告
    reports = ClassReport.query.filter_by(course_id=course_id).order_by(ClassReport.date.desc()).all()
    
    # 如果是教师且没有报告，自动生成一份
    if current_user.id == course.teacher_id and not reports:
        report = generate_course_report(course_id)
        if report:
            reports = [report]
    
    return render_template('report/reports.html', course=course, reports=reports)

@report.route('/generate-report/<int:course_id>', methods=['POST'])
@login_required
def generate_report(course_id):
    """生成课堂报告"""
    # 检查课程是否存在
    course = Course.query.get_or_404(course_id)
    
    # 检查权限
    if course.teacher_id != current_user.id:
        flash('只有教师可以生成课堂报告', 'danger')
        return redirect(url_for('report.reports', course_id=course_id))
    
    # 生成报告
    report = generate_course_report(course_id)
    
    if report:
        flash('课堂报告生成成功', 'success')
    else:
        flash('课堂报告生成失败', 'danger')
    
    return redirect(url_for('report.reports', course_id=course_id))

def generate_course_report(course_id):
    """
    生成课程报告
    
    Args:
        course_id: 课程ID
        
    Returns:
        生成的报告对象
    """
    try:
        # 获取课程
        course = Course.query.get(course_id)
        if not course:
            return None
        
        # 获取今天的日期
        today = datetime.utcnow().date()
        
        # 检查今天是否已经生成过报告
        existing_report = ClassReport.query.filter_by(
            course_id=course_id,
            date=today
        ).first()
        
        if existing_report:
            # 更新已有报告
            report = existing_report
        else:
            # 创建新报告
            report = ClassReport(
                course_id=course_id,
                title=f"{course.name} - {today.isoformat()} 课堂报告",
                date=today,
                created_by=current_user.id
            )
            db.session.add(report)
        
        # 统计签到人数
        attendance_count = Attendance.query.join(CourseStudent).filter(
            CourseStudent.course_id == course_id,
            Attendance.date == today
        ).count()
        report.attendance_count = attendance_count
        
        # 统计题目数量
        question_sessions = QuestionSession.query.filter(
            QuestionSession.course_id == course_id,
            QuestionSession.created_at >= datetime.combine(today, datetime.min.time()),
            QuestionSession.created_at <= datetime.combine(today, datetime.max.time())
        ).all()
        report.question_count = len(question_sessions)
        
        # 统计互动次数
        interaction_count = 0
        
        # 问题会话
        interaction_count += report.question_count
        
        # 分组讨论
        discussion_count = GroupDiscussion.query.filter(
            GroupDiscussion.course_id == course_id,
            GroupDiscussion.created_at >= datetime.combine(today, datetime.min.time()),
            GroupDiscussion.created_at <= datetime.combine(today, datetime.max.time())
        ).count()
        interaction_count += discussion_count
        
        # 随机点名
        random_pick_count = RandomPickRecord.query.filter(
            RandomPickRecord.course_id == course_id,
            RandomPickRecord.picked_at >= datetime.combine(today, datetime.min.time()),
            RandomPickRecord.picked_at <= datetime.combine(today, datetime.max.time())
        ).count()
        interaction_count += random_pick_count
        
        report.interaction_count = interaction_count
        
        # 保存报告
        db.session.commit()
        
        return report
    
    except Exception as e:
        db.session.rollback()
        print(f"生成报告失败: {str(e)}")
        return None

@report.route('/report-detail/<int:report_id>')
@login_required
def report_detail(report_id):
    """查看报告详情"""
    # 获取报告
    report = ClassReport.query.get_or_404(report_id)
    
    # 获取课程
    course = Course.query.get_or_404(report.course_id)
    
    # 检查权限
    if course.teacher_id != current_user.id and not CourseStudent.query.filter_by(
        course_id=course.id, student_id=current_user.id
    ).first():
        flash('您没有权限查看此报告', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取报告日期
    report_date = report.date
    
    # 获取当天的活动记录
    activities = ClassActivity.query.filter(
        ClassActivity.course_id == course.id,
        ClassActivity.created_at >= datetime.combine(report_date, datetime.min.time()),
        ClassActivity.created_at <= datetime.combine(report_date, datetime.max.time())
    ).order_by(ClassActivity.created_at).all()
    
    # 获取当天的签到记录
    attendances = Attendance.query.join(CourseStudent).filter(
        CourseStudent.course_id == course.id,
        Attendance.date == report_date
    ).all()
    
    # 获取当天的问题会话
    question_sessions = QuestionSession.query.filter(
        QuestionSession.course_id == course.id,
        QuestionSession.created_at >= datetime.combine(report_date, datetime.min.time()),
        QuestionSession.created_at <= datetime.combine(report_date, datetime.max.time())
    ).all()
    
    # 获取当天的随机点名记录
    random_picks = RandomPickRecord.query.filter(
        RandomPickRecord.course_id == course.id,
        RandomPickRecord.picked_at >= datetime.combine(report_date, datetime.min.time()),
        RandomPickRecord.picked_at <= datetime.combine(report_date, datetime.max.time())
    ).all()
    
    # 获取当天的评分记录
    scores = Score.query.filter(
        Score.course_id == course.id,
        Score.created_at >= datetime.combine(report_date, datetime.min.time()),
        Score.created_at <= datetime.combine(report_date, datetime.max.time())
    ).all()
    
    # 获取报告备注
    notes = ReportNote.query.filter_by(report_id=report_id).order_by(ReportNote.created_at).all()
    
    return render_template('report/report_detail.html', 
                          report=report, 
                          course=course,
                          activities=activities,
                          attendances=attendances,
                          question_sessions=question_sessions,
                          random_picks=random_picks,
                          scores=scores,
                          notes=notes)

@report.route('/add-note/<int:report_id>', methods=['POST'])
@login_required
def add_note(report_id):
    """添加报告备注"""
    # 获取报告
    report = ClassReport.query.get_or_404(report_id)
    
    # 检查权限
    course = Course.query.get(report.course_id)
    if not course or course.teacher_id != current_user.id:
        flash('只有教师可以添加报告备注', 'danger')
        return redirect(url_for('report.report_detail', report_id=report_id))
    
    # 获取表单数据
    content = request.form.get('content')
    
    if not content:
        flash('备注内容不能为空', 'danger')
        return redirect(url_for('report.report_detail', report_id=report_id))
    
    try:
        # 创建备注
        note = ReportNote(
            report_id=report_id,
            content=content,
            created_by=current_user.id
        )
        
        db.session.add(note)
        db.session.commit()
        
        flash('备注添加成功', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'备注添加失败: {str(e)}', 'danger')
    
    return redirect(url_for('report.report_detail', report_id=report_id))

@report.route('/delete-note/<int:note_id>', methods=['POST'])
@login_required
def delete_note(note_id):
    """删除报告备注"""
    # 获取备注
    note = ReportNote.query.get_or_404(note_id)
    
    # 获取报告
    report = ClassReport.query.get(note.report_id)
    
    # 检查权限
    course = Course.query.get(report.course_id)
    if not course or course.teacher_id != current_user.id:
        flash('只有教师可以删除报告备注', 'danger')
        return redirect(url_for('report.report_detail', report_id=report.id))
    
    try:
        # 删除备注
        db.session.delete(note)
        db.session.commit()
        
        flash('备注删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'备注删除失败: {str(e)}', 'danger')
    
    return redirect(url_for('report.report_detail', report_id=report.id))

@report.route('/api/add-note', methods=['POST'])
@login_required
def api_add_note():
    """添加报告备注API"""
    # 获取JSON数据
    data = request.get_json()
    
    report_id = data.get('report_id')
    content = data.get('content')
    
    # 获取报告
    report = ClassReport.query.get_or_404(report_id)
    
    # 检查权限
    course = Course.query.get(report.course_id)
    if not course or course.teacher_id != current_user.id:
        return jsonify({
            'success': False,
            'message': '只有教师可以添加报告备注'
        })
    
    if not content:
        return jsonify({
            'success': False,
            'message': '备注内容不能为空'
        })
    
    try:
        # 创建备注
        note = ReportNote(
            report_id=report_id,
            content=content,
            created_by=current_user.id
        )
        
        db.session.add(note)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '备注添加成功',
            'note': note.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'备注添加失败: {str(e)}'
        })

@report.route('/student-report/<int:course_id>')
@login_required
def student_report(course_id):
    """查看学生个人报告"""
    # 检查课程是否存在
    course = Course.query.get_or_404(course_id)
    
    # 检查权限（必须是该课程的学生）
    course_student = CourseStudent.query.filter_by(
        course_id=course_id, student_id=current_user.id
    ).first()
    
    if not course_student:
        flash('您不是该课程的学生，无法查看个人报告', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取学生在该课程的所有活动数据
    
    # 1. 获取考勤记录
    attendances = Attendance.query.filter_by(course_student_id=course_student.id).all()
    attendance_rate = len(attendances) / (len(attendances) or 1) * 100 if attendances else 0
    
    # 2. 获取回答的问题
    from app.models.interaction import StudentAnswer
    answers = StudentAnswer.query.filter_by(student_id=current_user.id).all()
    
    # 计算正确率
    correct_answers = sum(1 for answer in answers if answer.is_correct)
    accuracy_rate = (correct_answers / len(answers)) * 100 if answers else 0
    
    # 3. 获取随机点名记录
    random_picks = RandomPickRecord.query.filter_by(
        course_id=course_id,
        student_id=current_user.id
    ).all()
    
    # 4. 获取评分记录
    scores = Score.query.filter_by(
        course_id=course_id,
        target_type='student',
        target_id=current_user.id
    ).all()
    
    total_score = sum(score.score for score in scores)
    
    # 5. 获取参与的小组讨论
    # 由于GroupDiscussion模型没有直接关联到小组成员，我们简单地获取课程的所有讨论
    group_discussions = GroupDiscussion.query.filter_by(course_id=course_id).all()
    
    # 6. 获取最近的课堂报告
    reports = ClassReport.query.filter_by(course_id=course_id).order_by(ClassReport.date.desc()).limit(5).all()
    
    return render_template('report/student_report.html',
                          course=course,
                          attendances=attendances,
                          attendance_rate=attendance_rate,
                          answers=answers,
                          accuracy_rate=accuracy_rate,
                          random_picks=random_picks,
                          scores=scores,
                          total_score=total_score,
                          group_discussions=group_discussions,
                          reports=reports)

@report.route('/api/student-report/<int:course_id>')
@login_required
def api_student_report(course_id):
    """获取学生个人报告API"""
    # 检查课程是否存在
    course = Course.query.get_or_404(course_id)
    
    # 检查权限（必须是该课程的学生）
    course_student = CourseStudent.query.filter_by(
        course_id=course_id, student_id=current_user.id
    ).first()
    
    if not course_student:
        return jsonify({
            'success': False,
            'message': '您不是该课程的学生，无法查看个人报告'
        })
    
    try:
        # 获取学生在该课程的所有活动数据
        
        # 1. 获取考勤记录
        attendances = Attendance.query.filter_by(course_student_id=course_student.id).all()
        attendance_data = {
            'total': len(attendances),
            'present': sum(1 for a in attendances if a.status == 'present'),
            'absent': sum(1 for a in attendances if a.status == 'absent'),
            'late': sum(1 for a in attendances if a.status == 'late')
        }
        
        # 2. 获取回答的问题
        from app.models.interaction import StudentAnswer
        answers = StudentAnswer.query.filter_by(student_id=current_user.id).all()
        
        answer_data = {
            'total': len(answers),
            'correct': sum(1 for answer in answers if answer.is_correct),
            'incorrect': sum(1 for answer in answers if not answer.is_correct)
        }
        
        # 3. 获取随机点名记录
        random_picks = RandomPickRecord.query.filter_by(
            course_id=course_id,
            student_id=current_user.id
        ).all()
        
        random_pick_data = {
            'total': len(random_picks),
            'with_score': sum(1 for pick in random_picks if pick.score is not None)
        }
        
        # 4. 获取评分记录
        scores = Score.query.filter_by(
            course_id=course_id,
            target_type='student',
            target_id=current_user.id
        ).all()
        
        score_data = {
            'total': len(scores),
            'total_score': sum(score.score for score in scores),
            'positive': sum(1 for score in scores if score.score > 0),
            'negative': sum(1 for score in scores if score.score < 0)
        }
        
        return jsonify({
            'success': True,
            'course': {
                'id': course.id,
                'name': course.name
            },
            'attendance': attendance_data,
            'answers': answer_data,
            'random_picks': random_pick_data,
            'scores': score_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取学生报告失败: {str(e)}'
        })