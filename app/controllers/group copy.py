from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db, socketio
from app.models.user import User
from app.models.course import Course, CourseStudent, Group, GroupMember
from app.models.resource import Resource
from app.models.activity import ClassActivity
import os
import uuid
from datetime import datetime
import json

group = Blueprint('group', __name__)

@group.route('/manage')
@login_required
def manage():
    """小组管理页面"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        flash('只有教师可以管理小组', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取教师的课程
    courses = Course.query.filter_by(teacher_id=current_user.id).all()
    
    # 获取课程的小组
    course_groups = {}
    for course in courses:
        groups = Group.query.filter_by(course_id=course.id).all()
        course_groups[course.id] = groups
    
    # 获取课程的学生
    course_students = {}
    for course in courses:
        students = []
        course_student_records = CourseStudent.query.filter_by(course_id=course.id).all()
        for cs in course_student_records:
            student = User.query.get(cs.student_id)
            if student:
                students.append({
                    'student_id': cs.student_id,
                    'student': student
                })
        course_students[course.id] = students
    
    return render_template('group/manage.html', 
                          courses=courses, 
                          course_groups=course_groups,
                          course_students=course_students)

@group.route('/attendance')
@login_required
def attendance():
    """考勤管理页面"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        flash('只有教师可以查看考勤', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取教师的课程
    courses = Course.query.filter_by(teacher_id=current_user.id).all()
    
    # 获取课程的学生考勤情况
    course_attendance = {}
    for course in courses:
        students = []
        course_student_records = CourseStudent.query.filter_by(course_id=course.id).all()
        for cs in course_student_records:
            student = User.query.get(cs.student_id)
            if student:
                # 这里可以添加更多考勤相关的信息
                students.append({
                    'student_id': cs.student_id,
                    'student': student,
                    'last_active': cs.last_active,
                    'attendance_count': cs.attendance_count
                })
        course_attendance[course.id] = students
    
    return render_template('group/attendance.html', 
                          courses=courses, 
                          course_attendance=course_attendance)

@group.route('/monitor')
@login_required
def monitor():
    """设备监控页面"""
    # 检查用户是否为教师
    if not current_user.is_teacher():
        flash('只有教师可以监控设备', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取教师的课程
    courses = Course.query.filter_by(teacher_id=current_user.id).all()
    
    # 获取课程的小组设备状态
    from app.models.device import DeviceStatus
    course_devices = {}
    for course in courses:
        groups = Group.query.filter_by(course_id=course.id).all()
        devices = []
        for group in groups:
            device = DeviceStatus.query.filter_by(group_id=group.id).first()
            if device:
                devices.append({
                    'group': group,
                    'device': device
                })
            else:
                devices.append({
                    'group': group,
                    'device': None
                })
        course_devices[course.id] = devices
    
    return render_template('group/monitor.html', 
                          courses=courses, 
                          course_devices=course_devices)




