"""
广播功能控制器
"""
from flask import Blueprint, render_template, request, jsonify, current_app
from flask_login import login_required, current_user
from app.models.course import Course, Group, GroupMember
from app import db

broadcast = Blueprint('broadcast', __name__)

@broadcast.route('/teacher/<int:course_id>')
@login_required
def teacher_broadcast(course_id):
    """
    教师广播页面
    
    Args:
        course_id: 课程ID
    """
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return render_template('errors/403.html'), 403
    
    # 检查课程是否存在且属于当前教师
    course = Course.query.filter_by(id=course_id, teacher_id=current_user.id).first_or_404()
    
    return render_template('broadcast/teacher.html', course=course)

@broadcast.route('/student/<int:course_id>')
@login_required
def student_broadcast(course_id):
    """
    学生广播页面
    
    Args:
        course_id: 课程ID
    """
    # 检查课程是否存在
    course = Course.query.filter_by(id=course_id).first_or_404()
    
    # 检查学生是否已加入该课程
    if not course.has_student(current_user.id) and not current_user.is_teacher():
        return render_template('errors/403.html'), 403
    
    return render_template('broadcast/student.html', course=course)

@broadcast.route('/student/screen/<int:course_id>')
@login_required
def student_screen(course_id):
    """
    学生投屏页面
    
    Args:
        course_id: 课程ID
    """
    # 检查课程是否存在
    course = Course.query.filter_by(id=course_id).first_or_404()
    
    # 检查学生是否已加入该课程
    if not course.has_student(current_user.id) and not current_user.is_teacher():
        return render_template('errors/403.html'), 403
    
    return render_template('broadcast/student_screen.html', course=course)

@broadcast.route('/api/broadcast/status/<int:course_id>', methods=['GET'])
@login_required
def get_broadcast_status(course_id):
    """
    获取课程广播状态
    
    Args:
        course_id: 课程ID
    """
    # 检查课程是否存在
    course = Course.query.filter_by(id=course_id).first_or_404()
    
    # 从app.services.webrtc中获取房间信息
    from app.services.webrtc import get_room_info
    room_id = f"course_{course_id}"
    room_info = get_room_info(room_id)
    
    if not room_info:
        return jsonify({
            'status': 'inactive',
            'message': '没有活跃的广播',
            'broadcaster_id': None,
            'clients_count': 0,
            'selected_screens': []
        })
    
    return jsonify({
        'status': 'active' if room_info.get('teacher_id') else 'inactive',
        'message': '广播正在进行' if room_info.get('teacher_id') else '没有活跃的广播',
        'broadcaster_id': room_info.get('teacher_id'),
        'clients_count': len(room_info.get('clients', [])),
        'selected_screens': room_info.get('selected_screens', [])
    })

@broadcast.route('/api/broadcast/students/<int:course_id>', methods=['GET'])
@login_required
def get_student_screens(course_id):
    """
    获取课程中的学生画面列表
    
    Args:
        course_id: 课程ID
    """
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({
            'status': 'error',
            'message': '只有教师可以查看学生画面列表'
        }), 403
    
    # 检查课程是否存在且属于当前教师
    course = Course.query.filter_by(id=course_id, teacher_id=current_user.id).first_or_404()
    
    # 从app.services.webrtc中获取房间信息
    from app.services.webrtc import get_room_info
    room_id = f"course_{course_id}"
    room_info = get_room_info(room_id)
    
    if not room_info:
        return jsonify({
            'status': 'error',
            'message': '房间不存在',
            'students': []
        }), 404
    
    # 获取房间中的学生信息
    students = []
    for client_id in room_info.get('clients', []):
        # 跳过教师
        if client_id == room_info.get('teacher_id'):
            continue
        
        # 添加学生信息
        students.append({
            'client_id': client_id,
            'selected': client_id in room_info.get('selected_screens', [])
        })
    
    return jsonify({
        'status': 'success',
        'message': '获取学生画面列表成功',
        'students': students
    })

@broadcast.route('/api/broadcast/select-student/<int:course_id>', methods=['POST'])
@login_required
def select_student_screen(course_id):
    """
    选择学生画面
    
    Args:
        course_id: 课程ID
    """
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({
            'status': 'error',
            'message': '只有教师可以选择学生画面'
        }), 403
    
    # 检查课程是否存在且属于当前教师
    course = Course.query.filter_by(id=course_id, teacher_id=current_user.id).first_or_404()
    
    # 获取请求数据
    data = request.get_json()
    if not data or 'student_id' not in data:
        return jsonify({
            'status': 'error',
            'message': '学生ID不能为空'
        }), 400
    
    student_id = data['student_id']
    
    # 从app.services.webrtc中获取房间信息
    from app.services.webrtc import get_room_info
    room_id = f"course_{course_id}"
    room_info = get_room_info(room_id)
    
    if not room_info:
        return jsonify({
            'status': 'error',
            'message': '房间不存在'
        }), 404
    
    # 检查学生是否在房间中
    if student_id not in room_info.get('clients', []):
        return jsonify({
            'status': 'error',
            'message': '学生不在房间中'
        }), 404
    
    # 通过Socket.IO发送选择学生画面的请求
    from flask_socketio import SocketIO
    socketio = SocketIO(message_queue=current_app.config['SOCKETIO_MESSAGE_QUEUE'])
    socketio.emit('select_student_screen', {
        'room_id': room_id,
        'student_id': student_id
    }, room=room_info.get('teacher_id'))
    
    return jsonify({
        'status': 'success',
        'message': '选择学生画面请求已发送'
    })

@broadcast.route('/api/broadcast/deselect-student/<int:course_id>', methods=['POST'])
@login_required
def deselect_student_screen(course_id):
    """
    取消选择学生画面
    
    Args:
        course_id: 课程ID
    """
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({
            'status': 'error',
            'message': '只有教师可以取消选择学生画面'
        }), 403
    
    # 检查课程是否存在且属于当前教师
    course = Course.query.filter_by(id=course_id, teacher_id=current_user.id).first_or_404()
    
    # 获取请求数据
    data = request.get_json()
    if not data or 'student_id' not in data:
        return jsonify({
            'status': 'error',
            'message': '学生ID不能为空'
        }), 400
    
    student_id = data['student_id']
    
    # 从app.services.webrtc中获取房间信息
    from app.services.webrtc import get_room_info
    room_id = f"course_{course_id}"
    room_info = get_room_info(room_id)
    
    if not room_info:
        return jsonify({
            'status': 'error',
            'message': '房间不存在'
        }), 404
    
    # 通过Socket.IO发送取消选择学生画面的请求
    from flask_socketio import SocketIO
    socketio = SocketIO(message_queue=current_app.config['SOCKETIO_MESSAGE_QUEUE'])
    socketio.emit('deselect_student_screen', {
        'room_id': room_id,
        'student_id': student_id
    }, room=room_info.get('teacher_id'))
    
    return jsonify({
        'status': 'success',
        'message': '取消选择学生画面请求已发送'
    })

@broadcast.route('/api/broadcast/compare-students/<int:course_id>', methods=['POST'])
@login_required
def compare_student_screens(course_id):
    """
    比较学生画面
    
    Args:
        course_id: 课程ID
    """
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({
            'status': 'error',
            'message': '只有教师可以比较学生画面'
        }), 403
    
    # 检查课程是否存在且属于当前教师
    course = Course.query.filter_by(id=course_id, teacher_id=current_user.id).first_or_404()
    
    # 获取请求数据
    data = request.get_json()
    if not data or 'student_ids' not in data or not data['student_ids']:
        return jsonify({
            'status': 'error',
            'message': '学生ID列表不能为空'
        }), 400
    
    student_ids = data['student_ids']
    
    # 从app.services.webrtc中获取房间信息
    from app.services.webrtc import get_room_info
    room_id = f"course_{course_id}"
    room_info = get_room_info(room_id)
    
    if not room_info:
        return jsonify({
            'status': 'error',
            'message': '房间不存在'
        }), 404
    
    # 通过Socket.IO发送比较学生画面的请求
    from flask_socketio import SocketIO
    socketio = SocketIO(message_queue=current_app.config['SOCKETIO_MESSAGE_QUEUE'])
    socketio.emit('compare_student_screens', {
        'room_id': room_id,
        'student_ids': student_ids
    }, room=room_info.get('teacher_id'))
    
    return jsonify({
        'status': 'success',
        'message': '比较学生画面请求已发送'
    })

@broadcast.route('/api/broadcast/pause-screen/<int:course_id>', methods=['POST'])
@login_required
def pause_student_screen(course_id):
    """
    暂停学生画面
    
    Args:
        course_id: 课程ID
    """
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({
            'status': 'error',
            'message': '只有教师可以暂停学生画面'
        }), 403
    
    # 检查课程是否存在且属于当前教师
    course = Course.query.filter_by(id=course_id, teacher_id=current_user.id).first_or_404()
    
    # 获取请求数据
    data = request.get_json()
    if not data or 'student_id' not in data:
        return jsonify({
            'status': 'error',
            'message': '学生ID不能为空'
        }), 400
    
    student_id = data['student_id']
    
    # 从app.services.webrtc中获取房间信息
    from app.services.webrtc import get_room_info
    room_id = f"course_{course_id}"
    room_info = get_room_info(room_id)
    
    if not room_info:
        return jsonify({
            'status': 'error',
            'message': '房间不存在'
        }), 404
    
    # 检查学生是否在房间中
    if student_id not in room_info.get('clients', []):
        return jsonify({
            'status': 'error',
            'message': '学生不在房间中'
        }), 404
    
    # 通过Socket.IO发送暂停学生画面的请求
    from flask_socketio import SocketIO
    socketio = SocketIO(message_queue=current_app.config['SOCKETIO_MESSAGE_QUEUE'])
    socketio.emit('pause_screen', {
        'room_id': room_id,
        'student_id': student_id
    }, room=room_info.get('teacher_id'))
    
    return jsonify({
        'status': 'success',
        'message': '暂停学生画面请求已发送'
    })

@broadcast.route('/api/broadcast/resume-screen/<int:course_id>', methods=['POST'])
@login_required
def resume_student_screen(course_id):
    """
    恢复学生画面
    
    Args:
        course_id: 课程ID
    """
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({
            'status': 'error',
            'message': '只有教师可以恢复学生画面'
        }), 403
    
    # 检查课程是否存在且属于当前教师
    course = Course.query.filter_by(id=course_id, teacher_id=current_user.id).first_or_404()
    
    # 获取请求数据
    data = request.get_json()
    if not data or 'student_id' not in data:
        return jsonify({
            'status': 'error',
            'message': '学生ID不能为空'
        }), 400
    
    student_id = data['student_id']
    
    # 从app.services.webrtc中获取房间信息
    from app.services.webrtc import get_room_info
    room_id = f"course_{course_id}"
    room_info = get_room_info(room_id)
    
    if not room_info:
        return jsonify({
            'status': 'error',
            'message': '房间不存在'
        }), 404
    
    # 通过Socket.IO发送恢复学生画面的请求
    from flask_socketio import SocketIO
    socketio = SocketIO(message_queue=current_app.config['SOCKETIO_MESSAGE_QUEUE'])
    socketio.emit('resume_screen', {
        'room_id': room_id,
        'student_id': student_id
    }, room=room_info.get('teacher_id'))
    
    return jsonify({
        'status': 'success',
        'message': '恢复学生画面请求已发送'
    })

@broadcast.route('/api/broadcast/paused-screens/<int:course_id>', methods=['GET'])
@login_required
def get_paused_screens(course_id):
    """
    获取暂停的学生画面列表
    
    Args:
        course_id: 课程ID
    """
    # 检查用户是否为教师
    if not current_user.is_teacher():
        return jsonify({
            'status': 'error',
            'message': '只有教师可以查看暂停的学生画面列表'
        }), 403
    
    # 检查课程是否存在且属于当前教师
    course = Course.query.filter_by(id=course_id, teacher_id=current_user.id).first_or_404()
    
    # 从app.services.webrtc中获取房间信息
    from app.services.webrtc import get_room_info
    room_id = f"course_{course_id}"
    room_info = get_room_info(room_id)
    
    if not room_info:
        return jsonify({
            'status': 'error',
            'message': '房间不存在',
            'paused_screens': []
        }), 404
    
    return jsonify({
        'status': 'success',
        'message': '获取暂停的学生画面列表成功',
        'paused_screens': room_info.get('paused_screens', [])
    })

@broadcast.route('/group/<int:course_id>')
@login_required
def group_broadcast(course_id):
    """
    小组广播页面
    
    Args:
        course_id: 课程ID
    """
    # 检查课程是否存在
    course = Course.query.filter_by(id=course_id).first_or_404()
    
    # 检查用户是否为学生且已加入该课程
    if not course.has_student(current_user.id) and not current_user.is_teacher():
        return render_template('errors/403.html'), 403
    
    # 获取用户所在的小组
    group_member = GroupMember.query.join(Group).filter(
        Group.course_id == course_id,
        GroupMember.student_id == current_user.id
    ).first()
    
    if not group_member and not current_user.is_teacher():
        return jsonify({
            'status': 'error',
            'message': '您不是任何小组的成员'
        }), 403
    
    # 如果是教师，允许访问任何小组的广播页面
    group = None
    if current_user.is_teacher():
        # 如果提供了group_id参数，则使用该小组
        group_id = request.args.get('group_id')
        if group_id:
            group = Group.query.filter_by(id=group_id, course_id=course_id).first()
    else:
        # 学生只能访问自己所在小组的广播页面
        group = group_member.group
    
    if not group:
        return jsonify({
            'status': 'error',
            'message': '小组不存在'
        }), 404
    
    return render_template('broadcast/group.html', course=course, group=group)

@broadcast.route('/api/group-broadcast/start/<int:course_id>', methods=['POST'])
@login_required
def start_group_broadcast(course_id):
    """
    开始小组广播
    
    Args:
        course_id: 课程ID
    """
    # 检查课程是否存在
    course = Course.query.filter_by(id=course_id).first_or_404()
    
    # 获取请求数据
    data = request.get_json()
    if not data:
        return jsonify({
            'status': 'error',
            'message': '请求数据不能为空'
        }), 400
    
    target = data.get('target', 'teacher')  # 默认广播到教师端
    
    # 获取用户所在的小组
    group_member = GroupMember.query.join(Group).filter(
        Group.course_id == course_id,
        GroupMember.student_id == current_user.id
    ).first()
    
    if not group_member and not current_user.is_teacher():
        return jsonify({
            'status': 'error',
            'message': '您不是任何小组的成员'
        }), 403
    
    # 如果是教师，允许以任何小组的身份广播
    group = None
    if current_user.is_teacher():
        group_id = data.get('group_id')
        if group_id:
            group = Group.query.filter_by(id=group_id, course_id=course_id).first()
            if not group:
                return jsonify({
                    'status': 'error',
                    'message': '小组不存在'
                }), 404
    else:
        # 学生只能以自己所在小组的身份广播
        group = group_member.group
    
    # 通过Socket.IO发送小组广播请求
    from flask_socketio import SocketIO
    socketio = SocketIO(message_queue=current_app.config['SOCKETIO_MESSAGE_QUEUE'])
    
    # 发送小组广播请求
    socketio.emit('group_broadcast_request', {
        'room_id': f"course_{course_id}",
        'group_id': group.id,
        'target_type': target
    })
    
    return jsonify({
        'status': 'success',
        'message': '小组广播请求已发送',
        'group_id': group.id,
        'target': target
    })

@broadcast.route('/api/group-broadcast/stop/<int:course_id>', methods=['POST'])
@login_required
def stop_group_broadcast(course_id):
    """
    停止小组广播
    
    Args:
        course_id: 课程ID
    """
    # 检查课程是否存在
    course = Course.query.filter_by(id=course_id).first_or_404()
    
    # 获取请求数据
    data = request.get_json() or {}
    
    # 获取用户所在的小组
    group_member = GroupMember.query.join(Group).filter(
        Group.course_id == course_id,
        GroupMember.student_id == current_user.id
    ).first()
    
    if not group_member and not current_user.is_teacher():
        return jsonify({
            'status': 'error',
            'message': '您不是任何小组的成员'
        }), 403
    
    # 如果是教师，允许停止任何小组的广播
    group = None
    if current_user.is_teacher():
        group_id = data.get('group_id')
        if group_id:
            group = Group.query.filter_by(id=group_id, course_id=course_id).first()
            if not group:
                return jsonify({
                    'status': 'error',
                    'message': '小组不存在'
                }), 404
    else:
        # 学生只能停止自己所在小组的广播
        group = group_member.group
    
    # 通过Socket.IO发送停止小组广播请求
    from flask_socketio import SocketIO
    socketio = SocketIO(message_queue=current_app.config['SOCKETIO_MESSAGE_QUEUE'])
    
    # 发送停止小组广播请求
    socketio.emit('stop_group_broadcast', {
        'room_id': f"course_{course_id}",
        'group_id': group.id
    })
    
    return jsonify({
        'status': 'success',
        'message': '停止小组广播请求已发送',
        'group_id': group.id
    })

@broadcast.route('/api/group-broadcast/status/<int:course_id>', methods=['GET'])
@login_required
def get_group_broadcast_status(course_id):
    """
    获取小组广播状态
    
    Args:
        course_id: 课程ID
    """
    # 检查课程是否存在
    course = Course.query.filter_by(id=course_id).first_or_404()
    
    # 从app.services.webrtc中获取房间信息
    from app.services.webrtc import get_room_info
    room_id = f"course_{course_id}"
    room_info = get_room_info(room_id)
    
    if not room_info:
        return jsonify({
            'status': 'error',
            'message': '房间不存在',
            'group_broadcasts': {}
        }), 404
    
    # 获取小组广播信息
    group_broadcasts = room_info.get('group_broadcasts', {})
    
    return jsonify({
        'status': 'success',
        'message': '获取小组广播状态成功',
        'group_broadcasts': group_broadcasts
    })