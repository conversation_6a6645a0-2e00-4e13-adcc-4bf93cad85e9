from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, current_app
from flask_login import login_required, current_user
from app import db,socketio
from app.models.course import Course, Group, GroupMember, CourseStudent, Attendance
from app.models.user import User
from app.models.resource import Resource
from app.models.activity import ClassActivity
from datetime import datetime
import os
import uuid

group = Blueprint('group', __name__)

@group.route('/manage/<int:course_id>')
@login_required
def manage(course_id):
    """
    分组管理页面
    
    Args:
        course_id: 课程ID
    
    Returns:
        渲染的分组管理页面
    """
    course = Course.query.get_or_404(course_id)
    
    # 检查当前用户是否为课程教师
    if course.teacher_id != current_user.id:
        flash('您没有权限管理此课程的分组', 'danger')
        return redirect(url_for('course.detail', course_id=course_id))
    
    # 获取课程的所有小组
    groups = Group.query.filter_by(course_id=course_id).all()
    
    # 获取课程的所有学生
    course_students = CourseStudent.query.filter_by(course_id=course_id).all()
    students = [cs.student for cs in course_students]
    
    # 获取已分组的学生ID
    grouped_student_ids = db.session.query(GroupMember.student_id).join(
        Group, Group.id == GroupMember.group_id
    ).filter(Group.course_id == course_id).all()
    
    # 转换为集合以便快速查找
    grouped_student_ids = {student_id for (student_id,) in grouped_student_ids}
    
    # 过滤出未分组的学生
    ungrouped_students = [s for s in students if s.id not in grouped_student_ids]
    
    return render_template(
        'group/manage.html',
        course=course,
        groups=groups,
        ungrouped_students=ungrouped_students
    )

@group.route('/api/groups/<int:course_id>', methods=['GET'])
@login_required
def get_groups(course_id):
    """
    获取课程的所有分组信息
    
    Args:
        course_id: 课程ID
    
    Returns:
        JSON格式的分组信息
    """
    course = Course.query.get_or_404(course_id)
    
    # 检查当前用户是否为课程教师或学生
    is_teacher = course.teacher_id == current_user.id
    is_student = course.has_student(current_user.id)
    
    if not (is_teacher or is_student):
        return jsonify({'error': '您没有权限查看此课程的分组信息'}), 403
    
    # 获取课程的所有小组
    groups = Group.query.filter_by(course_id=course_id).all()
    
    # 构建分组信息
    groups_data = []
    for group in groups:
        # 获取小组成员
        members = []
        for member in group.members:
            student = User.query.get(member.student_id)
            if student:
                members.append({
                    'id': student.id,
                    'name': student.username,
                    'avatar': student.avatar if hasattr(student, 'avatar') else None
                })
        
        groups_data.append({
            'id': group.id,
            'name': group.name,
            'device_id': group.device_id,
            'members': members
        })
    
    # 获取未分组的学生
    course_students = CourseStudent.query.filter_by(course_id=course_id).all()
    student_ids = [cs.student_id for cs in course_students]
    
    # 获取已分组的学生ID
    grouped_student_ids = db.session.query(GroupMember.student_id).join(
        Group, Group.id == GroupMember.group_id
    ).filter(Group.course_id == course_id).all()
    grouped_student_ids = {student_id for (student_id,) in grouped_student_ids}
    
    # 过滤出未分组的学生
    ungrouped_student_ids = [sid for sid in student_ids if sid not in grouped_student_ids]
    ungrouped_students = []
    
    for student_id in ungrouped_student_ids:
        student = User.query.get(student_id)
        if student:
            ungrouped_students.append({
                'id': student.id,
                'name': student.username,
                'avatar': student.avatar if hasattr(student, 'avatar') else None
            })
    
    return jsonify({
        'groups': groups_data,
        'ungrouped_students': ungrouped_students
    })

@group.route('/api/groups/<int:course_id>', methods=['POST'])
@login_required
def create_group(course_id):
    """
    创建新的分组
    
    Args:
        course_id: 课程ID
    
    Returns:
        JSON格式的新分组信息
    """
    course = Course.query.get_or_404(course_id)
    
    # 检查当前用户是否为课程教师
    if course.teacher_id != current_user.id:
        return jsonify({'error': '您没有权限管理此课程的分组'}), 403
    
    # 获取请求数据
    data = request.json
    if not data or 'name' not in data:
        return jsonify({'error': '缺少必要的分组信息'}), 400
    
    # 创建新分组
    group = Group(
        name=data['name'],
        course_id=course_id,
        device_id=data.get('device_id', '')
    )
    
    db.session.add(group)
    db.session.commit()
    
    return jsonify({
        'id': group.id,
        'name': group.name,
        'device_id': group.device_id,
        'members': []
    }), 201

@group.route('/api/groups/<int:group_id>', methods=['PUT'])
@login_required
def update_group(group_id):
    """
    更新分组信息
    
    Args:
        group_id: 分组ID
    
    Returns:
        JSON格式的更新后的分组信息
    """
    group = Group.query.get_or_404(group_id)
    course = Course.query.get(group.course_id)
    
    # 检查当前用户是否为课程教师
    if course.teacher_id != current_user.id:
        return jsonify({'error': '您没有权限管理此课程的分组'}), 403
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({'error': '缺少更新数据'}), 400
    
    # 更新分组信息
    if 'name' in data:
        group.name = data['name']
    
    if 'device_id' in data:
        group.device_id = data['device_id']
    
    db.session.commit()
    
    return jsonify({
        'id': group.id,
        'name': group.name,
        'device_id': group.device_id
    })

@group.route('/api/groups/<int:group_id>', methods=['DELETE'])
@login_required
def delete_group(group_id):
    """
    删除分组
    
    Args:
        group_id: 分组ID
    
    Returns:
        JSON格式的操作结果
    """
    group = Group.query.get_or_404(group_id)
    course = Course.query.get(group.course_id)
    
    # 检查当前用户是否为课程教师
    if course.teacher_id != current_user.id:
        return jsonify({'error': '您没有权限管理此课程的分组'}), 403
    
    # 删除分组成员关联
    GroupMember.query.filter_by(group_id=group_id).delete()
    
    # 删除分组
    db.session.delete(group)
    db.session.commit()
    
    return jsonify({'message': '分组已成功删除'})

@group.route('/api/groups/<int:group_id>/members', methods=['POST'])
@login_required
def add_group_member(group_id):
    """
    添加分组成员
    
    Args:
        group_id: 分组ID
    
    Returns:
        JSON格式的操作结果
    """
    group = Group.query.get_or_404(group_id)
    course = Course.query.get(group.course_id)
    
    # 检查当前用户是否为课程教师
    if course.teacher_id != current_user.id:
        return jsonify({'error': '您没有权限管理此课程的分组'}), 403
    
    # 获取请求数据
    data = request.json
    if not data or 'student_id' not in data:
        return jsonify({'error': '缺少学生ID'}), 400
    
    student_id = data['student_id']
    
    # 检查学生是否已在课程中
    if not course.has_student(student_id):
        return jsonify({'error': '该学生不在此课程中'}), 400
    
    # 检查学生是否已在其他分组中
    existing_member = GroupMember.query.join(
        Group, Group.id == GroupMember.group_id
    ).filter(
        Group.course_id == course.id,
        GroupMember.student_id == student_id
    ).first()
    
    if existing_member:
        # 如果学生已在其他分组，先从原分组移除
        db.session.delete(existing_member)
    
    # 添加学生到当前分组
    member = GroupMember(
        group_id=group_id,
        student_id=student_id
    )
    
    db.session.add(member)
    db.session.commit()
    
    # 获取学生信息
    student = User.query.get(student_id)
    
    return jsonify({
        'id': student.id,
        'name': student.username,
        'avatar': student.avatar if hasattr(student, 'avatar') else None
    }), 201

@group.route('/api/groups/<int:group_id>/members/<int:student_id>', methods=['DELETE'])
@login_required
def remove_group_member(group_id, student_id):
    """
    移除分组成员
    
    Args:
        group_id: 分组ID
        student_id: 学生ID
    
    Returns:
        JSON格式的操作结果
    """
    group = Group.query.get_or_404(group_id)
    course = Course.query.get(group.course_id)
    
    # 检查当前用户是否为课程教师
    if course.teacher_id != current_user.id:
        return jsonify({'error': '您没有权限管理此课程的分组'}), 403
    
    # 查找并删除分组成员关联
    member = GroupMember.query.filter_by(
        group_id=group_id,
        student_id=student_id
    ).first_or_404()
    
    db.session.delete(member)
    db.session.commit()
    
    return jsonify({'message': '成员已成功从分组中移除'})
@group.route('/api/random/<int:course_id>', methods=['POST'])
@login_required
def random_group(course_id):
    """
    随机分组功能
    
    Args:
        course_id: 课程ID
    
    Returns:
        JSON格式的操作结果
    """
    from app.utils.group_utils import get_group_algorithm
    
    course = Course.query.get_or_404(course_id)
    
    # 检查当前用户是否为课程教师
    if course.teacher_id != current_user.id:
        return jsonify({'error': '您没有权限管理此课程的分组'}), 403
    
    # 获取请求数据
    data = request.json
    if not data or 'group_count' not in data:
        return jsonify({'error': '缺少分组数量参数'}), 400
    
    group_count = int(data['group_count'])
    algorithm = data.get('algorithm', 'equal')  # 默认使用均等分配算法
    
    # 获取课程的所有学生
    course_students = CourseStudent.query.filter_by(course_id=course_id).all()
    student_ids = [cs.student_id for cs in course_students]
    
    if not student_ids:
        return jsonify({'error': '课程中没有学生，无法进行分组'}), 400
    
    if group_count < 1 or group_count > len(student_ids):
        return jsonify({'error': f'分组数量必须在1到{len(student_ids)}之间'}), 400
    
    # 清除现有分组
    # 1. 删除所有分组成员
    group_ids = [group.id for group in Group.query.filter_by(course_id=course_id).all()]
    if group_ids:
        GroupMember.query.filter(GroupMember.group_id.in_(group_ids)).delete(synchronize_session=False)
    
    # 2. 删除所有分组
    Group.query.filter_by(course_id=course_id).delete()
    
    # 提交删除操作
    db.session.commit()
    
    # 获取分组算法
    group_algorithm = get_group_algorithm(algorithm)
    
    # 使用算法进行分组
    group_result = group_algorithm(student_ids, group_count)
    
    # 创建新分组并分配学生
    for group_idx, students in group_result.items():
        # 创建小组
        group = Group(
            name=f'小组 {group_idx}',
            course_id=course_id
        )
        db.session.add(group)
        db.session.flush()  # 获取自增ID
        
        # 分配学生到小组
        for student_id in students:
            member = GroupMember(
                group_id=group.id,
                student_id=student_id
            )
            db.session.add(member)
    
    # 提交分组操作
    db.session.commit()
    
    return jsonify({
        'message': '随机分组成功',
        'group_count': group_count,
        'algorithm': algorithm
    })
@group.route('/api/devices/<int:course_id>', methods=['GET'])
@login_required
def get_devices(course_id):
    """
    获取课程的所有设备状态
    
    Args:
        course_id: 课程ID
    
    Returns:
        JSON格式的设备状态信息
    """
    from app.services.device_monitor import get_all_device_status
    from app.models.device import DeviceStatus
    
    course = Course.query.get_or_404(course_id)
    
    # 检查当前用户是否为课程教师或学生
    is_teacher = course.teacher_id == current_user.id
    is_student = course.has_student(current_user.id)
    
    if not (is_teacher or is_student):
        return jsonify({'error': '您没有权限查看此课程的设备状态'}), 403
    
    # 获取课程的所有小组
    groups = Group.query.filter_by(course_id=course_id).all()
    
    # 获取所有设备状态
    all_devices = get_all_device_status()
    
    # 过滤出课程相关的设备
    course_devices = []
    
    # 教师设备
    teacher_device = next((d for d in all_devices if d['type'] == 'teacher' and d['owner_id'] == course.teacher_id), None)
    if teacher_device:
        teacher_device['role'] = 'teacher'
        teacher_device['name'] = '教师设备'
        course_devices.append(teacher_device)
    
    # 小组设备
    for group in groups:
        if group.device_id:
            group_device = next((d for d in all_devices if d['device_id'] == group.device_id), None)
            if group_device:
                group_device['role'] = 'group'
                group_device['name'] = f'{group.name}设备'
                group_device['group_id'] = group.id
                course_devices.append(group_device)
    
    # 学生设备
    student_ids = [cs.student_id for cs in CourseStudent.query.filter_by(course_id=course_id).all()]
    for device in all_devices:
        if device['type'] == 'student' and device['owner_id'] in student_ids:
            student = User.query.get(device['owner_id'])
            if student:
                device['role'] = 'student'
                device['name'] = f'{student.username}的设备'
                course_devices.append(device)
    
    return jsonify({'devices': course_devices})

@group.route('/monitor/<int:course_id>')
@login_required
def monitor(course_id):
    """
    设备状态监控页面
    
    Args:
        course_id: 课程ID
    
    Returns:
        渲染的设备状态监控页面
    """
    course = Course.query.get_or_404(course_id)
    
    # 检查当前用户是否为课程教师
    if course.teacher_id != current_user.id:
        flash('您没有权限查看此课程的设备状态监控', 'danger')
        return redirect(url_for('course.detail', course_id=course_id))
    
    return render_template(
        'group/monitor.html',
        course=course
    )
@group.route('/api/attendance/<int:course_id>', methods=['GET'])
@login_required
def get_attendance(course_id):
    """
    获取课程的考勤记录
    
    Args:
        course_id: 课程ID
    
    Returns:
        JSON格式的考勤记录
    """
    from datetime import datetime, timedelta
    
    course = Course.query.get_or_404(course_id)
    
    # 检查当前用户是否为课程教师
    if course.teacher_id != current_user.id:
        return jsonify({'error': '您没有权限查看此课程的考勤记录'}), 403
    
    # 获取查询参数
    date_str = request.args.get('date')
    if date_str:
        try:
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': '日期格式无效，请使用YYYY-MM-DD格式'}), 400
    else:
        # 默认使用今天的日期
        date = datetime.utcnow().date()
    
    # 获取课程的所有学生
    course_students = CourseStudent.query.filter_by(course_id=course_id).all()
    
    # 获取指定日期的考勤记录
    attendance_data = []
    for cs in course_students:
        # 查找学生在指定日期的考勤记录
        attendance = Attendance.query.filter_by(
            course_student_id=cs.id,
            date=date
        ).first()
        
        # 如果没有考勤记录，则创建一个默认的缺勤记录
        if not attendance:
            attendance_data.append({
                'student_id': cs.student_id,
                'student_name': cs.student.username,
                'status': 'absent',
                'check_in_time': None,
                'note': ''
            })
        else:
            attendance_data.append({
                'student_id': cs.student_id,
                'student_name': cs.student.username,
                'status': attendance.status,
                'check_in_time': attendance.check_in_time.isoformat() if attendance.check_in_time else None,
                'note': attendance.note or ''
            })
    
    return jsonify({
        'date': date.isoformat(),
        'attendance': attendance_data
    })

@group.route('/api/attendance/<int:course_id>', methods=['POST'])
@login_required
def update_attendance(course_id):
    """
    更新考勤记录
    
    Args:
        course_id: 课程ID
    
    Returns:
        JSON格式的操作结果
    """
    from datetime import datetime
    
    course = Course.query.get_or_404(course_id)
    
    # 检查当前用户是否为课程教师
    if course.teacher_id != current_user.id:
        return jsonify({'error': '您没有权限更新此课程的考勤记录'}), 403
    
    # 获取请求数据
    data = request.json
    if not data or 'attendance' not in data:
        return jsonify({'error': '缺少考勤数据'}), 400
    
    # 获取日期
    date_str = data.get('date')
    if date_str:
        try:
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': '日期格式无效，请使用YYYY-MM-DD格式'}), 400
    else:
        # 默认使用今天的日期
        date = datetime.utcnow().date()
    
    # 更新考勤记录
    attendance_data = data['attendance']
    updated_count = 0
    
    for record in attendance_data:
        student_id = record.get('student_id')
        status = record.get('status')
        note = record.get('note', '')
        
        if not student_id or not status:
            continue
        
        # 查找学生的课程关联记录
        course_student = CourseStudent.query.filter_by(
            course_id=course_id,
            student_id=student_id
        ).first()
        
        if not course_student:
            continue
        
        # 查找或创建考勤记录
        attendance = Attendance.query.filter_by(
            course_student_id=course_student.id,
            date=date
        ).first()
        
        if not attendance:
            attendance = Attendance(
                course_student_id=course_student.id,
                date=date,
                status=status,
                note=note
            )
            db.session.add(attendance)
        else:
            attendance.status = status
            attendance.note = note
            if status == 'present' and attendance.check_in_time is None:
                attendance.check_in_time = datetime.datetime()
        
        updated_count += 1
    
    db.session.commit()
    
    return jsonify({
        'message': f'成功更新{updated_count}条考勤记录',
        'date': date.isoformat()
    })

@group.route('/attendance/<int:course_id>')
@login_required
def attendance(course_id):
    """
    考勤管理页面
    
    Args:
        course_id: 课程ID
    
    Returns:
        渲染的考勤管理页面
    """
    course = Course.query.get_or_404(course_id)
    
    # 检查当前用户是否为课程教师
    if course.teacher_id != current_user.id:
        flash('您没有权限管理此课程的考勤', 'danger')
        return redirect(url_for('course.detail', course_id=course_id))
    
    return render_template(
        'group/attendance.html',
        course=course
    )

@group.route('/api/check-in/<int:course_id>', methods=['POST'])
@login_required
def student_check_in(course_id):
    """
    学生签到API
    """
    # 1. 验证角色
    if not current_user.is_student():
        return jsonify({'success': False, 'message': '只有学生可以签到'}), 403

    # 2. 查找课程和学生关联
    course = Course.query.get_or_404(course_id)
    course_student = CourseStudent.query.filter_by(
        course_id=course.id,
        student_id=current_user.id
    ).first()

    if not course_student:
        return jsonify({'success': False, 'message': '您未加入此课程，无法签到'}), 403

    # 3. 检查是否已签到
    from datetime import datetime
    today = datetime.utcnow().date()
    existing_attendance = Attendance.query.filter_by(
        course_student_id=course_student.id,
        date=today
    ).first()

    if existing_attendance:
        return jsonify({
            'success': False,
            'message': '已经签到过了',
            'check_in_time': existing_attendance.check_in_time.isoformat()
        }), 409

    # 4. 创建新的签到记录
    try:
        new_attendance = Attendance(
            course_student_id=course_student.id,
            status='present',
            date=today,
            check_in_time=datetime.utcnow()
        )
        db.session.add(new_attendance)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '签到成功！',
            'check_in_time': new_attendance.check_in_time.isoformat()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'签到时发生错误: {str(e)}'}), 500
    
@group.route('/whiteboard/<group_id>')
@login_required
def whiteboard(group_id):
    """小组白板协作功能"""
    # 获取小组信息
    group = Group.query.get_or_404(group_id)
    course = Course.query.get(group.course_id)
    
    # 验证用户权限
    has_permission = False
    if current_user.is_teacher():
        # 教师检查是否是该课程的教师
        if course and course.teacher_id == current_user.id:
            has_permission = True
    else:
        # 学生检查是否是该小组的成员
        membership = GroupMember.query.filter_by(
            group_id=group_id,
            student_id=current_user.id
        ).first()
        if membership:
            has_permission = True
    
    if not has_permission:
        flash('您没有权限访问此小组的白板', 'danger')
        return redirect(url_for('main.index'))
    
    # 记录活动
    if course:
        activity = ClassActivity(
            course_id=course.id,
            type='whiteboard',
            content_id=group_id,
            title=f"小组白板协作: {group.name}",
            description=f"使用白板进行协作",
            created_by=current_user.id,
            participants_count=GroupMember.query.filter_by(group_id=group_id).count()
        )
        db.session.add(activity)
        db.session.commit()
    
    return render_template('group/whiteboard.html', group=group, course=course)

@group.route('/share-file', methods=['GET', 'POST'])
@login_required
def share_file():
    """小组内文件分享功能"""
    # 获取用户所在的小组
    user_groups = []
    
    if current_user.is_student():
        # 学生用户获取所在小组
        group_memberships = GroupMember.query.filter_by(student_id=current_user.id).all()
        for membership in group_memberships:
            group = Group.query.get(membership.group_id)
            if group:
                course = Course.query.get(group.course_id)
                if course:
                    user_groups.append({
                        'group': group,
                        'course': course
                    })
    elif current_user.is_teacher():
        # 教师用户获取教授课程的所有小组
        courses = Course.query.filter_by(teacher_id=current_user.id).all()
        for course in courses:
            groups = Group.query.filter_by(course_id=course.id).all()
            for group in groups:
                user_groups.append({
                    'group': group,
                    'course': course
                })
    
    # 获取用户的资源
    user_resources = Resource.query.filter_by(owner_id=current_user.id).all()
    
    # 处理表单提交
    if request.method == 'POST':
        try:
            # 获取表单数据
            group_id = request.form.get('group_id')
            share_type = request.form.get('share_type')
            
            # 验证小组ID
            group = Group.query.get(group_id)
            if not group:
                flash('无效的小组', 'danger')
                return redirect(url_for('group.share_file'))
            
            # 验证用户是否有权限分享到该小组
            has_permission = False
            if current_user.is_teacher():
                # 教师检查是否是该课程的教师
                course = Course.query.get(group.course_id)
                if course and course.teacher_id == current_user.id:
                    has_permission = True
            else:
                # 学生检查是否是该小组的成员
                membership = GroupMember.query.filter_by(
                    group_id=group_id,
                    student_id=current_user.id
                ).first()
                if membership:
                    has_permission = True
            
            if not has_permission:
                flash('您没有权限向此小组分享文件', 'danger')
                return redirect(url_for('group.share_file'))
            
            # 处理不同的分享方式
            resource_ids = []
            
            if share_type == 'resource':
                # 从资源库选择
                resource_ids = request.form.getlist('resource_ids')
                if not resource_ids:
                    flash('请选择至少一个资源', 'warning')
                    return redirect(url_for('group.share_file'))
                
                # 验证资源所有权
                for resource_id in resource_ids:
                    resource = Resource.query.get(resource_id)
                    if not resource or resource.owner_id != current_user.id:
                        flash('您没有权限分享某些选定的资源', 'danger')
                        return redirect(url_for('group.share_file'))
                
                # 更新资源元数据，标记为已分享
                for resource_id in resource_ids:
                    resource = Resource.query.get(resource_id)
                    metadata = resource.file_metadata or {}
                    metadata['shared'] = True
                    metadata['shared_to_group'] = group_id
                    metadata['shared_at'] = datetime.utcnow().isoformat()
                    metadata['shared_by'] = current_user.id
                    resource.file_metadata = metadata
                    db.session.add(resource)
                
                db.session.commit()
                
            elif share_type == 'local':
                # 上传本地文件
                files = request.files.getlist('file')
                if not files or files[0].filename == '':
                    flash('请选择至少一个文件', 'warning')
                    return redirect(url_for('group.share_file'))
                
                # 上传文件并创建资源记录
                upload_folder = current_app.config['UPLOAD_FOLDER']
                for file in files:
                    # 生成唯一文件名
                    filename = f"{uuid.uuid4().hex}_{file.filename}"
                    file_path = os.path.join(upload_folder, filename)
                    
                    # 保存文件
                    file.save(file_path)
                    
                    # 确定文件类型
                    file_ext = os.path.splitext(file.filename)[1].lower()
                    if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                        file_type = 'image'
                    elif file_ext in ['.doc', '.docx', '.pdf', '.txt', '.ppt', '.pptx']:
                        file_type = 'document'
                    elif file_ext in ['.mp4', '.avi', '.mov', '.wmv']:
                        file_type = 'video'
                    elif file_ext in ['.mp3', '.wav', '.ogg']:
                        file_type = 'audio'
                    else:
                        file_type = 'other'
                    
                    # 创建资源记录
                    resource = Resource(
                        name=file.filename,
                        type=file_type,
                        url=filename,
                        size=os.path.getsize(file_path),
                        format=file_ext[1:] if file_ext else '',
                        owner_id=current_user.id,
                        course_id=group.course_id,
                        file_metadata={
                            'shared': True,
                            'shared_to_group': group_id,
                            'shared_at': datetime.utcnow().isoformat(),
                            'shared_by': current_user.id
                        }
                    )
                    
                    db.session.add(resource)
                    db.session.flush()  # 获取资源ID
                    resource_ids.append(str(resource.id))
                
                db.session.commit()
            
            # 记录活动
            course = Course.query.get(group.course_id)
            if course:
                activity = ClassActivity(
                    course_id=course.id,
                    type='file_share',
                    content_id=','.join(resource_ids),
                    title=f"小组文件分享: {group.name}",
                    description=f"分享了 {len(resource_ids)} 个文件到小组",
                    created_by=current_user.id,
                    participants_count=GroupMember.query.filter_by(group_id=group_id).count()
                )
                db.session.add(activity)
                db.session.commit()
            
            # 通过WebSocket通知小组成员
            notify_group_members_shared_files(group_id, resource_ids)
            
            flash('文件分享成功！', 'success')
            return redirect(url_for('group.share_file'))
        
        except Exception as e:
            db.session.rollback()
            flash(f'文件分享失败: {str(e)}', 'danger')
            return redirect(url_for('group.share_file'))
    
    # 获取小组成员信息
    group_members = {}
    for group_info in user_groups:
        group = group_info['group']
        members = []
        group_member_records = GroupMember.query.filter_by(group_id=group.id).all()
        for gm in group_member_records:
            student = User.query.get(gm.student_id)
            if student:
                members.append(student)
        group_members[group.id] = members
    
    return render_template('group/share_file.html',
                          user_groups=user_groups,
                          user_resources=user_resources,
                          group_members=group_members)

def notify_group_members_shared_files(group_id, resource_ids):
    """通知小组成员有新的文件分享"""
    # 获取小组成员
    group_members = GroupMember.query.filter_by(group_id=group_id).all()
    student_ids = [member.student_id for member in group_members]
    
    # 获取资源信息
    resources = Resource.query.filter(Resource.id.in_(resource_ids)).all()
    resource_data = [{
        'id': res.id,
        'name': res.name,
        'type': res.type,
        'url': res.url,
        'shared_by': current_user.name
    } for res in resources]
    
    # 构建通知数据
    notification_data = {
        'type': 'group_shared_files',
        'group_id': group_id,
        'resources': resource_data,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    # 发送给小组所有成员
    for student_id in student_ids:
        socketio.emit('group_event', notification_data, room=f'user_{student_id}')
