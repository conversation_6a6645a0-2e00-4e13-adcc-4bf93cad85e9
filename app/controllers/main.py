from flask import Blueprint, render_template, current_app
from flask_login import login_required, current_user

main = Blueprint('main', __name__)

@main.route('/')
def index():
    """首页"""
    return render_template('index.html')

@main.route('/dashboard')
@login_required
def dashboard():
    """用户仪表盘"""
    return render_template('dashboard.html')

@main.route('/webrtc-demo')
@login_required
def webrtc_demo():
    """WebRTC演示页面"""
    return render_template('webrtc_demo.html')