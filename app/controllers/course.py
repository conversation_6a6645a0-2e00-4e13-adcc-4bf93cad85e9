from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.course import Course, CourseStudent, Group, GroupMember
from datetime import datetime
import random
import json

course = Blueprint('course', __name__)

@course.route('/')
@login_required
def index():
    """课程列表"""
    if current_user.is_teacher():
        # 教师看到自己创建的所有课程
        courses = Course.query.filter_by(teacher_id=current_user.id).order_by(Course.created_at.desc()).all()
    else:
        # 学生看到自己加入的所有课程
        courses = Course.query.join(CourseStudent).filter(CourseStudent.student_id == current_user.id).order_by(Course.created_at.desc()).all()
        
    return render_template('course/index.html', courses=courses)

@course.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建课程"""
    if request.method == 'POST':
        # 验证用户是否为教师
        if not current_user.is_teacher():
            flash('只有教师可以创建课程', 'danger')
            return redirect(url_for('course.index'))
        
        # 获取表单数据
        name = request.form.get('name')
        description = request.form.get('description', '')
        code_length = int(request.form.get('code_length', 4))
        
        # 验证数据
        if not name:
            flash('课程名称不能为空', 'danger')
            return render_template('course/create.html')
        
        # 验证code_length是否为有效值（4、6或9）
        if code_length not in [4, 6, 9]:
            code_length = 4  # 默认使用4位
        
        # 创建课程
        new_course = Course(
            name=name,
            description=description,
            teacher_id=current_user.id
        )
        
        # 生成访问码
        new_course.generate_access_code(length=code_length)
        
        # 保存到数据库
        try:
            db.session.add(new_course)
            db.session.commit()
            flash(f'课程 "{name}" 创建成功，访问码: {new_course.access_code}', 'success')
            return redirect(url_for('course.detail', course_id=new_course.id))
        except Exception as e:
            db.session.rollback()
            flash(f'创建课程失败: {str(e)}', 'danger')
            return render_template('course/create.html')
    
    return render_template('course/create.html')

@course.route('/<int:course_id>')
@login_required
def detail(course_id):
    """课程详情页"""
    course = Course.query.get_or_404(course_id)
    
    # 权限检查：必须是课程的教师或已加入的学生
    is_student_in_course = CourseStudent.query.filter_by(
        course_id=course.id,
        student_id=current_user.id
    ).first()

    if not (current_user.is_teacher() and course.teacher_id == current_user.id) and not is_student_in_course:
        flash('您没有权限访问此课程', 'danger')
        return redirect(url_for('course.index'))

    if current_user.is_teacher():
        return render_template('course/detail.html', course=course)
    else:
        # 查找学生所在的小组
        student_group = Group.query.join(GroupMember).filter(
            Group.course_id == course.id,
            GroupMember.student_id == current_user.id
        ).first()
        return render_template('course/student_detail.html', course=course, student_group=student_group)

@course.route('/join', methods=['GET'])
@login_required
def join_course_page():
    """显示加入课程的页面"""
    if not current_user.is_student():
        flash('只有学生才能加入课程', 'warning')
        return redirect(url_for('main.dashboard'))
    return render_template('course/join.html')

@course.route('/handle_join', methods=['POST'])
@login_required
def handle_join_request():
    """处理从表单提交的加入课程请求"""
    access_code = request.form.get('access_code')
    if not access_code:
        flash('请输入课程码', 'danger')
        return redirect(url_for('course.join_course_page'))

    course = Course.query.filter_by(access_code=access_code).first()

    if not course:
        flash('课程码无效，请重新输入', 'danger')
        return redirect(url_for('course.join_course_page'))

    # 检查是否已加入
    existing_enrollment = CourseStudent.query.filter_by(
        course_id=course.id,
        student_id=current_user.id
    ).first()

    if existing_enrollment:
        flash('您已经在此课程中', 'info')
        return redirect(url_for('course.detail', course_id=course.id))

    # 加入课程
    try:
        enrollment = CourseStudent(
            course_id=course.id,
            student_id=current_user.id
        )
        db.session.add(enrollment)
        db.session.commit()
        flash(f'成功加入课程: {course.name}', 'success')
        return redirect(url_for('course.detail', course_id=course.id))
    except Exception as e:
        db.session.rollback()
        flash(f'加入课程失败: {str(e)}', 'danger')
        return redirect(url_for('course.join_course_page'))

@course.route('/<course_id>/join', methods=['POST'])
@login_required
def join(course_id):
    """加入课程"""
    # 这里将来会添加加入课程的逻辑
    flash('加入课程功能尚未实现', 'info')
    return redirect(url_for('course.detail', course_id=course_id))

@course.route('/join/<access_code>')
def join_by_code(access_code):
    """通过访问码加入课程（用于二维码扫描）"""
    # 查找课程
    course = Course.query.filter_by(access_code=access_code).first()
    if not course:
        flash('课程不存在或访问码无效', 'danger')
        return redirect(url_for('main.index'))
    
    # 如果用户已登录，直接跳转到课程详情
    if current_user.is_authenticated:
        return redirect(url_for('course.detail', course_id=course.id))
    else:
        # 如果用户未登录，跳转到登录页面，并在登录后重定向到课程详情
        flash(f'请先登录以加入课程: {course.name}', 'info')
        return redirect(url_for('auth.login', next=url_for('course.detail', course_id=course.id)))

@course.route('/generate-code', methods=['POST'])
@login_required
def generate_code():
    """生成课程码"""
    # 验证用户是否为教师
    if not current_user.is_teacher():
        return jsonify({'error': '只有教师可以生成课程码'}), 403
    
    # 获取请求参数
    course_id = request.json.get('course_id')
    length = request.json.get('length', 4)
    
    # 验证长度是否为有效值（4、6或9）
    if length not in [4, 6, 9]:
        length = 4  # 默认使用4位
    
    if course_id:
        # 更新现有课程的访问码
        course = Course.query.get(course_id)
        if not course:
            return jsonify({'error': '课程不存在'}), 404
        
        # 验证当前用户是否为课程的教师
        if course.teacher_id != current_user.id:
            return jsonify({'error': '您没有权限修改此课程'}), 403
        
        # 生成新的访问码
        code = course.generate_access_code(length=length)
        
        try:
            db.session.commit()
            return jsonify({'code': code, 'message': '课程码生成成功'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'生成课程码失败: {str(e)}'}), 500
    else:
        # 仅生成一个随机码，不保存到数据库
        chars = '0123456789'
        code = ''.join(random.choice(chars) for _ in range(length))
        return jsonify({'code': code, 'message': '临时课程码生成成功'})

@course.route('/generate-qr', methods=['POST'])
@login_required
def generate_qr():
    """生成课程二维码"""
    # 验证用户是否为教师
    if not current_user.is_teacher():
        return jsonify({'error': '只有教师可以生成二维码'}), 403
    
    # 获取请求参数
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '课程ID不能为空'}), 400
        
        course_id = data.get('course_id')
        if not course_id:
            return jsonify({'error': '课程ID不能为空'}), 400
    except Exception:
        return jsonify({'error': '无效的请求数据'}), 400
    
    base_url = data.get('base_url', request.host_url.rstrip('/'))
    
    # 查找课程
    course = Course.query.get(course_id)
    if not course:
        return jsonify({'error': '课程不存在'}), 404
    
    # 验证当前用户是否为课程的教师
    if course.teacher_id != current_user.id:
        return jsonify({'error': '您没有权限为此课程生成二维码'}), 403
    
    try:
        # 生成二维码
        qr_code_data = course.generate_qr_code(base_url=base_url)
        
        # 保存到数据库
        db.session.commit()
        
        return jsonify({
            'qr_code': qr_code_data,
            'access_code': course.access_code,
            'course_id': course.id,
            'course_name': course.name,
            'message': '二维码生成成功'
        })
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'生成二维码失败: {str(e)}'}), 500

@course.route('/api/courses', methods=['POST'])
@login_required
def create_course_api():
    """创建课程API"""
    # 验证用户是否为教师
    if not current_user.is_teacher():
        return jsonify({'error': '只有教师可以创建课程'}), 403
    
    # 获取JSON数据
    data = request.get_json()
    if not data:
        return jsonify({'error': '无效的请求数据'}), 400
    
    # 验证必要字段
    name = data.get('name')
    if not name:
        return jsonify({'error': '课程名称不能为空'}), 400
    
    # 获取可选字段
    description = data.get('description', '')
    code_length = data.get('code_length', 4)
    start_time = data.get('start_time')
    end_time = data.get('end_time')
    
    # 验证code_length是否为有效值（4、6或9）
    if code_length not in [4, 6, 9]:
        code_length = 4  # 默认使用4位
    
    # 处理日期时间
    try:
        if start_time:
            start_time = datetime.fromisoformat(start_time)
        if end_time:
            end_time = datetime.fromisoformat(end_time)
    except ValueError:
        return jsonify({'error': '日期格式无效，请使用ISO格式 (YYYY-MM-DDTHH:MM:SS)'}), 400
    
    # 创建课程
    new_course = Course(
        name=name,
        description=description,
        teacher_id=current_user.id,
        start_time=start_time,
        end_time=end_time
    )
    
    # 生成访问码
    access_code = new_course.generate_access_code(length=code_length)
    
    # 保存到数据库
    try:
        db.session.add(new_course)
        db.session.commit()
        
        # 返回创建的课程信息
        return jsonify({
            'id': new_course.id,
            'name': new_course.name,
            'description': new_course.description,
            'access_code': access_code,
            'teacher_id': new_course.teacher_id,
            'start_time': new_course.start_time.isoformat() if new_course.start_time else None,
            'end_time': new_course.end_time.isoformat() if new_course.end_time else None,
            'status': new_course.status,
            'created_at': new_course.created_at.isoformat(),
            'message': '课程创建成功'
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建课程失败: {str(e)}'}), 500

@course.route('/api/join-by-code', methods=['POST'])
@login_required
def join_by_code_api():
    """通过数字码加入课程API"""
    # 获取JSON数据
    data = request.get_json()
    if not data:
        return jsonify({'error': '无效的请求数据'}), 400
    
    # 验证必要字段
    access_code = data.get('access_code')
    if not access_code:
        return jsonify({'error': '课程访问码不能为空'}), 400
    
    # 查找课程
    course = Course.query.filter_by(access_code=access_code).first()
    if not course:
        return jsonify({'error': '课程不存在或访问码无效'}), 404
    
    # 检查用户是否已经加入课程
    existing_enrollment = CourseStudent.query.filter_by(
        course_id=course.id,
        student_id=current_user.id
    ).first()
    
    if existing_enrollment:
        return jsonify({
            'message': '您已经加入了此课程',
            'course': {
                'id': course.id,
                'name': course.name,
                'description': course.description,
                'status': course.status
            }
        })
    
    # 加入课程
    try:
        enrollment = CourseStudent(
            course_id=course.id,
            student_id=current_user.id
        )
        
        db.session.add(enrollment)
        db.session.commit()
        
        return jsonify({
            'message': f'成功加入课程: {course.name}',
            'course': {
                'id': course.id,
                'name': course.name,
                'description': course.description,
                'status': course.status,
                'teacher_id': course.teacher_id
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'加入课程失败: {str(e)}'}), 500

@course.route('/api/join-by-qr', methods=['POST'])
@login_required
def join_by_qr_api():
    """通过二维码数据加入课程API"""
    # 获取JSON数据
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400
        
        qr_data = data.get('qr_data')
        if not qr_data:
            return jsonify({'error': '二维码数据不能为空'}), 400
    except Exception:
        return jsonify({'error': '无效的请求数据'}), 400
    
    try:
        # 解析二维码数据
        qr_info = json.loads(qr_data)
        course_id = qr_info.get('course_id')
        access_code = qr_info.get('access_code')
        
        if not course_id or not access_code:
            return jsonify({'error': '二维码数据格式无效'}), 400
        
        # 查找课程
        course = Course.query.get(course_id)
        if not course or course.access_code != access_code:
            return jsonify({'error': '课程不存在或访问码无效'}), 404
        
        # 检查用户是否已经加入课程
        existing_enrollment = CourseStudent.query.filter_by(
            course_id=course.id,
            student_id=current_user.id
        ).first()
        
        if existing_enrollment:
            return jsonify({
                'message': '您已经加入了此课程',
                'course': {
                    'id': course.id,
                    'name': course.name,
                    'description': course.description,
                    'status': course.status
                }
            })
        
        # 加入课程
        enrollment = CourseStudent(
            course_id=course.id,
            student_id=current_user.id
        )
        
        db.session.add(enrollment)
        db.session.commit()
        
        return jsonify({
            'message': f'成功加入课程: {course.name}',
            'course': {
                'id': course.id,
                'name': course.name,
                'description': course.description,
                'status': course.status,
                'teacher_id': course.teacher_id
            }
        })
        
    except json.JSONDecodeError:
        return jsonify({'error': '二维码数据格式无效'}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'加入课程失败: {str(e)}'}), 500