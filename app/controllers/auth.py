from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from app import db
from app.models.user import User
from datetime import datetime, timezone
import re

auth = Blueprint('auth', __name__)

def validate_email(email):
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """验证密码强度"""
    if len(password) < 6:
        return False, "密码长度至少6位"
    return True, ""

@auth.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '')
        remember_me = bool(request.form.get('remember_me'))
        
        # 验证输入
        if not email or not password:
            flash('请填写完整的登录信息', 'error')
            return render_template('auth/login.html')
        
        if not validate_email(email):
            flash('请输入有效的邮箱地址', 'error')
            return render_template('auth/login.html')
        
        # 查找用户
        user = User.query.filter_by(email=email).first()
        
        if user and user.verify_password(password):
            # 更新最后登录时间
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            # 登录用户
            login_user(user, remember=remember_me)
            flash(f'欢迎回来，{user.username}！', 'success')
            
            # 重定向到原来想访问的页面或首页
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('main.index'))
        else:
            flash('邮箱或密码错误', 'error')
    
    return render_template('auth/login.html')

@auth.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '')
        password2 = request.form.get('password2', '')
        role = request.form.get('role', 'student')
        
        # 验证输入
        if not all([username, email, password, password2]):
            flash('请填写完整的注册信息', 'error')
            return render_template('auth/register.html')
        
        if not validate_email(email):
            flash('请输入有效的邮箱地址', 'error')
            return render_template('auth/register.html')
        
        if password != password2:
            flash('两次输入的密码不一致', 'error')
            return render_template('auth/register.html')
        
        is_valid, error_msg = validate_password(password)
        if not is_valid:
            flash(error_msg, 'error')
            return render_template('auth/register.html')
        
        if role not in ['teacher', 'student']:
            flash('请选择有效的用户角色', 'error')
            return render_template('auth/register.html')
        
        # 检查用户名和邮箱是否已存在
        if User.query.filter_by(username=username).first():
            flash('用户名已存在，请选择其他用户名', 'error')
            return render_template('auth/register.html')
        
        if User.query.filter_by(email=email).first():
            flash('邮箱已被注册，请使用其他邮箱', 'error')
            return render_template('auth/register.html')
        
        # 创建新用户
        try:
            user = User(
                username=username,
                email=email,
                password=password,
                role=role
            )
            db.session.add(user)
            db.session.commit()
            
            flash('注册成功！请登录', 'success')
            return redirect(url_for('auth.login'))
        except Exception as e:
            db.session.rollback()
            flash('注册失败，请稍后重试', 'error')
            return render_template('auth/register.html')
    
    return render_template('auth/register.html')

@auth.route('/logout')
@login_required
def logout():
    """用户登出"""
    username = current_user.username
    logout_user()
    flash(f'再见，{username}！', 'success')
    return redirect(url_for('main.index'))