/* Excalidraw风格的CSS */
:root {
  --primary-color: #6965db;
  --secondary-color: #f8f9fa;
  --text-color: #1e1e1e;
  --border-color: #d1d5db;
  --background-color: #ffffff;
  --error-color: #ef4444;
  --success-color: #10b981;
  --font-family: '<PERSON>', 'Segoe UI', system-ui, -apple-system, sans-serif;
}

/* 全局样式 */
body {
  font-family: var(--font-family);
  color: var(--text-color);
  background-color: var(--background-color);
  margin: 0;
  padding: 0;
  line-height: 1.5;
}

/* 手绘风格容器 */
.hand-drawn {
  border: 1px solid var(--text-color);
  border-radius: 8px;
  padding: 16px;
  position: relative;
  background-color: var(--secondary-color);
  box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1);
}

/* 手绘风格按钮 */
.btn-hand-drawn {
  font-family: var(--font-family);
  background-color: var(--primary-color);
  color: white;
  border: 2px solid var(--text-color);
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1);
}

.btn-hand-drawn:hover {
  transform: translateY(-2px);
  box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.2);
}

.btn-hand-drawn:active {
  transform: translateY(0);
  box-shadow: 1px 1px 0 rgba(0, 0, 0, 0.1);
}

/* 导航栏 */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--background-color);
  border-bottom: 2px solid var(--border-color);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-left: 1.5rem;
}

.nav-link {
  color: var(--text-color);
  text-decoration: none;
  transition: color 0.2s;
}

.nav-link:hover {
  color: var(--primary-color);
}

/* 表单样式 */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  font-family: var(--font-family);
  border: 2px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-color);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* 卡片样式 */
.card {
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  background-color: var(--background-color);
}

.card-header {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.card-body {
  padding: 0.5rem 0;
}

/* 网格布局 */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

/* 响应式容器 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* 提示消息 */
.alert {
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  border: 2px solid transparent;
  border-radius: 4px;
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: var(--success-color);
  color: var(--success-color);
}

.alert-error {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: var(--error-color);
  color: var(--error-color);
}

/* 加载动画 */
.spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 0.25rem solid rgba(105, 101, 219, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 工具栏 */
.toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: var(--secondary-color);
  border: 2px solid var(--border-color);
  border-radius: 4px;
}

/* 白板样式 */
.whiteboard {
  width: 100%;
  height: 500px;
  background-color: white;
  border: 2px solid var(--border-color);
  border-radius: 4px;
}

/* 视频容器 */
.video-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1rem;
}

.video-item {
  position: relative;
  padding-top: 56.25%; /* 16:9 宽高比 */
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.video-item video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .navbar-nav {
    margin-top: 1rem;
    flex-direction: column;
  }
  
  .nav-item {
    margin-left: 0;
    margin-top: 0.5rem;
  }
}

/* 自定义字体 
@font-face {
  font-family: 'Virgil';
  src: url('../fonts/Virgil.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
}*/