document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const excalidrawContainer = document.getElementById('excalidraw-container');
    const saveBtn = document.getElementById('save-btn');
    const clearBtn = document.getElementById('clear-btn');
    const onlineUsersContainer = document.getElementById('online-users');
    
    // 获取用户和小组信息
    const groupId = document.getElementById('group-id').value;
    const userId = document.getElementById('user-id').value;
    const userName = document.getElementById('user-name').value;
    
    // 白板状态和操作历史
    let excalidrawAPI = null;
    let lastElements = [];

    // 事件节流函数
    const throttle = (func, limit) => {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    };

    // 初始化Socket.IO连接
    const socket = io('/whiteboard', {
        query: {
            'group_id': groupId,
            'user_id': userId,
            'user_name': userName
        }
    });
    
    // 初始化Excalidraw
    const Excalidraw = window.ExcalidrawLib.Excalidraw;
    const excalidrawElement = React.createElement(Excalidraw, {
        ref: (api) => (excalidrawAPI = api),
        onChange: throttle((elements, appState) => {
            // Deep copy elements to prevent mutation issues
            const nextElements = JSON.parse(JSON.stringify(elements));
            const { added, updated, deleted } = getChangedElements(lastElements, nextElements);

            if (added.length === 0 && updated.length === 0 && deleted.length === 0) {
                return; // No changes
            }

            // 确保绘画元素包含完整的点数据
            const processedElements = [...added, ...updated].map(element => {
                if (element.type === 'freedraw' && element.points) {
                    // 确保points数组完整且格式正确
                    return {
                        ...element,
                        points: element.points.map(point => Array.isArray(point) ? point : [point.x || 0, point.y || 0])
                    };
                }
                return element;
            });

            const operation = {
                userId: userId,
                elements: processedElements,
                deletedIds: deleted.map(el => el.id),
                timestamp: Date.now()
            };

            socket.emit('whiteboard_operation', {
                group_id: groupId,
                operation: operation
            });

            // Store the deep-copied state for the next comparison
            lastElements = nextElements;
        }, 100) // 增加节流时间到100ms，确保绘画数据完整
    });

    ReactDOM.render(excalidrawElement, excalidrawContainer);

    // 计算增量变化的函数
    const getChangedElements = (prevElements, nextElements) => {
        const prevMap = new Map(prevElements.map(el => [el.id, el]));
        const nextMap = new Map(nextElements.map(el => [el.id, el]));

        const added = [];
        const updated = [];
        const deleted = [];

        // 查找新增和修改的元素
        for (const [id, element] of nextMap) {
            if (!prevMap.has(id)) {
                added.push(element);
            } else {
                const prevElement = prevMap.get(id);
                let hasChanged = false;

                // 检查版本变化
                if (element.version > prevElement.version) {
                    hasChanged = true;
                }

                // 检查freedraw元素的点数据变化
                if (element.type === 'freedraw') {
                    if (!prevElement.points || !element.points) {
                        hasChanged = true;
                    } else if (element.points.length !== prevElement.points.length) {
                        hasChanged = true;
                    } else {
                        // 深度比较points数组
                        for (let i = 0; i < element.points.length; i++) {
                            const currentPoint = element.points[i];
                            const prevPoint = prevElement.points[i];
                            if (!prevPoint || currentPoint[0] !== prevPoint[0] || currentPoint[1] !== prevPoint[1]) {
                                hasChanged = true;
                                break;
                            }
                        }
                    }
                }

                // 检查提交状态变化
                if (element.isCommitted !== prevElement.isCommitted) {
                    hasChanged = true;
                }

                if (hasChanged) {
                    updated.push(element);
                }
            }
        }

        // 查找删除的元素
        for (const [id, element] of prevMap) {
            if (!nextMap.has(id)) {
                deleted.push(element);
            }
        }

        return { added, updated, deleted };
    };
    
    // 更新在线用户列表
    const updateOnlineUsers = (users) => {
        onlineUsersContainer.innerHTML = '';
        
        users.forEach(user => {
            const userSpan = document.createElement('span');
            userSpan.textContent = user.name;
            userSpan.classList.add('user-badge');
            
            if (user.id === userId) {
                userSpan.classList.add('active');
                userSpan.textContent += ' (你)';
            }
            
            onlineUsersContainer.appendChild(userSpan);
        });
    };
    
    // Socket.IO事件处理
    socket.on('connect', () => {
        console.log('已连接到白板服务器');
        socket.emit('request_whiteboard_state', { group_id: groupId });
    });
    
    socket.on('disconnect', () => {
        console.log('与白板服务器断开连接');
    });
    
    socket.on('whiteboard_operation', (data) => {
        if (excalidrawAPI && data.operation.userId !== userId) {
            const { elements: updatedOrAddedElements, deletedIds } = data.operation;

            // Use a Map for robust element merging
            const currentElementsMap = new Map(excalidrawAPI.getSceneElements().map(el => [el.id, el]));

            // 1. Remove deleted elements
            if (deletedIds && deletedIds.length > 0) {
                for (const id of deletedIds) {
                    currentElementsMap.delete(id);
                }
            }

            // 2. Add or update elements
            if (updatedOrAddedElements && updatedOrAddedElements.length > 0) {
                for (const element of updatedOrAddedElements) {
                    // 确保freedraw元素的points数据格式正确
                    if (element.type === 'freedraw' && element.points) {
                        element.points = element.points.map(point =>
                            Array.isArray(point) ? point : [point.x || 0, point.y || 0]
                        );
                    }
                    currentElementsMap.set(element.id, element);
                }
            }

            const newElements = Array.from(currentElementsMap.values());

            // 使用批量更新避免频繁重绘
            excalidrawAPI.updateScene({
                elements: newElements,
                commitToHistory: false // 避免在协作时创建历史记录
            });

            // Update local state to prevent re-broadcasting received changes
            lastElements = JSON.parse(JSON.stringify(newElements));
        }
    });
    
    socket.on('whiteboard_state', (data) => {
        if (excalidrawAPI && data.elements) {
            // 处理接收到的元素数据，确保格式正确
            const processedElements = data.elements.map(element => {
                if (element.type === 'freedraw' && element.points) {
                    return {
                        ...element,
                        points: element.points.map(point =>
                            Array.isArray(point) ? point : [point.x || 0, point.y || 0]
                        )
                    };
                }
                return element;
            });

            lastElements = JSON.parse(JSON.stringify(processedElements));
            excalidrawAPI.updateScene({
                elements: processedElements,
                commitToHistory: false
            });
        }
    });
    
    socket.on('online_users', (data) => {
        updateOnlineUsers(data.users);
    });
    
    // 按钮事件处理
    saveBtn.addEventListener('click', async () => {
        if (!excalidrawAPI) return;
        
        const { exportToBlob } = await window.ExcalidrawLib;
        
        try {
            const blob = await exportToBlob({
                elements: excalidrawAPI.getSceneElements(),
                appState: excalidrawAPI.getAppState(),
                mimeType: 'image/png',
                quality: 1
            });
            
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `whiteboard-${groupId}-${new Date().toISOString().slice(0, 10)}.png`;
            link.click();
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('保存白板失败:', error);
            alert('保存白板失败，请重试');
        }
    });
    
    clearBtn.addEventListener('click', () => {
        if (!excalidrawAPI) return;
        
        if (confirm('确定要清除白板内容吗？此操作不可撤销。')) {
            excalidrawAPI.updateScene({
                elements: []
            });
            lastElements = [];
            
            socket.emit('whiteboard_clear', {
                group_id: groupId,
                user_id: userId
            });
        }
    });
    
    // 页面卸载前断开连接
    window.addEventListener('beforeunload', () => {
        socket.disconnect();
    });
});
