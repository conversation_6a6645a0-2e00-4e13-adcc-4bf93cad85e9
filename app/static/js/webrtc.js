/**
 * WebRTC客户端处理模块
 */

class WebRTCClient {
    /**
     * 初始化WebRTC客户端
     * @param {Object} config - 配置对象
     * @param {string} config.socketUrl - Socket.IO服务器URL
     * @param {string} config.userId - 用户ID
     * @param {string} config.userType - 用户类型（'teacher'或'student'）
     * @param {Object} config.iceServers - ICE服务器配置
     */
    constructor(config) {
        this.socket = io(config.socketUrl || window.location.origin);
        this.userId = config.userId;
        this.userType = config.userType;
        this.clientId = null;
        this.roomId = null;
        this.peers = {};
        this.localStream = null;
        this.isScreenSharing = false;
        this.isBroadcasting = false;
        
        // ICE服务器配置
        this.iceServers = config.iceServers || {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' }
            ]
        };
        
        // 事件回调
        this.onConnected = null;
        this.onRoomJoined = null;
        this.onPeerJoined = null;
        this.onPeerLeft = null;
        this.onRemoteStream = null;
        this.onBroadcastStarted = null;
        this.onBroadcastStopped = null;
        this.onError = null;
        this.onScreenSelectedByTeacher = null;
        this.onScreenDeselectedByTeacher = null;
        
        this._initSocketEvents();
    }
    
    /**
     * 初始化Socket.IO事件监听
     * @private
     */
    _initSocketEvents() {
        // 连接成功
        this.socket.on('connected', (data) => {
            this.clientId = data.client_id;
            console.log('Connected to signaling server with ID:', this.clientId);
            if (this.onConnected) {
                this.onConnected(this.clientId);
            }
        });
        
        // 加入房间成功
        this.socket.on('room_joined', (data) => {
            console.log('Joined room:', data.room_id);
            this.roomId = data.room_id;
            
            // 为房间中的每个对等端创建连接
            data.peers.forEach(peer => {
                this._createPeerConnection(peer.client_id);
                if (this.localStream) {
                    this._createOffer(peer.client_id);
                }
            });
            
            if (this.onRoomJoined) {
                this.onRoomJoined(data);
            }
        });
        
        // 新对等端加入
        this.socket.on('peer_joined', (data) => {
            console.log('Peer joined:', data.client_id);
            this._createPeerConnection(data.client_id);
            
            // 如果我们有本地流且是广播者，则创建offer
            if (this.localStream && (this.isBroadcasting || this.userType === 'teacher')) {
                this._createOffer(data.client_id);
            }
            
            if (this.onPeerJoined) {
                this.onPeerJoined(data);
            }
        });
        
        // 对等端离开
        this.socket.on('peer_left', (data) => {
            console.log('Peer left:', data.client_id);
            if (this.peers[data.client_id]) {
                this.peers[data.client_id].connection.close();
                delete this.peers[data.client_id];
            }
            
            if (this.onPeerLeft) {
                this.onPeerLeft(data);
            }
        });
        
        // 接收offer
        this.socket.on('offer', (data) => {
            console.log('Received offer from:', data.source_id);
            this._handleOffer(data.source_id, data.offer);
        });
        
        // 接收answer
        this.socket.on('answer', (data) => {
            console.log('Received answer from:', data.source_id);
            this._handleAnswer(data.source_id, data.answer);
        });
        
        // 接收ICE候选
        this.socket.on('ice_candidate', (data) => {
            console.log('Received ICE candidate from:', data.source_id);
            this._handleIceCandidate(data.source_id, data.candidate);
        });
        
        // 广播开始
        this.socket.on('broadcast_started', (data) => {
            console.log('Broadcast started by:', data.broadcaster_id);
            if (this.onBroadcastStarted) {
                this.onBroadcastStarted(data);
            }
        });
        
        // 广播停止
        this.socket.on('broadcast_stopped', (data) => {
            console.log('Broadcast stopped by:', data.broadcaster_id);
            if (this.onBroadcastStopped) {
                this.onBroadcastStopped(data);
            }
        });
        
        // 画面被教师选择
        this.socket.on('screen_selected_by_teacher', (data) => {
            console.log('Screen selected by teacher:', data.teacher_id);
            if (this.onScreenSelectedByTeacher) {
                this.onScreenSelectedByTeacher(data);
            }
        });
        
        // 画面被教师取消选择
        this.socket.on('screen_deselected_by_teacher', (data) => {
            console.log('Screen deselected by teacher:', data.teacher_id);
            if (this.onScreenDeselectedByTeacher) {
                this.onScreenDeselectedByTeacher(data);
            }
        });
        
        // 错误处理
        this.socket.on('error', (data) => {
            console.error('Error:', data.message);
            if (this.onError) {
                this.onError(data);
            }
        });
    }
    
    /**
     * 创建对等连接
     * @param {string} peerId - 对等端ID
     * @private
     */
    _createPeerConnection(peerId) {
        if (this.peers[peerId]) {
            console.log('Connection to peer already exists:', peerId);
            return;
        }
        
        const peerConnection = new RTCPeerConnection(this.iceServers);
        
        // 添加本地流的轨道
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => {
                peerConnection.addTrack(track, this.localStream);
            });
        }
        
        // 处理ICE候选
        peerConnection.onicecandidate = (event) => {
            if (event.candidate) {
                this.socket.emit('ice_candidate', {
                    target_id: peerId,
                    candidate: event.candidate
                });
            }
        };
        
        // 处理连接状态变化
        peerConnection.onconnectionstatechange = (event) => {
            console.log(`Connection state with ${peerId} changed to: ${peerConnection.connectionState}`);
        };
        
        // 处理ICE连接状态变化
        peerConnection.oniceconnectionstatechange = (event) => {
            console.log(`ICE connection state with ${peerId} changed to: ${peerConnection.iceConnectionState}`);
        };
        
        // 处理远程流
        peerConnection.ontrack = (event) => {
            console.log('Received remote track from:', peerId, event.track.kind);
            let stream = this.peers[peerId].stream;
            if (!stream) {
                stream = new MediaStream();
                this.peers[peerId].stream = stream;
            }

            // 如果有相同类型的轨道，先移除旧的
            const oldTracks = stream.getTracks().filter(t => t.kind === event.track.kind);
            oldTracks.forEach(t => stream.removeTrack(t));
            
            // 添加新轨道
            stream.addTrack(event.track);

            if (this.onRemoteStream) {
                this.onRemoteStream(peerId, stream);
            }
        };

        // 处理协商需求
        peerConnection.onnegotiationneeded = async () => {
            console.log(`Negotiation needed for peer: ${peerId}`);
            try {
                // 只有在有本地流且是广播方时才发起offer
                if (this.localStream && (this.isBroadcasting || this.userType === 'teacher')) {
                    await this._createOffer(peerId);
                }
            } catch (error) {
                console.error(`Error during negotiation for peer ${peerId}:`, error);
                if (this.onError) {
                    this.onError({ message: '协商失败: ' + error.message });
                }
            }
        };
        
        this.peers[peerId] = {
            connection: peerConnection,
            stream: null
        };
        
        console.log('Created peer connection for:', peerId);
    }
    
    /**
     * 创建并发送offer
     * @param {string} peerId - 对等端ID
     * @private
     */
    async _createOffer(peerId) {
        if (!this.peers[peerId] || !this.peers[peerId].connection) {
            console.error('No peer connection for:', peerId);
            return;
        }
        
        // 防止重复协商
        if (this.peers[peerId].connection.signalingState !== 'stable') {
            console.warn(`Signaling state is not stable for peer ${peerId}, skipping offer.`);
            return;
        }
        
        try {
            const peerConnection = this.peers[peerId].connection;
            const offer = await peerConnection.createOffer();
            await peerConnection.setLocalDescription(offer);
            
            this.socket.emit('offer', {
                target_id: peerId,
                offer: offer
            });
            
            console.log('Sent offer to:', peerId);
        } catch (error) {
            console.error('Error creating offer:', error);
            if (this.onError) {
                this.onError({ message: '创建offer失败: ' + error.message });
            }
        }
    }
    
    /**
     * 处理接收到的offer
     * @param {string} peerId - 对等端ID
     * @param {RTCSessionDescriptionInit} offer - 接收到的offer
     * @private
     */
    async _handleOffer(peerId, offer) {
        if (!this.peers[peerId]) {
            this._createPeerConnection(peerId);
        }
        
        try {
            const peerConnection = this.peers[peerId].connection;
            await peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
            
            // 创建answer
            const answer = await peerConnection.createAnswer();
            await peerConnection.setLocalDescription(answer);
            
            this.socket.emit('answer', {
                target_id: peerId,
                answer: answer
            });
            
            console.log('Sent answer to:', peerId);
        } catch (error) {
            console.error('Error handling offer:', error);
            if (this.onError) {
                this.onError({ message: '处理offer失败: ' + error.message });
            }
        }
    }
    
    /**
     * 处理接收到的answer
     * @param {string} peerId - 对等端ID
     * @param {RTCSessionDescriptionInit} answer - 接收到的answer
     * @private
     */
    async _handleAnswer(peerId, answer) {
        if (!this.peers[peerId]) {
            console.error('No peer connection for:', peerId);
            return;
        }
        
        try {
            const peerConnection = this.peers[peerId].connection;
            await peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
            console.log('Set remote description for:', peerId);
        } catch (error) {
            console.error('Error handling answer:', error);
            if (this.onError) {
                this.onError({ message: '处理answer失败: ' + error.message });
            }
        }
    }
    
    /**
     * 处理接收到的ICE候选
     * @param {string} peerId - 对等端ID
     * @param {RTCIceCandidateInit} candidate - 接收到的ICE候选
     * @private
     */
    async _handleIceCandidate(peerId, candidate) {
        if (!this.peers[peerId] || !this.peers[peerId].connection) {
            console.error('No peer connection for:', peerId);
            return;
        }
        
        try {
            const peerConnection = this.peers[peerId].connection;
            await peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
            console.log('Added ICE candidate for:', peerId);
        } catch (error) {
            console.error('Error handling ICE candidate:', error);
            if (this.onError) {
                this.onError({ message: '处理ICE候选失败: ' + error.message });
            }
        }
    }
    
    /**
     * 加入房间
     * @param {string} roomId - 房间ID
     */
    joinRoom(roomId) {
        this.socket.emit('join_room', {
            room_id: roomId,
            user_id: this.userId,
            user_type: this.userType
        });
        console.log('Joining room:', roomId);
    }
    
    /**
     * 离开房间
     */
    leaveRoom() {
        if (this.roomId) {
            this.socket.emit('leave_room', {
                room_id: this.roomId
            });
            
            // 关闭所有对等连接
            Object.keys(this.peers).forEach(peerId => {
                if (this.peers[peerId].connection) {
                    this.peers[peerId].connection.close();
                }
            });
            
            this.peers = {};
            this.roomId = null;
            
            console.log('Left room');
        }
    }
    
    /**
     * 开始屏幕共享
     * @returns {Promise<MediaStream>} 屏幕共享流
     */
    async startScreenSharing() {
        try {
            const stream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    cursor: 'always',
                    displaySurface: 'monitor'
                },
                audio: false
            });
            
            // 停止之前的本地流
            this.stopLocalStream();
            
            this.localStream = stream;
            this.isScreenSharing = true;
            
            const videoTrack = stream.getVideoTracks()[0];

            // 更新所有对等连接的视频轨道
            for (const peerId in this.peers) {
                const peerConnection = this.peers[peerId].connection;
                const sender = peerConnection.getSenders().find(s => s.track && s.track.kind === 'video');
                
                if (sender) {
                    console.log(`Replacing video track for peer ${peerId}`);
                    if (videoTrack) await sender.replaceTrack(videoTrack);
                } else {
                    console.log(`Adding video track for peer ${peerId}`);
                    if (videoTrack) peerConnection.addTrack(videoTrack, stream);
                }
            }
            
            // 监听屏幕共享停止事件
            stream.getVideoTracks()[0].onended = () => {
                this.stopScreenSharing();
            };
            
            console.log('Screen sharing started');
            return stream;
        } catch (error) {
            console.error('Error starting screen sharing:', error);
            if (this.onError) {
                this.onError({ message: '开始屏幕共享失败: ' + error.message });
            }
            throw error;
        }
    }
    
    /**
     * 停止屏幕共享
     */
    stopScreenSharing() {
        if (this.localStream && this.isScreenSharing) {
            this.stopLocalStream();
            this.isScreenSharing = false;
            console.log('Screen sharing stopped');
        }
    }
    
    /**
     * 开始摄像头视频
     * @param {Object} constraints - 媒体约束
     * @returns {Promise<MediaStream>} 摄像头视频流
     */
    async startCamera(constraints = { video: true, audio: true }) {
        try {
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            
            // 停止之前的本地流
            this.stopLocalStream();
            
            this.localStream = stream;
            this.isScreenSharing = false;

            const videoTrack = stream.getVideoTracks()[0];
            const audioTrack = stream.getAudioTracks()[0];

            // 更新所有对等连接的轨道
            for (const peerId in this.peers) {
                const peerConnection = this.peers[peerId].connection;
                
                const videoSender = peerConnection.getSenders().find(s => s.track && s.track.kind === 'video');
                if (videoSender) {
                    if (videoTrack) await videoSender.replaceTrack(videoTrack);
                } else if (videoTrack) {
                    peerConnection.addTrack(videoTrack, stream);
                }

                const audioSender = peerConnection.getSenders().find(s => s.track && s.track.kind === 'audio');
                if (audioSender) {
                    if (audioTrack) await audioSender.replaceTrack(audioTrack);
                } else if (audioTrack) {
                    peerConnection.addTrack(audioTrack, stream);
                }
            }
            
            console.log('Camera started');
            return stream;
        } catch (error) {
            console.error('Error starting camera:', error);
            if (this.onError) {
                this.onError({ message: '开始摄像头失败: ' + error.message });
            }
            throw error;
        }
    }
    
    /**
     * 停止本地流
     */
    stopLocalStream() {
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => {
                track.stop();
            });
            this.localStream = null;
        }
    }
    
    /**
     * 开始广播
     */
    startBroadcast() {
        if (this.roomId) {
            this.socket.emit('broadcast_request', {
                room_id: this.roomId
            });
            this.isBroadcasting = true;
            console.log('Broadcast request sent');
        } else {
            console.error('Not in a room');
            if (this.onError) {
                this.onError({ message: '未加入房间，无法开始广播' });
            }
        }
    }
    
    /**
     * 停止广播
     */
    stopBroadcast() {
        if (this.roomId && this.isBroadcasting) {
            this.socket.emit('stop_broadcast', {
                room_id: this.roomId
            });
            this.isBroadcasting = false;
            console.log('Stop broadcast request sent');
        }
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        this.leaveRoom();
        this.stopLocalStream();
        this.socket.disconnect();
        console.log('Disconnected from signaling server');
    }
}