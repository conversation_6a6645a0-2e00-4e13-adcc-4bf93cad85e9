from app import db
from datetime import datetime

class DeviceStatus(db.Model):
    """设备状态模型"""
    __tablename__ = 'device_status'
    
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.String(100), unique=True, index=True)
    type = db.Column(db.String(20))  # 'teacher', 'group', 'student'
    owner_id = db.Column(db.Integer)  # 用户ID或小组ID
    status = db.Column(db.String(20), default='offline')  # 'online', 'offline', 'broadcasting', 'receiving'
    ip_address = db.Column(db.String(50))
    last_ping = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<DeviceStatus {self.device_id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'device_id': self.device_id,
            'type': self.type,
            'owner_id': self.owner_id,
            'status': self.status,
            'ip_address': self.ip_address,
            'last_ping': self.last_ping.isoformat() if self.last_ping else None
        }