from app import db
from datetime import datetime

class Score(db.Model):
    """评分模型，用于记录教师对学生或小组的评分"""
    __tablename__ = 'scores'
    
    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.<PERSON>, db.<PERSON>ey('courses.id'))
    target_type = db.Column(db.String(20))  # 'student' 或 'group'
    target_id = db.Column(db.Integer)  # 学生ID或小组ID
    score = db.Column(db.Float, nullable=False)  # 评分值（可以是正数或负数）
    reason = db.Column(db.Text)  # 评分理由
    created_by = db.Column(db.Integer, db.Foreign<PERSON>ey('users.id'))  # 评分教师ID
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    course = db.relationship('Course', backref=db.backref('scores', lazy='dynamic'))
    teacher = db.relationship('User', backref=db.backref('given_scores', lazy='dynamic'))
    
    def __repr__(self):
        return f'<Score {self.id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'course_id': self.course_id,
            'target_type': self.target_type,
            'target_id': self.target_id,
            'score': self.score,
            'reason': self.reason,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }