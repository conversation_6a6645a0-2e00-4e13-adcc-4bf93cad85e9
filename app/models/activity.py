from app import db
from datetime import datetime

class ClassActivity(db.Model):
    """课堂活动记录模型，用于记录课堂中的各种活动"""
    __tablename__ = 'class_activities'
    
    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>ey('courses.id'))
    type = db.Column(db.String(50))  # 活动类型：'question', 'discussion', 'file_share', 'random_pick', 'broadcast', 'attendance'
    content_id = db.Column(db.Integer)  # 关联的内容ID（如问题ID、讨论ID等）
    title = db.Column(db.String(200))  # 活动标题
    description = db.Column(db.Text, nullable=True)  # 活动描述
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # 创建者ID
    created_at = db.Column(db.DateTime, default=datetime.utcnow)  # 创建时间
    end_time = db.Column(db.DateTime, nullable=True)  # 结束时间（如果适用）
    status = db.Column(db.String(20), default='active')  # 活动状态：'active', 'ended'
    participants_count = db.Column(db.Integer, default=0)  # 参与人数
    
    # 关系
    course = db.relationship('Course', backref=db.backref('activities', lazy='dynamic'))
    creator = db.relationship('User', backref=db.backref('created_activities', lazy='dynamic'))
    
    def __repr__(self):
        return f'<ClassActivity {self.id}: {self.type}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'course_id': self.course_id,
            'type': self.type,
            'content_id': self.content_id,
            'title': self.title,
            'description': self.description,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'participants_count': self.participants_count
        }

class ClassReport(db.Model):
    """课堂报告模型，用于存储课堂报告"""
    __tablename__ = 'class_reports'
    
    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.Integer, db.ForeignKey('courses.id'))
    title = db.Column(db.String(200))  # 报告标题
    date = db.Column(db.Date, default=lambda: datetime.utcnow().date())  # 报告日期
    attendance_count = db.Column(db.Integer, default=0)  # 签到人数
    question_count = db.Column(db.Integer, default=0)  # 题目数量
    interaction_count = db.Column(db.Integer, default=0)  # 互动次数
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # 创建者ID
    created_at = db.Column(db.DateTime, default=datetime.utcnow)  # 创建时间
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)  # 更新时间
    
    # 关系
    course = db.relationship('Course', backref=db.backref('reports', lazy='dynamic'))
    creator = db.relationship('User', backref=db.backref('created_reports', lazy='dynamic'))
    notes = db.relationship('ReportNote', backref='report', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<ClassReport {self.id}: {self.title}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'course_id': self.course_id,
            'title': self.title,
            'date': self.date.isoformat() if self.date else None,
            'attendance_count': self.attendance_count,
            'question_count': self.question_count,
            'interaction_count': self.interaction_count,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class ReportNote(db.Model):
    """报告备注模型，用于存储报告备注"""
    __tablename__ = 'report_notes'
    
    id = db.Column(db.Integer, primary_key=True)
    report_id = db.Column(db.Integer, db.ForeignKey('class_reports.id'))
    content = db.Column(db.Text, nullable=False)  # 备注内容
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # 创建者ID
    created_at = db.Column(db.DateTime, default=datetime.utcnow)  # 创建时间
    
    # 关系
    creator = db.relationship('User', backref=db.backref('report_notes', lazy='dynamic'))
    
    def __repr__(self):
        return f'<ReportNote {self.id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'report_id': self.report_id,
            'content': self.content,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }