from app import db
from datetime import datetime
import random
import string
import qrcode
import io
import base64
from PIL import Image

class Course(db.Model):
    """课程模型"""
    __tablename__ = 'courses'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    access_code = db.Column(db.String(10), unique=True, index=True)
    qr_code = db.Column(db.String(200))
    start_time = db.Column(db.DateTime)
    end_time = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='scheduled')  # 'scheduled', 'active', 'completed'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 外键
    teacher_id = db.Column(db.Integer, db.<PERSON>Key('users.id'))
    
    # 关系
    students = db.relationship('CourseStudent', backref='course', lazy='dynamic')
    groups = db.relationship('Group', backref='course', lazy='dynamic')
    # resources = db.relationship('Resource', backref='course', lazy='dynamic')  # 在Resource模型中定义
    # activities = db.relationship('Activity', backref='course', lazy='dynamic')  # TODO: 实现Activity模型后启用
    
    def generate_access_code(self, length=4):
        """
        生成课程访问码
        
        Args:
            length: 访问码长度，支持4位、6位或9位，默认为4位
            
        Returns:
            生成的访问码
        """
        # 验证长度是否为有效值
        if length not in [4, 6, 9]:
            length = 4  # 默认使用4位
        
        # 使用数字字符集
        chars = string.digits
        
        # 生成随机码
        max_attempts = 100  # 防止无限循环
        attempts = 0
        
        while attempts < max_attempts:
            # 生成随机码
            code = ''.join(random.choice(chars) for _ in range(length))
            
            # 确保访问码唯一
            if Course.query.filter_by(access_code=code).first() is None:
                self.access_code = code
                return code
            
            attempts += 1
        
        # 如果多次尝试后仍无法生成唯一码，使用时间戳作为后缀
        timestamp = str(int(datetime.utcnow().timestamp()))[-4:]
        code = ''.join(random.choice(chars) for _ in range(length - len(timestamp))) + timestamp
        self.access_code = code
        return code
    
    def generate_qr_code(self, base_url="http://localhost:5000"):
        """
        生成课程二维码
        
        Args:
            base_url: 应用的基础URL，默认为本地开发环境
            
        Returns:
            base64编码的二维码图片数据
        """
        if not self.access_code:
            raise ValueError("课程访问码不存在，请先生成访问码")
        
        # 构建二维码数据，包含课程ID和访问码
        qr_data = {
            "course_id": self.id,
            "access_code": self.access_code,
            "join_url": f"{base_url}/course/join/{self.access_code}"
        }
        
        # 将数据转换为JSON字符串
        import json
        qr_content = json.dumps(qr_data)
        
        # 创建二维码实例
        qr = qrcode.QRCode(
            version=1,  # 控制二维码大小，1是最小的
            error_correction=qrcode.constants.ERROR_CORRECT_L,  # 错误纠正级别
            box_size=10,  # 每个方格的像素数
            border=4,  # 边框大小
        )
        
        # 添加数据
        qr.add_data(qr_content)
        qr.make(fit=True)
        
        # 创建二维码图片
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        # 将图片转换为base64编码
        img_buffer = io.BytesIO()
        qr_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        # 编码为base64
        qr_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        
        # 保存二维码数据到数据库
        self.qr_code = f"data:image/png;base64,{qr_base64}"
        
        return self.qr_code
    
    def has_student(self, student_id):
        """
        检查学生是否已加入课程
        
        Args:
            student_id: 学生ID
            
        Returns:
            bool: 学生是否已加入课程
        """
        return CourseStudent.query.filter_by(
            course_id=self.id,
            student_id=student_id
        ).first() is not None
    
    def __repr__(self):
        return f'<Course {self.name}>'

class CourseStudent(db.Model):
    """课程学生关联模型"""
    __tablename__ = 'course_students'
    
    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.Integer, db.ForeignKey('courses.id'))
    student_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    joined_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    student = db.relationship('User', backref=db.backref('courses_enrolled', lazy='dynamic'))
    attendances = db.relationship('Attendance', backref='course_student', lazy='dynamic')
    
    def __repr__(self):
        return f'<CourseStudent {self.course_id}:{self.student_id}>'

class Attendance(db.Model):
    """考勤记录模型"""
    __tablename__ = 'attendances'
    
    id = db.Column(db.Integer, primary_key=True)
    course_student_id = db.Column(db.Integer, db.ForeignKey('course_students.id'))
    status = db.Column(db.String(20), default='present')  # 'present', 'absent', 'late'
    date = db.Column(db.Date, default=lambda: datetime.utcnow().date())
    check_in_time = db.Column(db.DateTime, default=datetime.utcnow)
    note = db.Column(db.String(200))
    
    def __repr__(self):
        return f'<Attendance {self.course_student_id}:{self.date}>'

class Group(db.Model):
    """小组模型"""
    __tablename__ = 'groups'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    course_id = db.Column(db.Integer, db.ForeignKey('courses.id'))
    device_id = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    members = db.relationship('GroupMember', backref='group', lazy='dynamic')
    
    def __repr__(self):
        return f'<Group {self.name}>'

class GroupMember(db.Model):
    """小组成员模型"""
    __tablename__ = 'group_members'
    
    id = db.Column(db.Integer, primary_key=True)
    group_id = db.Column(db.Integer, db.ForeignKey('groups.id'))
    student_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    joined_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    student = db.relationship('User', backref=db.backref('groups', lazy='dynamic'))
    
    def __repr__(self):
        return f'<GroupMember {self.group_id}:{self.student_id}>'