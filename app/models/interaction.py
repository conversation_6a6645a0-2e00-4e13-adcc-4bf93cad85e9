from app import db
from datetime import datetime

class QuestionSession(db.Model):
    """题目会话模型，用于记录教师发送的题目会话"""
    __tablename__ = 'question_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100))
    course_id = db.Column(db.In<PERSON>ger, db.<PERSON>ey('courses.id'))
    teacher_id = db.Column(db.Integer, db.Foreign<PERSON>ey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    end_time = db.Column(db.DateTime)  # 答题截止时间
    status = db.Column(db.String(20), default='active')  # 'active', 'ended'
    
    # 关系
    course = db.relationship('Course', backref=db.backref('question_sessions', lazy='dynamic'))
    teacher = db.relationship('User', backref=db.backref('question_sessions', lazy='dynamic'))
    questions = db.relationship('SessionQuestion', backref='session', lazy='dynamic')
    
    def __repr__(self):
        return f'<QuestionSession {self.id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'title': self.title,
            'course_id': self.course_id,
            'teacher_id': self.teacher_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status
        }

class SessionQuestion(db.Model):
    """会话题目模型，用于关联会话和题目"""
    __tablename__ = 'session_questions'
    
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('question_sessions.id'))
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'))
    order = db.Column(db.Integer)  # 题目顺序
    
    # 关系
    question = db.relationship('Question', backref=db.backref('sessions', lazy='dynamic'))
    answers = db.relationship('StudentAnswer', backref='session_question', lazy='dynamic')
    
    def __repr__(self):
        return f'<SessionQuestion {self.session_id}:{self.question_id}>'

class TemporaryQuestion(db.Model):
    """临时题目模型，用于存储教师临时创建的题目"""
    __tablename__ = 'temporary_questions'
    
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('question_sessions.id'))
    type = db.Column(db.String(20))  # 'single', 'multiple', 'truefalse', 'fillblank', 'subjective'
    content = db.Column(db.Text, nullable=False)
    options = db.Column(db.JSON)  # 选项（JSON格式）
    answer = db.Column(db.JSON)  # 答案（JSON格式）
    score = db.Column(db.Float, default=1.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 外键
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # 关系
    session = db.relationship('QuestionSession', backref=db.backref('temporary_questions', lazy='dynamic'))
    creator = db.relationship('User', backref=db.backref('temporary_questions', lazy='dynamic'))
    answers = db.relationship('StudentAnswer', backref='temporary_question', lazy='dynamic')
    
    def __repr__(self):
        return f'<TemporaryQuestion {self.id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'type': self.type,
            'content': self.content,
            'options': self.options,
            'score': self.score,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'created_by': self.created_by
        }

class ScreenshotQuestion(db.Model):
    """截屏题目模型，用于存储教师截屏作为题目"""
    __tablename__ = 'screenshot_questions'
    
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('question_sessions.id'))
    image_url = db.Column(db.String(200))  # 截屏图片URL
    description = db.Column(db.Text)  # 题目描述
    answer = db.Column(db.Text)  # 参考答案
    score = db.Column(db.Float, default=1.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 外键
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # 关系
    session = db.relationship('QuestionSession', backref=db.backref('screenshot_questions', lazy='dynamic'))
    creator = db.relationship('User', backref=db.backref('screenshot_questions', lazy='dynamic'))
    answers = db.relationship('StudentAnswer', backref='screenshot_question', lazy='dynamic')
    
    def __repr__(self):
        return f'<ScreenshotQuestion {self.id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'image_url': self.image_url,
            'description': self.description,
            'score': self.score,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'created_by': self.created_by
        }

class StudentAnswer(db.Model):
    """学生答案模型"""
    __tablename__ = 'student_answers'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    session_question_id = db.Column(db.Integer, db.ForeignKey('session_questions.id'), nullable=True)
    temporary_question_id = db.Column(db.Integer, db.ForeignKey('temporary_questions.id'), nullable=True)
    screenshot_question_id = db.Column(db.Integer, db.ForeignKey('screenshot_questions.id'), nullable=True)
    answer = db.Column(db.JSON)  # 学生答案（JSON格式）
    score = db.Column(db.Float)  # 得分
    is_correct = db.Column(db.Boolean)  # 是否正确
    submitted_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    student = db.relationship('User', backref=db.backref('answers', lazy='dynamic'))
    
    def __repr__(self):
        return f'<StudentAnswer {self.id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'student_id': self.student_id,
            'session_question_id': self.session_question_id,
            'temporary_question_id': self.temporary_question_id,
            'screenshot_question_id': self.screenshot_question_id,
            'answer': self.answer,
            'score': self.score,
            'is_correct': self.is_correct,
            'submitted_at': self.submitted_at.isoformat() if self.submitted_at else None
        }

class RandomPickRecord(db.Model):
    """随机点名记录模型"""
    __tablename__ = 'random_pick_records'
    
    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.Integer, db.ForeignKey('courses.id'))
    pick_type = db.Column(db.String(20))  # 'student' 或 'group'
    target_id = db.Column(db.Integer)  # 学生ID或小组ID
    picked_at = db.Column(db.DateTime, default=datetime.utcnow)
    score = db.Column(db.Float, nullable=True)  # 评分（可选）
    note = db.Column(db.Text, nullable=True)  # 备注（可选）
    scored_at = db.Column(db.DateTime, nullable=True)  # 评分时间（可选）
    scored_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # 评分教师ID（可选）
    
    # 关系
    course = db.relationship('Course', backref=db.backref('random_picks', lazy='dynamic'))
    scorer = db.relationship('User', backref=db.backref('given_pick_scores', lazy='dynamic'), foreign_keys=[scored_by])
    
    def __repr__(self):
        return f'<RandomPickRecord {self.id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'course_id': self.course_id,
            'pick_type': self.pick_type,
            'target_id': self.target_id,
            'picked_at': self.picked_at.isoformat() if self.picked_at else None,
            'score': self.score,
            'note': self.note,
            'scored_at': self.scored_at.isoformat() if self.scored_at else None,
            'scored_by': self.scored_by
        }

class GroupDiscussion(db.Model):
    """分组讨论模型"""
    __tablename__ = 'group_discussions'
    
    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.Integer, db.ForeignKey('courses.id'))
    title = db.Column(db.String(100))
    description = db.Column(db.Text)
    topic_content = db.Column(db.Text, nullable=True)  # 文本主题内容
    image_url = db.Column(db.String(200), nullable=True)  # 截屏图片URL
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    end_time = db.Column(db.DateTime)  # 讨论结束时间
    status = db.Column(db.String(20), default='active')  # 'active', 'ended'
    
    # 关系
    course = db.relationship('Course', backref=db.backref('discussions', lazy='dynamic'))
    creator = db.relationship('User', backref=db.backref('discussions', lazy='dynamic'))
    
    def __repr__(self):
        return f'<GroupDiscussion {self.id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'course_id': self.course_id,
            'title': self.title,
            'description': self.description,
            'topic_content': self.topic_content,
            'image_url': self.image_url,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status
        }
    
    @property
    def remaining_time(self):
        """计算剩余时间（秒）"""
        if self.end_time and self.end_time > datetime.utcnow():
            return (self.end_time - datetime.utcnow()).total_seconds()
        return 0