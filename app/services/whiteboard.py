from flask import current_app, request, session
from flask_socketio import Namespace, emit, join_room, leave_room
import json
import time
from ..models.user import User
from ..models.course import Group

class WhiteboardNamespace(Namespace):
    """处理白板协作的WebSocket命名空间"""
    
    def __init__(self, namespace=None):
        super().__init__(namespace)
        # 存储小组白板状态
        self.whiteboard_states = {}
        # 存储在线用户
        self.online_users = {}
        # 存储小组的用户
        self.group_users = {}
    
    def on_connect(self):
        """处理客户端连接"""
        user_id = request.args.get('user_id')
        user_name = request.args.get('user_name')
        group_id = request.args.get('group_id')
        
        if not user_id or not group_id:
            return False
        
        # Store context in session for disconnect handler
        session['user_id'] = user_id
        session['group_id'] = group_id

        # 将用户加入小组房间
        join_room(f'group_{group_id}')
        
        # 初始化小组数据结构
        if group_id not in self.group_users:
            self.group_users[group_id] = {}
        
        if group_id not in self.whiteboard_states:
            self.whiteboard_states[group_id] = []

        # 添加用户到在线列表
        self.group_users[group_id][user_id] = {
            'id': user_id,
            'name': user_name,
            'connected_at': time.time()
        }
        
        # 广播在线用户更新
        self._broadcast_online_users(group_id)
        
        current_app.logger.info(f'用户 {user_name} (ID: {user_id}) 加入了小组 {group_id} 的白板')
        return True
    
    def on_disconnect(self):
        """处理客户端断开连接"""
        user_id = session.get('user_id')
        group_id = session.get('group_id')
        
        if not user_id or not group_id:
            return
        
        # 从小组中移除用户
        if group_id in self.group_users and user_id in self.group_users[group_id]:
            user_name = self.group_users[group_id][user_id]['name']
            del self.group_users[group_id][user_id]
            
            # 广播在线用户更新
            self._broadcast_online_users(group_id)
            
            current_app.logger.info(f'用户 {user_name} (ID: {user_id}) 离开了小组 {group_id} 的白板')
        
        # 离开房间
        leave_room(f'group_{group_id}')
    
    def on_request_whiteboard_state(self, data):
        """处理请求白板状态"""
        group_id = data.get('group_id')
        
        if not group_id:
            return
        
        # 发送当前白板状态
        if group_id in self.whiteboard_states:
            emit('whiteboard_state', {
                'elements': self.whiteboard_states[group_id]
            })
    
    def on_whiteboard_operation(self, data):
        """处理白板操作（增量）"""
        group_id = data.get('group_id')
        operation = data.get('operation')
        
        if not group_id or not operation:
            return
        
        # 更新服务器端的白板状态
        self._update_whiteboard_state(group_id, operation)
        
        # 广播增量操作到小组
        emit('whiteboard_operation', {
            'operation': operation
        }, room=f'group_{group_id}', include_self=False)

    def _update_whiteboard_state(self, group_id, operation):
        """增量更新服务器上的白板状态"""
        if group_id not in self.whiteboard_states:
            self.whiteboard_states[group_id] = []

        current_elements = self.whiteboard_states[group_id]
        element_map = {el['id']: el for el in current_elements}

        # 处理删除的元素
        deleted_ids = operation.get('deletedIds', [])
        if deleted_ids:
            for el_id in deleted_ids:
                if el_id in element_map:
                    del element_map[el_id]

        # 处理更新或新增的元素
        updated_elements = operation.get('elements', [])
        if updated_elements:
            for element in updated_elements:
                element_map[element['id']] = element
        
        self.whiteboard_states[group_id] = list(element_map.values())

    def on_whiteboard_clear(self, data):
        """处理清除白板"""
        group_id = data.get('group_id')
        user_id = data.get('user_id')
        
        if not group_id or not user_id:
            return
        
        # 清除白板状态
        self.whiteboard_states[group_id] = []
        
        # 广播清除命令
        emit('whiteboard_state', {
            'elements': []
        }, room=f'group_{group_id}', include_self=True) # Include self to ensure clear
        
        # 获取用户名
        user_name = "未知用户"
        if group_id in self.group_users and user_id in self.group_users[group_id]:
            user_name = self.group_users[group_id][user_id]['name']
        
        current_app.logger.info(f'用户 {user_name} (ID: {user_id}) 清除了小组 {group_id} 的白板')
    
    def _broadcast_online_users(self, group_id):
        """广播在线用户列表"""
        if group_id in self.group_users:
            emit('online_users', {
                'users': list(self.group_users[group_id].values())
            }, room=f'group_{group_id}')
