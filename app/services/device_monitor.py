from app import db
from app.models.device import DeviceStatus
from datetime import datetime, timedelta
from flask import request
from flask_socketio import emit, join_room, leave_room
import json
import threading
import time

# 设备状态缓存
device_status_cache = {}

# 设备状态检查间隔（秒）
STATUS_CHECK_INTERVAL = 10

# 设备离线超时（秒）
OFFLINE_TIMEOUT = 30

def initialize_device_monitor(app, socketio):
    """
    初始化设备状态监控
    
    Args:
        app: Flask应用实例
        socketio: SocketIO实例
    """
    # 设备状态更新处理程序
    @socketio.on('device_status_update')
    def handle_device_status_update(data):
        """
        处理设备状态更新
        
        Args:
            data: 设备状态数据
        """
        device_id = data.get('device_id')
        if not device_id:
            return {'error': '缺少设备ID'}, 400
        
        # 更新设备状态
        device = DeviceStatus.query.filter_by(device_id=device_id).first()
        
        if not device:
            # 如果设备不存在，创建新设备
            device = DeviceStatus(
                device_id=device_id,
                type=data.get('type', 'student'),
                owner_id=data.get('owner_id'),
                status=data.get('status', 'online'),
                ip_address=request.remote_addr
            )
            db.session.add(device)
        else:
            # 更新现有设备状态
            device.status = data.get('status', device.status)
            device.ip_address = request.remote_addr
            device.last_ping = datetime.utcnow()
        
        db.session.commit()
        
        # 更新缓存
        device_status_cache[device_id] = device.to_dict()
        
        # 广播设备状态更新
        emit('device_status_changed', device.to_dict(), broadcast=True, room='device_monitor')
        
        return {'success': True}
    
    @socketio.on('join_device_monitor')
    def handle_join_device_monitor():
        """
        加入设备监控房间
        """
        join_room('device_monitor')
        
        # 发送当前所有设备状态
        devices = DeviceStatus.query.all()
        device_list = [device.to_dict() for device in devices]
        emit('device_status_list', {'devices': device_list})
    
    @socketio.on('leave_device_monitor')
    def handle_leave_device_monitor():
        """
        离开设备监控房间
        """
        leave_room('device_monitor')
    
    # 启动设备状态检查线程
    def check_device_status():
        """
        定期检查设备状态，将长时间未更新的设备标记为离线
        """
        with app.app_context():
            while True:
                try:
                    # 获取所有设备
                    devices = DeviceStatus.query.all()
                    offline_threshold = datetime.utcnow() - timedelta(seconds=OFFLINE_TIMEOUT)
                    
                    for device in devices:
                        # 如果设备长时间未更新状态，标记为离线
                        if device.last_ping < offline_threshold and device.status != 'offline':
                            device.status = 'offline'
                            db.session.commit()
                            
                            # 更新缓存
                            device_status_cache[device.device_id] = device.to_dict()
                            
                            # 广播设备状态更新
                            socketio.emit('device_status_changed', device.to_dict(), room='device_monitor')
                    
                    # 等待下一次检查
                    time.sleep(STATUS_CHECK_INTERVAL)
                except Exception as e:
                    print(f"设备状态检查错误: {e}")
                    time.sleep(STATUS_CHECK_INTERVAL)
    
    # 启动后台线程
    status_thread = threading.Thread(target=check_device_status)
    status_thread.daemon = True
    status_thread.start()

def get_device_status(device_id):
    """
    获取设备状态
    
    Args:
        device_id: 设备ID
    
    Returns:
        设备状态字典
    """
    # 优先从缓存获取
    if device_id in device_status_cache:
        return device_status_cache[device_id]
    
    # 从数据库获取
    device = DeviceStatus.query.filter_by(device_id=device_id).first()
    if device:
        # 更新缓存
        device_status_cache[device_id] = device.to_dict()
        return device.to_dict()
    
    return None

def get_all_device_status():
    """
    获取所有设备状态
    
    Returns:
        设备状态列表
    """
    devices = DeviceStatus.query.all()
    return [device.to_dict() for device in devices]