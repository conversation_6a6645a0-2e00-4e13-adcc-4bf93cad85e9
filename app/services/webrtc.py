"""
WebRTC服务模块，提供信令服务器和P2P连接功能
"""
from flask import current_app, request
from flask_socketio import emit, join_room, leave_room
import uuid
import logging
from datetime import datetime

# 设置日志
logger = logging.getLogger(__name__)

# 存储活跃的房间和连接
active_rooms = {}
active_connections = {}

# 存储教师选择的学生画面
selected_student_screens = {}

# 存储暂停的画面
paused_screens = {}

# 存储小组广播信息
group_broadcasts = {}

def initialize_webrtc_handlers(socketio):
    """
    初始化WebRTC相关的Socket.IO事件处理器
    
    Args:
        socketio: Flask-SocketIO实例
    """
    @socketio.on('connect')
    def handle_connect():
        """处理客户端连接"""
        client_id = request.sid
        active_connections[client_id] = {
            'rooms': [],
            'user_id': None,
            'user_type': None,
            'group_id': None
        }
        logger.info(f"Client connected: {client_id}")
        emit('connected', {'client_id': client_id})
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """处理客户端断开连接"""
        client_id = get_client_id()
        if client_id in active_connections:
            # 从所有房间中移除客户端
            for room_id in list(active_connections.get(client_id, {}).get('rooms', [])):
                leave_room(room_id)
                
                if room_id in active_rooms and client_id in active_rooms[room_id]['clients']:
                    active_rooms[room_id]['clients'].remove(client_id)
                    
                    # 如果离开的是教师，清除教师ID并停止广播
                    if active_rooms[room_id].get('teacher_id') == client_id:
                        active_rooms[room_id]['teacher_id'] = None
                        if active_rooms[room_id].get('broadcaster_id') == client_id:
                            active_rooms[room_id]['broadcaster_id'] = None
                            emit('broadcast_stopped', {'broadcaster_id': client_id}, room=room_id, include_self=False)

                    # 通知房间中的其他客户端
                    emit('peer_left', {'client_id': client_id}, room=room_id, include_self=False)
                    
                    # 如果房间为空，则删除房间
                    if not active_rooms[room_id]['clients']:
                        logger.info(f"Room {room_id} is empty, deleting.")
                        if room_id in active_rooms:
                            del active_rooms[room_id]
            
            # 删除客户端连接记录
            del active_connections[client_id]
            logger.info(f"Client disconnected: {client_id}")
    
    @socketio.on('join_room')
    def handle_join_room(data):
        """
        处理客户端加入房间请求
        
        Args:
            data: 包含房间ID和用户信息的字典
        """
        client_id = get_client_id()
        room_id = data.get('room_id')
        user_id = data.get('user_id')
        user_type = data.get('user_type')  # 'teacher', 'student' 或 'group'
        group_id = data.get('group_id')  # 小组ID，仅当user_type为'group'时有效
        
        if not room_id:
            emit('error', {'message': '房间ID不能为空'})
            return
        
        # 更新客户端信息
        if client_id in active_connections:
            active_connections[client_id]['user_id'] = user_id
            active_connections[client_id]['user_type'] = user_type
            if group_id:
                active_connections[client_id]['group_id'] = group_id
            
            # 将客户端加入房间
            join_room(room_id)
            
            # 如果房间不存在，则创建
            if room_id not in active_rooms:
                active_rooms[room_id] = {
                    'clients': [],
                    'teacher_id': None,
                    'group_ids': {}  # 存储小组ID和对应的客户端ID
                }
            
            # 如果是教师，记录教师ID
            if user_type == 'teacher':
                active_rooms[room_id]['teacher_id'] = client_id
            
            # 检查是否有正在进行的广播，并通知新加入的学生
            if active_rooms[room_id].get('broadcaster_id') and user_type == 'student':
                emit('broadcast_started', {'broadcaster_id': active_rooms[room_id]['broadcaster_id']})
            
            # 如果是小组，记录小组ID
            if user_type == 'group' and group_id:
                active_rooms[room_id]['group_ids'][group_id] = client_id
            
            # 将客户端添加到房间
            if client_id not in active_rooms[room_id]['clients']:
                active_rooms[room_id]['clients'].append(client_id)
                active_connections[client_id]['rooms'].append(room_id)
            
            # 通知房间中的其他客户端
            emit('peer_joined', {
                'client_id': client_id,
                'user_id': user_id,
                'user_type': user_type,
                'group_id': group_id
            }, room=room_id, include_self=False)
            
            # 向新加入的客户端发送房间中的其他客户端信息
            peers = []
            for peer_id in active_rooms[room_id]['clients']:
                if peer_id != client_id and peer_id in active_connections:
                    peers.append({
                        'client_id': peer_id,
                        'user_id': active_connections[peer_id]['user_id'],
                        'user_type': active_connections[peer_id]['user_type'],
                        'group_id': active_connections[peer_id].get('group_id')
                    })
            
            emit('room_joined', {
                'room_id': room_id,
                'peers': peers,
                'teacher_id': active_rooms[room_id]['teacher_id']
            })
            
            logger.info(f"Client {client_id} joined room {room_id}")
    
    @socketio.on('leave_room')
    def handle_leave_room(data):
        """
        处理客户端离开房间请求
        
        Args:
            data: 包含房间ID的字典
        """
        client_id = get_client_id()
        room_id = data.get('room_id')
        
        if not room_id:
            emit('error', {'message': '房间ID不能为空'})
            return
        
        if client_id in active_connections and room_id in active_connections[client_id]['rooms']:
            # 从房间中移除客户端
            active_connections[client_id]['rooms'].remove(room_id)
            leave_room(room_id)
            
            if room_id in active_rooms and client_id in active_rooms[room_id]['clients']:
                active_rooms[room_id]['clients'].remove(client_id)
                
                # 如果离开的是教师，清除教师ID
                if active_rooms[room_id]['teacher_id'] == client_id:
                    active_rooms[room_id]['teacher_id'] = None
                
                # 如果离开的是小组，清除小组ID
                user_type = active_connections[client_id]['user_type']
                if user_type == 'group':
                    group_id = active_connections[client_id].get('group_id')
                    if group_id and group_id in active_rooms[room_id]['group_ids']:
                        del active_rooms[room_id]['group_ids'][group_id]
                        
                        # 如果该小组正在广播，停止广播
                        if room_id in group_broadcasts and group_id in group_broadcasts[room_id]:
                            del group_broadcasts[room_id][group_id]
                            # 通知房间中的其他客户端
                            emit('group_broadcast_stopped', {
                                'group_id': group_id,
                                'client_id': client_id
                            }, room=room_id)
                
                # 通知房间中的其他客户端
                emit('peer_left', {'client_id': client_id}, room=room_id)
                
                # 如果房间为空，则删除房间
                if not active_rooms[room_id]['clients']:
                    del active_rooms[room_id]
            
            logger.info(f"Client {client_id} left room {room_id}")
    
    @socketio.on('offer')
    def handle_offer(data):
        """
        处理WebRTC offer
        
        Args:
            data: 包含offer和目标客户端ID的字典
        """
        client_id = get_client_id()
        target_id = data.get('target_id')
        offer = data.get('offer')
        
        if not target_id or not offer:
            emit('error', {'message': '目标ID和offer不能为空'})
            return
        
        # 转发offer给目标客户端
        emit('offer', {
            'offer': offer,
            'source_id': client_id
        }, room=target_id)
        
        logger.debug(f"Forwarded offer from {client_id} to {target_id}")
    
    @socketio.on('answer')
    def handle_answer(data):
        """
        处理WebRTC answer
        
        Args:
            data: 包含answer和目标客户端ID的字典
        """
        client_id = get_client_id()
        target_id = data.get('target_id')
        answer = data.get('answer')
        
        if not target_id or not answer:
            emit('error', {'message': '目标ID和answer不能为空'})
            return
        
        # 转发answer给目标客户端
        emit('answer', {
            'answer': answer,
            'source_id': client_id
        }, room=target_id)
        
        logger.debug(f"Forwarded answer from {client_id} to {target_id}")
    
    @socketio.on('ice_candidate')
    def handle_ice_candidate(data):
        """
        处理ICE候选
        
        Args:
            data: 包含ICE候选和目标客户端ID的字典
        """
        client_id = get_client_id()
        target_id = data.get('target_id')
        candidate = data.get('candidate')
        
        if not target_id or not candidate:
            emit('error', {'message': '目标ID和candidate不能为空'})
            return
        
        # 转发ICE候选给目标客户端
        emit('ice_candidate', {
            'candidate': candidate,
            'source_id': client_id
        }, room=target_id)
        
        logger.debug(f"Forwarded ICE candidate from {client_id} to {target_id}")
    
    @socketio.on('broadcast_request')
    def handle_broadcast_request(data):
        """
        处理广播请求
        
        Args:
            data: 包含房间ID的字典
        """
        client_id = get_client_id()
        room_id = data.get('room_id')
        
        if not room_id:
            emit('error', {'message': '房间ID不能为空'})
            return
        
        if room_id in active_rooms:
            # 检查请求者是否为教师
            if active_rooms[room_id].get('teacher_id') == client_id:
                # 记录广播者ID
                active_rooms[room_id]['broadcaster_id'] = client_id
                # 通知房间中的所有其他客户端
                emit('broadcast_started', {'broadcaster_id': client_id}, room=room_id, include_self=False)
                logger.info(f"Broadcast started in room {room_id} by teacher {client_id}")
            else:
                emit('error', {'message': '只有教师可以发起广播'})
        else:
            emit('error', {'message': '房间不存在'})
    
    @socketio.on('stop_broadcast')
    def handle_stop_broadcast(data):
        """
        处理停止广播请求
        
        Args:
            data: 包含房间ID的字典
        """
        client_id = get_client_id()
        room_id = data.get('room_id')
        
        if not room_id:
            emit('error', {'message': '房间ID不能为空'})
            return
        
        if room_id in active_rooms:
            # 检查请求者是否为教师
            if active_rooms[room_id].get('teacher_id') == client_id:
                # 清除广播者ID
                active_rooms[room_id]['broadcaster_id'] = None
                # 通知房间中的所有其他客户端
                emit('broadcast_stopped', {'broadcaster_id': client_id}, room=room_id, include_self=False)
                logger.info(f"Broadcast stopped in room {room_id} by teacher {client_id}")
            else:
                emit('error', {'message': '只有教师可以停止广播'})
        else:
            emit('error', {'message': '房间不存在'})
            
    @socketio.on('select_student_screen')
    def handle_select_student_screen(data):
        """
        处理教师选择学生画面请求
        
        Args:
            data: 包含房间ID和学生客户端ID的字典
        """
        client_id = get_client_id()
        room_id = data.get('room_id')
        student_id = data.get('student_id')
        
        if not room_id or not student_id:
            emit('error', {'message': '房间ID和学生ID不能为空'})
            return
        
        if room_id in active_rooms:
            # 检查请求者是否为教师
            if active_rooms[room_id]['teacher_id'] == client_id:
                # 检查学生是否在房间中
                if student_id in active_rooms[room_id]['clients']:
                    # 初始化房间的已选择学生画面列表
                    if room_id not in selected_student_screens:
                        selected_student_screens[room_id] = []
                    
                    # 添加学生ID到已选择列表
                    if student_id not in selected_student_screens[room_id]:
                        selected_student_screens[room_id].append(student_id)
                    
                    # 通知教师
                    emit('student_screen_selected', {
                        'student_id': student_id,
                        'selected_screens': selected_student_screens[room_id]
                    })
                    
                    # 通知被选择的学生
                    emit('screen_selected_by_teacher', {
                        'teacher_id': client_id
                    }, room=student_id)
                    
                    logger.info(f"Teacher {client_id} selected student {student_id} screen in room {room_id}")
                else:
                    emit('error', {'message': '学生不在房间中'})
            else:
                emit('error', {'message': '只有教师可以选择学生画面'})
        else:
            emit('error', {'message': '房间不存在'})
    
    @socketio.on('deselect_student_screen')
    def handle_deselect_student_screen(data):
        """
        处理教师取消选择学生画面请求
        
        Args:
            data: 包含房间ID和学生客户端ID的字典
        """
        client_id = get_client_id()
        room_id = data.get('room_id')
        student_id = data.get('student_id')
        
        if not room_id or not student_id:
            emit('error', {'message': '房间ID和学生ID不能为空'})
            return
        
        if room_id in active_rooms:
            # 检查请求者是否为教师
            if active_rooms[room_id]['teacher_id'] == client_id:
                # 从已选择列表中移除学生ID
                if room_id in selected_student_screens and student_id in selected_student_screens[room_id]:
                    selected_student_screens[room_id].remove(student_id)
                    
                    # 通知教师
                    emit('student_screen_deselected', {
                        'student_id': student_id,
                        'selected_screens': selected_student_screens[room_id]
                    })
                    
                    # 通知被取消选择的学生
                    emit('screen_deselected_by_teacher', {
                        'teacher_id': client_id
                    }, room=student_id)
                    
                    logger.info(f"Teacher {client_id} deselected student {student_id} screen in room {room_id}")
                else:
                    emit('error', {'message': '学生画面未被选择'})
            else:
                emit('error', {'message': '只有教师可以取消选择学生画面'})
        else:
            emit('error', {'message': '房间不存在'})
    
    @socketio.on('compare_student_screens')
    def handle_compare_student_screens(data):
        """
        处理教师比较学生画面请求
        
        Args:
            data: 包含房间ID和学生客户端ID列表的字典
        """
        client_id = get_client_id()
        room_id = data.get('room_id')
        student_ids = data.get('student_ids', [])
        
        if not room_id or not student_ids:
            emit('error', {'message': '房间ID和学生ID列表不能为空'})
            return
        
        if room_id in active_rooms:
            # 检查请求者是否为教师
            if active_rooms[room_id]['teacher_id'] == client_id:
                # 验证所有学生都在房间中
                valid_students = [sid for sid in student_ids if sid in active_rooms[room_id]['clients']]
                
                if valid_students:
                    # 更新已选择的学生画面列表
                    selected_student_screens[room_id] = valid_students
                    
                    # 通知教师
                    emit('student_screens_comparison', {
                        'student_ids': valid_students
                    })
                    
                    # 通知被选择的学生
                    for student_id in valid_students:
                        emit('screen_selected_for_comparison', {
                            'teacher_id': client_id
                        }, room=student_id)
                    
                    logger.info(f"Teacher {client_id} comparing student screens {valid_students} in room {room_id}")
                else:
                    emit('error', {'message': '没有有效的学生ID'})
            else:
                emit('error', {'message': '只有教师可以比较学生画面'})
        else:
            emit('error', {'message': '房间不存在'})
            
    @socketio.on('pause_screen')
    def handle_pause_screen(data):
        """
        处理教师暂停学生画面请求
        
        Args:
            data: 包含房间ID和学生客户端ID的字典
        """
        client_id = get_client_id()
        room_id = data.get('room_id')
        student_id = data.get('student_id')
        
        if not room_id or not student_id:
            emit('error', {'message': '房间ID和学生ID不能为空'})
            return
        
        if room_id in active_rooms:
            # 检查请求者是否为教师
            if active_rooms[room_id]['teacher_id'] == client_id:
                # 检查学生是否在房间中
                if student_id in active_rooms[room_id]['clients']:
                    # 初始化房间的已暂停画面列表
                    if room_id not in paused_screens:
                        paused_screens[room_id] = []
                    
                    # 添加学生ID到已暂停列表
                    if student_id not in paused_screens[room_id]:
                        paused_screens[room_id].append(student_id)
                    
                    # 通知教师
                    emit('screen_paused', {
                        'student_id': student_id,
                        'paused_screens': paused_screens[room_id]
                    })
                    
                    # 通知被暂停的学生
                    emit('screen_paused_by_teacher', {
                        'teacher_id': client_id
                    }, room=student_id)
                    
                    logger.info(f"Teacher {client_id} paused student {student_id} screen in room {room_id}")
                else:
                    emit('error', {'message': '学生不在房间中'})
            else:
                emit('error', {'message': '只有教师可以暂停学生画面'})
        else:
            emit('error', {'message': '房间不存在'})
    
    @socketio.on('resume_screen')
    def handle_resume_screen(data):
        """
        处理教师恢复学生画面请求
        
        Args:
            data: 包含房间ID和学生客户端ID的字典
        """
        client_id = get_client_id()
        room_id = data.get('room_id')
        student_id = data.get('student_id')
        
        if not room_id or not student_id:
            emit('error', {'message': '房间ID和学生ID不能为空'})
            return
        
        if room_id in active_rooms:
            # 检查请求者是否为教师
            if active_rooms[room_id]['teacher_id'] == client_id:
                # 从已暂停列表中移除学生ID
                if room_id in paused_screens and student_id in paused_screens[room_id]:
                    paused_screens[room_id].remove(student_id)
                    
                    # 通知教师
                    emit('screen_resumed', {
                        'student_id': student_id,
                        'paused_screens': paused_screens[room_id]
                    })
                    
                    # 通知被恢复的学生
                    emit('screen_resumed_by_teacher', {
                        'teacher_id': client_id
                    }, room=student_id)
                    
                    logger.info(f"Teacher {client_id} resumed student {student_id} screen in room {room_id}")
                else:
                    emit('error', {'message': '学生画面未被暂停'})
            else:
                emit('error', {'message': '只有教师可以恢复学生画面'})
        else:
            emit('error', {'message': '房间不存在'})
            
    @socketio.on('group_broadcast_request')
    def handle_group_broadcast_request(data):
        """
        处理小组广播请求
        
        Args:
            data: 包含房间ID、小组ID和目标类型的字典
        """
        client_id = get_client_id()
        room_id = data.get('room_id')
        group_id = data.get('group_id')
        target_type = data.get('target_type', 'teacher')  # 'teacher' 或 'all_groups'
        
        if not room_id or not group_id:
            emit('error', {'message': '房间ID和小组ID不能为空'})
            return
        
        if room_id in active_rooms:
            # 检查请求者是否为小组成员
            if client_id in active_connections and active_connections[client_id]['user_type'] == 'group':
                # 初始化房间的小组广播信息
                if room_id not in group_broadcasts:
                    group_broadcasts[room_id] = {}
                
                # 记录小组广播信息
                group_broadcasts[room_id][group_id] = {
                    'client_id': client_id,
                    'target_type': target_type,
                    'started_at': datetime.utcnow().isoformat()
                }
                
                # 通知房间中的目标客户端
                if target_type == 'teacher':
                    # 只通知教师
                    teacher_id = active_rooms[room_id].get('teacher_id')
                    if teacher_id:
                        emit('group_broadcast_started', {
                            'group_id': group_id,
                            'client_id': client_id
                        }, room=teacher_id)
                else:
                    # 通知所有小组和教师
                    emit('group_broadcast_started', {
                        'group_id': group_id,
                        'client_id': client_id
                    }, room=room_id)
                
                # 通知发起广播的小组
                emit('group_broadcast_started', {
                    'group_id': group_id,
                    'client_id': client_id,
                    'target_type': target_type
                })
                
                logger.info(f"Group {group_id} (client {client_id}) started broadcast in room {room_id} to {target_type}")
            else:
                emit('error', {'message': '只有小组成员可以发起小组广播'})
        else:
            emit('error', {'message': '房间不存在'})
    
    @socketio.on('stop_group_broadcast')
    def handle_stop_group_broadcast(data):
        """
        处理停止小组广播请求
        
        Args:
            data: 包含房间ID和小组ID的字典
        """
        client_id = get_client_id()
        room_id = data.get('room_id')
        group_id = data.get('group_id')
        
        if not room_id or not group_id:
            emit('error', {'message': '房间ID和小组ID不能为空'})
            return
        
        if room_id in active_rooms and room_id in group_broadcasts:
            # 检查请求者是否为小组成员
            if client_id in active_connections and active_connections[client_id]['user_type'] == 'group':
                # 检查小组是否正在广播
                if group_id in group_broadcasts[room_id]:
                    # 获取广播目标类型
                    target_type = group_broadcasts[room_id][group_id].get('target_type', 'teacher')
                    
                    # 移除小组广播信息
                    del group_broadcasts[room_id][group_id]
                    
                    # 通知房间中的目标客户端
                    if target_type == 'teacher':
                        # 只通知教师
                        teacher_id = active_rooms[room_id].get('teacher_id')
                        if teacher_id:
                            emit('group_broadcast_stopped', {
                                'group_id': group_id,
                                'client_id': client_id
                            }, room=teacher_id)
                    else:
                        # 通知所有小组和教师
                        emit('group_broadcast_stopped', {
                            'group_id': group_id,
                            'client_id': client_id
                        }, room=room_id)
                    
                    # 通知发起广播的小组
                    emit('group_broadcast_stopped', {
                        'group_id': group_id,
                        'client_id': client_id
                    })
                    
                    logger.info(f"Group {group_id} (client {client_id}) stopped broadcast in room {room_id}")
                else:
                    emit('error', {'message': '小组未在广播'})
            else:
                emit('error', {'message': '只有小组成员可以停止小组广播'})
        else:
            emit('error', {'message': '房间不存在或没有小组广播'})

def get_client_id():
    """
    获取当前连接的客户端ID
    
    Returns:
        str: 客户端ID
    """
    return request.sid

def get_room_info(room_id):
    """
    获取房间信息
    
    Args:
        room_id: 房间ID
    
    Returns:
        dict: 房间信息
    """
    if room_id in active_rooms:
        return {
            'room_id': room_id,
            'clients': active_rooms[room_id]['clients'],
            'teacher_id': active_rooms[room_id]['teacher_id'],
            'group_ids': active_rooms[room_id].get('group_ids', {}),
            'selected_screens': selected_student_screens.get(room_id, []),
            'paused_screens': paused_screens.get(room_id, []),
            'group_broadcasts': group_broadcasts.get(room_id, {})
        }
    return None

def get_selected_student_screens(room_id):
    """
    获取教师选择的学生画面列表
    
    Args:
        room_id: 房间ID
    
    Returns:
        list: 已选择的学生客户端ID列表
    """
    return selected_student_screens.get(room_id, [])

def get_paused_screens(room_id):
    """
    获取教师暂停的学生画面列表
    
    Args:
        room_id: 房间ID
    
    Returns:
        list: 已暂停的学生客户端ID列表
    """
    return paused_screens.get(room_id, [])

def get_group_broadcasts(room_id):
    """
    获取房间中的小组广播信息
    
    Args:
        room_id: 房间ID
    
    Returns:
        dict: 小组广播信息
    """
    return group_broadcasts.get(room_id, {})

def create_course_room(course_id):
    """
    为课程创建WebRTC房间
    
    Args:
        course_id: 课程ID
    
    Returns:
        str: 房间ID
    """
    room_id = f"course_{course_id}"
    if room_id not in active_rooms:
        active_rooms[room_id] = {
            'clients': [],
            'teacher_id': None,
            'group_ids': {}
        }
    return room_id