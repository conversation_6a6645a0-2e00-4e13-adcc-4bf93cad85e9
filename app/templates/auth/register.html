{% extends "base.html" %}

{% block title %}注册 - 智慧教室系统{% endblock %}

{% block content %}
<div class="hand-drawn" style="max-width: 500px; margin: 2rem auto;">
    <h1>用户注册</h1>
    <form method="POST" action="{{ url_for('auth.register') }}">
        <div class="form-group">
            <label for="username" class="form-label">用户名</label>
            <input type="text" class="form-control" id="username" name="username" 
                   value="{{ request.form.username if request.form.username else '' }}" required>
        </div>
        <div class="form-group">
            <label for="email" class="form-label">邮箱</label>
            <input type="email" class="form-control" id="email" name="email" 
                   value="{{ request.form.email if request.form.email else '' }}" required>
        </div>
        <div class="form-group">
            <label for="password" class="form-label">密码</label>
            <input type="password" class="form-control" id="password" name="password" required>
            <small class="form-text">密码长度至少6位</small>
        </div>
        <div class="form-group">
            <label for="password2" class="form-label">确认密码</label>
            <input type="password" class="form-control" id="password2" name="password2" required>
        </div>
        <div class="form-group">
            <label>角色</label>
            <div>
                <label>
                    <input type="radio" name="role" value="teacher" 
                           {{ 'checked' if request.form.role == 'teacher' else '' }}> 教师
                </label>
                <label style="margin-left: 1rem;">
                    <input type="radio" name="role" value="student" 
                           {{ 'checked' if not request.form.role or request.form.role == 'student' else '' }}> 学生
                </label>
            </div>
        </div>
        <button type="submit" class="btn-hand-drawn">注册</button>
    </form>
    <p style="margin-top: 1rem;">
        已有账号？<a href="{{ url_for('auth.login') }}">立即登录</a>
    </p>
</div>
{% endblock %}