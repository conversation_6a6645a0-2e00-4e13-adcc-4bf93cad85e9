{% extends "base.html" %}

{% block title %}登录 - 智慧教室系统{% endblock %}

{% block content %}
<div class="hand-drawn" style="max-width: 500px; margin: 2rem auto;">
    <h1>用户登录</h1>
    <form method="POST" action="{{ url_for('auth.login') }}">
        <div class="form-group">
            <label for="email" class="form-label">邮箱</label>
            <input type="email" class="form-control" id="email" name="email" 
                   value="{{ request.form.email if request.form.email else '' }}" required>
        </div>
        <div class="form-group">
            <label for="password" class="form-label">密码</label>
            <input type="password" class="form-control" id="password" name="password" required>
        </div>
        <div class="form-group">
            <label>
                <input type="checkbox" name="remember_me" 
                       {{ 'checked' if request.form.remember_me else '' }}> 记住我
            </label>
        </div>
        <button type="submit" class="btn-hand-drawn">登录</button>
    </form>
    <p style="margin-top: 1rem;">
        还没有账号？<a href="{{ url_for('auth.register') }}">立即注册</a>
    </p>
</div>
{% endblock %}