{% extends 'base.html' %}

{% block title %}课程安排{% endblock %}

{% block styles %}
{{ super() }}
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css" rel="stylesheet">
<style>
    .fc-event {
        cursor: pointer;
    }
    .plan-details {
        margin-top: 20px;
    }
    .resource-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        margin-bottom: 5px;
        border: 1px solid #eee;
        border-radius: 4px;
    }
    .resource-type {
        background-color: #f8f9fa;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 0.9rem;
        margin-right: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="hand-drawn">课程安排</h1>
        <div>
            <button type="button" class="btn-hand-drawn me-2" data-bs-toggle="modal" data-bs-target="#createPlanModal">创建安排</button>
            <a href="{{ url_for('course.index') }}" class="btn-hand-drawn">返回课程</a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card hand-drawn-border">
                <div class="card-header hand-drawn">
                    <h5 class="mb-0">课程日历</h5>
                </div>
                <div class="card-body">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card hand-drawn-border">
                <div class="card-header hand-drawn">
                    <h5 class="mb-0">安排详情</h5>
                </div>
                <div class="card-body">
                    <div id="plan-details" class="plan-details">
                        <div class="text-center text-muted py-4">
                            <p>点击日历中的安排查看详情</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建课程安排模态框 -->
<div class="modal fade" id="createPlanModal" tabindex="-1" aria-labelledby="createPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createPlanModalLabel">创建课程安排</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="create-plan-form">
                    <div class="mb-3">
                        <label for="plan-title" class="form-label">标题</label>
                        <input type="text" class="form-control hand-drawn-border" id="plan-title" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="plan-description" class="form-label">描述</label>
                        <textarea class="form-control hand-drawn-border" id="plan-description" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="plan-start-time" class="form-label">开始时间</label>
                            <input type="datetime-local" class="form-control hand-drawn-border" id="plan-start-time" name="start_time" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="plan-end-time" class="form-label">结束时间</label>
                            <input type="datetime-local" class="form-control hand-drawn-border" id="plan-end-time" name="end_time" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="plan-location" class="form-label">地点</label>
                        <input type="text" class="form-control hand-drawn-border" id="plan-location" name="location">
                    </div>
                    <div class="mb-3">
                        <label for="plan-course" class="form-label">课程</label>
                        <select class="form-select hand-drawn-border" id="plan-course" name="course_id" required>
                            <option value="">请选择课程</option>
                            {% for course in courses %}
                                <option value="{{ course.id }}">{{ course.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">资源</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">课件</h6>
                                    </div>
                                    <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                        <div id="resources-list">
                                            {% if resources %}
                                                {% for resource in resources %}
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="resources" value="{{ resource.id }}" id="resource-{{ resource.id }}">
                                                        <label class="form-check-label" for="resource-{{ resource.id }}">
                                                            {{ resource.name }}
                                                        </label>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <p class="text-muted">暂无课件</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">试卷</h6>
                                    </div>
                                    <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                        <div id="exams-list">
                                            {% if exams %}
                                                {% for exam in exams %}
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="exams" value="{{ exam.id }}" id="exam-{{ exam.id }}">
                                                        <label class="form-check-label" for="exam-{{ exam.id }}">
                                                            {{ exam.name }}
                                                        </label>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <p class="text-muted">暂无试卷</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="save-plan-btn">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑课程安排模态框 -->
<div class="modal fade" id="editPlanModal" tabindex="-1" aria-labelledby="editPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPlanModalLabel">编辑课程安排</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="edit-plan-form">
                    <input type="hidden" id="edit-plan-id" name="id">
                    <div class="mb-3">
                        <label for="edit-plan-title" class="form-label">标题</label>
                        <input type="text" class="form-control hand-drawn-border" id="edit-plan-title" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit-plan-description" class="form-label">描述</label>
                        <textarea class="form-control hand-drawn-border" id="edit-plan-description" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit-plan-start-time" class="form-label">开始时间</label>
                            <input type="datetime-local" class="form-control hand-drawn-border" id="edit-plan-start-time" name="start_time" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit-plan-end-time" class="form-label">结束时间</label>
                            <input type="datetime-local" class="form-control hand-drawn-border" id="edit-plan-end-time" name="end_time" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit-plan-location" class="form-label">地点</label>
                        <input type="text" class="form-control hand-drawn-border" id="edit-plan-location" name="location">
                    </div>
                    <div class="mb-3">
                        <label for="edit-plan-course" class="form-label">课程</label>
                        <select class="form-select hand-drawn-border" id="edit-plan-course" name="course_id" required>
                            <option value="">请选择课程</option>
                            {% for course in courses %}
                                <option value="{{ course.id }}">{{ course.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">资源</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">课件</h6>
                                    </div>
                                    <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                        <div id="edit-resources-list">
                                            {% if resources %}
                                                {% for resource in resources %}
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="resources" value="{{ resource.id }}" id="edit-resource-{{ resource.id }}">
                                                        <label class="form-check-label" for="edit-resource-{{ resource.id }}">
                                                            {{ resource.name }}
                                                        </label>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <p class="text-muted">暂无课件</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">试卷</h6>
                                    </div>
                                    <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                        <div id="edit-exams-list">
                                            {% if exams %}
                                                {% for exam in exams %}
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="exams" value="{{ exam.id }}" id="edit-exam-{{ exam.id }}">
                                                        <label class="form-check-label" for="edit-exam-{{ exam.id }}">
                                                            {{ exam.name }}
                                                        </label>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <p class="text-muted">暂无试卷</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger me-auto" id="delete-plan-btn">删除</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="update-plan-btn">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/locales-all.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化日历
        const calendarEl = document.getElementById('calendar');
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'zh-cn',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            events: '/interaction/api/course_plans',
            eventClick: function(info) {
                showPlanDetails(info.event.id);
            }
        });
        calendar.render();
        
        // 显示课程安排详情
        function showPlanDetails(planId) {
            fetch(`/interaction/api/course_plans/${planId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const plan = data.plan;
                        const detailsContainer = document.getElementById('plan-details');
                        
                        // 格式化日期
                        const startDate = new Date(plan.start_time);
                        const endDate = new Date(plan.end_time);
                        const formatDate = (date) => {
                            return date.toLocaleString('zh-CN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                        };
                        
                        // 构建详情HTML
                        let detailsHtml = `
                            <h4>${plan.title}</h4>
                            <p class="text-muted">${plan.course ? plan.course.name : '未指定课程'}</p>
                            <hr>
                            <p><strong>时间:</strong> ${formatDate(startDate)} - ${formatDate(endDate)}</p>
                            <p><strong>地点:</strong> ${plan.location || '未指定'}</p>
                            <p><strong>描述:</strong> ${plan.description || '无'}</p>
                        `;
                        
                        // 添加资源列表
                        if (plan.resources && plan.resources.length > 0) {
                            detailsHtml += `<h5 class="mt-3">相关资源</h5>`;
                            detailsHtml += `<div class="list-group">`;
                            
                            plan.resources.forEach(resource => {
                                let typeText = '';
                                let typeClass = '';
                                
                                if (resource.type === 'resource') {
                                    typeText = '课件';
                                    typeClass = 'bg-info';
                                } else if (resource.type === 'exam') {
                                    typeText = '试卷';
                                    typeClass = 'bg-warning';
                                }
                                
                                detailsHtml += `
                                    <div class="resource-item">
                                        <div>
                                            <span class="resource-type ${typeClass}">${typeText}</span>
                                            ${resource.name}
                                        </div>
                                    </div>
                                `;
                            });
                            
                            detailsHtml += `</div>`;
                        }
                        
                        // 添加编辑按钮
                        detailsHtml += `
                            <div class="mt-3">
                                <button class="btn btn-outline-primary btn-sm edit-plan-btn" data-id="${plan.id}">
                                    编辑
                                </button>
                            </div>
                        `;
                        
                        detailsContainer.innerHTML = detailsHtml;
                        
                        // 添加编辑按钮事件监听器
                        document.querySelector('.edit-plan-btn').addEventListener('click', function() {
                            openEditModal(plan.id);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('plan-details').innerHTML = '<div class="alert alert-danger">加载详情失败</div>';
                });
        }
        
        // 打开编辑模态框
        function openEditModal(planId) {
            fetch(`/interaction/api/course_plans/${planId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const plan = data.plan;
                        
                        // 填充表单
                        document.getElementById('edit-plan-id').value = plan.id;
                        document.getElementById('edit-plan-title').value = plan.title;
                        document.getElementById('edit-plan-description').value = plan.description || '';
                        document.getElementById('edit-plan-location').value = plan.location || '';
                        document.getElementById('edit-plan-course').value = plan.course_id;
                        
                        // 格式化日期时间
                        const formatDateTime = (dateString) => {
                            const date = new Date(dateString);
                            return date.toISOString().slice(0, 16);
                        };
                        
                        document.getElementById('edit-plan-start-time').value = formatDateTime(plan.start_time);
                        document.getElementById('edit-plan-end-time').value = formatDateTime(plan.end_time);
                        
                        // 选中资源
                        if (plan.resources) {
                            // 重置所有复选框
                            document.querySelectorAll('#edit-resources-list input[type="checkbox"], #edit-exams-list input[type="checkbox"]').forEach(checkbox => {
                                checkbox.checked = false;
                            });
                            
                            // 选中相关资源
                            plan.resources.forEach(resource => {
                                if (resource.type === 'resource') {
                                    const checkbox = document.getElementById(`edit-resource-${resource.id}`);
                                    if (checkbox) checkbox.checked = true;
                                } else if (resource.type === 'exam') {
                                    const checkbox = document.getElementById(`edit-exam-${resource.id}`);
                                    if (checkbox) checkbox.checked = true;
                                }
                            });
                        }
                        
                        // 打开模态框
                        const editModal = new bootstrap.Modal(document.getElementById('editPlanModal'));
                        editModal.show();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('加载课程安排失败');
                });
        }
        
        // 保存课程安排
        document.getElementById('save-plan-btn').addEventListener('click', function() {
            const form = document.getElementById('create-plan-form');
            
            // 验证表单
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            
            // 收集表单数据
            const formData = {
                title: document.getElementById('plan-title').value,
                description: document.getElementById('plan-description').value,
                start_time: document.getElementById('plan-start-time').value,
                end_time: document.getElementById('plan-end-time').value,
                location: document.getElementById('plan-location').value,
                course_id: document.getElementById('plan-course').value,
                resources: []
            };
            
            // 收集选中的资源
            document.querySelectorAll('#resources-list input[type="checkbox"]:checked').forEach(checkbox => {
                formData.resources.push({
                    id: parseInt(checkbox.value),
                    type: 'resource'
                });
            });
            
            document.querySelectorAll('#exams-list input[type="checkbox"]:checked').forEach(checkbox => {
                formData.resources.push({
                    id: parseInt(checkbox.value),
                    type: 'exam'
                });
            });
            
            // 发送请求
            fetch('/interaction/course_plans/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createPlanModal'));
                    modal.hide();
                    
                    // 刷新日历
                    calendar.refetchEvents();
                    
                    // 显示成功消息
                    alert('课程安排创建成功');
                } else {
                    alert('创建失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发生错误，请稍后重试');
            });
        });
        
        // 更新课程安排
        document.getElementById('update-plan-btn').addEventListener('click', function() {
            const form = document.getElementById('edit-plan-form');
            
            // 验证表单
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            
            // 收集表单数据
            const planId = document.getElementById('edit-plan-id').value;
            const formData = {
                title: document.getElementById('edit-plan-title').value,
                description: document.getElementById('edit-plan-description').value,
                start_time: document.getElementById('edit-plan-start-time').value,
                end_time: document.getElementById('edit-plan-end-time').value,
                location: document.getElementById('edit-plan-location').value,
                course_id: document.getElementById('edit-plan-course').value,
                resources: []
            };
            
            // 收集选中的资源
            document.querySelectorAll('#edit-resources-list input[type="checkbox"]:checked').forEach(checkbox => {
                formData.resources.push({
                    id: parseInt(checkbox.value),
                    type: 'resource'
                });
            });
            
            document.querySelectorAll('#edit-exams-list input[type="checkbox"]:checked').forEach(checkbox => {
                formData.resources.push({
                    id: parseInt(checkbox.value),
                    type: 'exam'
                });
            });
            
            // 发送请求
            fetch(`/interaction/course_plans/${planId}/edit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editPlanModal'));
                    modal.hide();
                    
                    // 刷新日历
                    calendar.refetchEvents();
                    
                    // 刷新详情
                    showPlanDetails(planId);
                    
                    // 显示成功消息
                    alert('课程安排更新成功');
                } else {
                    alert('更新失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发生错误，请稍后重试');
            });
        });
        
        // 删除课程安排
        document.getElementById('delete-plan-btn').addEventListener('click', function() {
            if (confirm('确定要删除这个课程安排吗？')) {
                const planId = document.getElementById('edit-plan-id').value;
                
                fetch(`/interaction/course_plans/${planId}/delete`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 关闭模态框
                        const modal = bootstrap.Modal.getInstance(document.getElementById('editPlanModal'));
                        modal.hide();
                        
                        // 刷新日历
                        calendar.refetchEvents();
                        
                        // 清空详情
                        document.getElementById('plan-details').innerHTML = `
                            <div class="text-center text-muted py-4">
                                <p>点击日历中的安排查看详情</p>
                            </div>
                        `;
                        
                        // 显示成功消息
                        alert('课程安排已删除');
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('发生错误，请稍后重试');
                });
            }
        });
    });
</script>
{% endblock %}