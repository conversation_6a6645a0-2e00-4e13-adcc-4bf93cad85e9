{% extends 'base.html' %}

{% block title %}分组讨论{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>分组讨论</h2>
    
    <div class="row">
        <div class="col-md-12">
            {% if active_discussions %}
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">当前活跃讨论</h5>
                </div>
                <div class="card-body">
                    {% for discussion in active_discussions %}
                    <div class="card mb-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ discussion.title }}</h5>
                            <span class="badge bg-primary" id="timer-{{ discussion.id }}">
                                {% if discussion.remaining_time > 0 %}
                                {{ (discussion.remaining_time / 60)|int }}:{{ '%02d'|format(discussion.remaining_time|int % 60) }}
                                {% else %}
                                已结束
                                {% endif %}
                            </span>
                        </div>
                        <div class="card-body">
                            {% if discussion.description %}
                            <p class="card-text">{{ discussion.description }}</p>
                            {% endif %}
                            
                            {% if discussion.topic_content %}
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2 text-muted">讨论主题</h6>
                                    <p class="card-text">{{ discussion.topic_content }}</p>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if discussion.image_url %}
                            <div class="text-center">
                                <img src="{{ discussion.image_url }}" class="img-fluid rounded" alt="讨论主题截图">
                            </div>
                            {% endif %}
                            
                            <div class="d-flex justify-content-between mt-3">
                                <small class="text-muted">课程: {{ discussion.course.name }}</small>
                                <small class="text-muted">开始于: {{ discussion.created_at.strftime('%H:%M') }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% else %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="display-1 text-muted mb-4">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="text-muted">当前没有活跃的讨论</h3>
                    <p class="text-muted">等待教师发起分组讨论</p>
                </div>
            </div>
            {% endif %}
            
            {% if past_discussions %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">历史讨论</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        {% for discussion in past_discussions %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">{{ discussion.title }}</h5>
                                <small class="text-muted">{{ discussion.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <p class="mb-1">{{ discussion.description }}</p>
                            <small class="text-muted">课程: {{ discussion.course.name }}</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 倒计时定时器
        {% for discussion in active_discussions %}
        if ({{ discussion.remaining_time }} > 0) {
            const timerId{{ discussion.id }} = setInterval(function() {
                const timerElement = document.getElementById('timer-{{ discussion.id }}');
                let remainingTime = parseInt(timerElement.getAttribute('data-remaining') || '{{ discussion.remaining_time }}');
                
                remainingTime -= 1;
                timerElement.setAttribute('data-remaining', remainingTime);
                
                if (remainingTime <= 0) {
                    timerElement.textContent = '已结束';
                    clearInterval(timerId{{ discussion.id }});
                    // 可以在这里添加自动刷新页面的逻辑
                } else {
                    const minutes = Math.floor(remainingTime / 60);
                    const seconds = remainingTime % 60;
                    timerElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                }
            }, 1000);
        }
        {% endfor %}
    });
</script>
{% endblock %}