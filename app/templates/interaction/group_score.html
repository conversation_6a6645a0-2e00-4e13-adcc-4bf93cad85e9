{% extends 'base.html' %}

{% block title %}小组评分{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">小组评分</h1>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">评分表单</h5>
                </div>
                <div class="card-body">
                    <form id="scoreForm" method="POST" action="{{ url_for('interaction.group_score') }}">
                        <div class="mb-3">
                            <label for="course_id" class="form-label">选择课程</label>
                            <select class="form-select" id="course_id" name="course_id" required>
                                <option value="">-- 请选择课程 --</option>
                                {% for course in courses %}
                                <option value="{{ course.id }}">{{ course.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="group_id" class="form-label">选择小组</label>
                            <select class="form-select" id="group_id" name="group_id" required disabled>
                                <option value="">-- 请先选择课程 --</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="score" class="form-label">评分</label>
                            <div class="input-group">
                                <button type="button" class="btn btn-outline-secondary" id="decreaseScore">-</button>
                                <input type="number" class="form-control text-center" id="score" name="score" value="1" min="-10" max="10" step="1" required>
                                <button type="button" class="btn btn-outline-secondary" id="increaseScore">+</button>
                            </div>
                            <div class="form-text">可以输入正数（加分）或负数（减分），范围：-10 到 10</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="reason" class="form-label">评分理由</label>
                            <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="请输入评分理由（可选）"></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">提交评分</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">最近评分记录</h5>
                </div>
                <div class="card-body">
                    {% if recent_scores %}
                    <ul class="list-group">
                        {% for score in recent_scores %}
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ score.group.name }}</strong>
                                    <span class="badge {% if score.score > 0 %}bg-success{% else %}bg-danger{% endif %}">{{ score.score }}</span>
                                </div>
                                <small class="text-muted">{{ score.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            {% if score.reason %}
                            <small class="text-muted">{{ score.reason }}</small>
                            {% endif %}
                        </li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <p class="text-muted">暂无评分记录</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 课程小组数据
const courseGroups = {{ course_groups|tojson }};

// 当课程选择变化时，更新小组下拉列表
document.getElementById('course_id').addEventListener('change', function() {
    const courseId = this.value;
    const groupSelect = document.getElementById('group_id');
    
    // 清空小组下拉列表
    groupSelect.innerHTML = '';
    
    if (courseId) {
        // 添加小组选项
        const groups = courseGroups[courseId] || [];
        
        if (groups.length > 0) {
            groupSelect.disabled = false;
            
            // 添加默认选项
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = '-- 请选择小组 --';
            groupSelect.appendChild(defaultOption);
            
            // 添加小组选项
            groups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.id;
                option.textContent = group.name;
                groupSelect.appendChild(option);
            });
        } else {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = '该课程没有小组';
            groupSelect.appendChild(option);
            groupSelect.disabled = true;
        }
    } else {
        // 添加默认选项
        const option = document.createElement('option');
        option.value = '';
        option.textContent = '-- 请先选择课程 --';
        groupSelect.appendChild(option);
        groupSelect.disabled = true;
    }
});

// 增加分数按钮
document.getElementById('increaseScore').addEventListener('click', function() {
    const scoreInput = document.getElementById('score');
    let score = parseInt(scoreInput.value) || 0;
    score = Math.min(score + 1, 10);
    scoreInput.value = score;
});

// 减少分数按钮
document.getElementById('decreaseScore').addEventListener('click', function() {
    const scoreInput = document.getElementById('score');
    let score = parseInt(scoreInput.value) || 0;
    score = Math.max(score - 1, -10);
    scoreInput.value = score;
});
</script>
{% endblock %}