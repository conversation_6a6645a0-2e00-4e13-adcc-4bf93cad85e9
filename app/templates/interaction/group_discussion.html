{% extends 'base.html' %}

{% block title %}分组讨论{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>分组讨论</h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">发起讨论</h5>
                </div>
                <div class="card-body">
                    <form id="discussionForm" method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="course_id" class="form-label">选择课程</label>
                            <select class="form-select" id="course_id" name="course_id" required>
                                <option value="">请选择课程</option>
                                {% for course in courses %}
                                <option value="{{ course.id }}">{{ course.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">讨论标题</label>
                            <input type="text" class="form-control" id="title" name="title" value="分组讨论" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">讨论说明</label>
                            <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="duration" class="form-label">讨论时长（分钟）</label>
                            <input type="number" class="form-control" id="duration" name="duration" min="1" max="120" value="10" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">讨论主题类型</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="topic_type" id="topic_type_text" value="text" checked>
                                <label class="form-check-label" for="topic_type_text">
                                    文本主题
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="topic_type" id="topic_type_screenshot" value="screenshot">
                                <label class="form-check-label" for="topic_type_screenshot">
                                    截屏主题
                                </label>
                            </div>
                        </div>
                        
                        <div id="textTopicSection" class="mb-3">
                            <label for="topic_content" class="form-label">主题内容</label>
                            <textarea class="form-control" id="topic_content" name="topic_content" rows="4"></textarea>
                        </div>
                        
                        <div id="screenshotSection" class="mb-3" style="display: none;">
                            <label class="form-label">截屏主题</label>
                            <div class="d-grid gap-2 mb-2">
                                <button type="button" class="btn btn-outline-primary" id="captureScreenBtn">
                                    <i class="fas fa-camera"></i> 捕获屏幕
                                </button>
                            </div>
                            <div id="screenshotPreview" class="text-center p-3 border rounded mb-2" style="display: none;">
                                <img id="screenshotImage" class="img-fluid" src="" alt="截屏预览">
                            </div>
                            <input type="hidden" id="screenshot_data" name="screenshot_data">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">发起讨论</button>
                    </form>
                </div>
            </div>
            
            {% if past_discussions %}
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">历史讨论</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        {% for discussion in past_discussions %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">{{ discussion.title }}</h5>
                                <small class="text-muted">{{ discussion.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <p class="mb-1">{{ discussion.description }}</p>
                            <small class="text-muted">课程: {{ discussion.course.name }}</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
        
        <div class="col-md-6">
            {% if active_discussions %}
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">当前活跃讨论</h5>
                </div>
                <div class="card-body">
                    {% for discussion in active_discussions %}
                    <div class="card mb-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ discussion.title }}</h5>
                            <span class="badge bg-primary" id="timer-{{ discussion.id }}">
                                {% if discussion.remaining_time > 0 %}
                                {{ (discussion.remaining_time / 60)|int }}:{{ '%02d'|format(discussion.remaining_time|int % 60) }}
                                {% else %}
                                已结束
                                {% endif %}
                            </span>
                        </div>
                        <div class="card-body">
                            {% if discussion.description %}
                            <p class="card-text">{{ discussion.description }}</p>
                            {% endif %}
                            
                            {% if discussion.topic_content %}
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2 text-muted">讨论主题</h6>
                                    <p class="card-text">{{ discussion.topic_content }}</p>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if discussion.image_url %}
                            <div class="text-center">
                                <img src="{{ discussion.image_url }}" class="img-fluid rounded" alt="讨论主题截图">
                            </div>
                            {% endif %}
                            
                            <div class="d-flex justify-content-between mt-3">
                                <small class="text-muted">课程: {{ discussion.course.name }}</small>
                                <small class="text-muted">开始于: {{ discussion.created_at.strftime('%H:%M') }}</small>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button type="button" class="btn btn-sm btn-outline-danger end-discussion-btn" data-discussion-id="{{ discussion.id }}">
                                结束讨论
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% else %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="display-1 text-muted mb-4">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="text-muted">当前没有活跃的讨论</h3>
                    <p class="text-muted">使用左侧表单发起新的分组讨论</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 主题类型切换
        const topicTypeText = document.getElementById('topic_type_text');
        const topicTypeScreenshot = document.getElementById('topic_type_screenshot');
        const textTopicSection = document.getElementById('textTopicSection');
        const screenshotSection = document.getElementById('screenshotSection');
        
        topicTypeText.addEventListener('change', function() {
            if (this.checked) {
                textTopicSection.style.display = 'block';
                screenshotSection.style.display = 'none';
            }
        });
        
        topicTypeScreenshot.addEventListener('change', function() {
            if (this.checked) {
                textTopicSection.style.display = 'none';
                screenshotSection.style.display = 'block';
            }
        });
        
        // 截屏功能
        const captureScreenBtn = document.getElementById('captureScreenBtn');
        const screenshotPreview = document.getElementById('screenshotPreview');
        const screenshotImage = document.getElementById('screenshotImage');
        const screenshotData = document.getElementById('screenshot_data');
        
        captureScreenBtn.addEventListener('click', function() {
            // 这里实际应用中应该使用屏幕捕获API
            // 由于浏览器安全限制，这里只是模拟截屏功能
            alert('请使用系统截屏工具截取屏幕，然后粘贴到此处');
            
            // 监听粘贴事件
            document.addEventListener('paste', function(e) {
                const items = (e.clipboardData || e.originalEvent.clipboardData).items;
                
                for (let i = 0; i < items.length; i++) {
                    if (items[i].type.indexOf('image') !== -1) {
                        const blob = items[i].getAsFile();
                        const reader = new FileReader();
                        
                        reader.onload = function(event) {
                            screenshotImage.src = event.target.result;
                            screenshotData.value = event.target.result;
                            screenshotPreview.style.display = 'block';
                        };
                        
                        reader.readAsDataURL(blob);
                        break;
                    }
                }
            });
        });
        
        // 表单提交前验证
        const discussionForm = document.getElementById('discussionForm');
        discussionForm.addEventListener('submit', function(event) {
            const topicType = document.querySelector('input[name="topic_type"]:checked').value;
            
            if (topicType === 'text') {
                const topicContent = document.getElementById('topic_content').value.trim();
                if (!topicContent) {
                    event.preventDefault();
                    alert('请输入讨论主题内容');
                    return;
                }
            } else if (topicType === 'screenshot') {
                const screenshotData = document.getElementById('screenshot_data').value;
                if (!screenshotData) {
                    event.preventDefault();
                    alert('请先捕获屏幕');
                    return;
                }
            }
        });
        
        // 结束讨论按钮
        const endDiscussionBtns = document.querySelectorAll('.end-discussion-btn');
        endDiscussionBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const discussionId = this.getAttribute('data-discussion-id');
                if (confirm('确定要结束此讨论吗？')) {
                    // 发送请求结束讨论
                    fetch(`/interaction/end-discussion/${discussionId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('结束讨论失败');
                    });
                }
            });
        });
        
        // 倒计时定时器
        {% for discussion in active_discussions %}
        if ({{ discussion.remaining_time }} > 0) {
            const timerId{{ discussion.id }} = setInterval(function() {
                const timerElement = document.getElementById('timer-{{ discussion.id }}');
                let remainingTime = parseInt(timerElement.getAttribute('data-remaining') || '{{ discussion.remaining_time }}');
                
                remainingTime -= 1;
                timerElement.setAttribute('data-remaining', remainingTime);
                
                if (remainingTime <= 0) {
                    timerElement.textContent = '已结束';
                    clearInterval(timerId{{ discussion.id }});
                    // 可以在这里添加自动刷新页面的逻辑
                } else {
                    const minutes = Math.floor(remainingTime / 60);
                    const seconds = remainingTime % 60;
                    timerElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                }
            }, 1000);
        }
        {% endfor %}
    });
</script>
{% endblock %}