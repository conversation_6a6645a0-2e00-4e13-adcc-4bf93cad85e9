{% extends 'base.html' %}

{% block title %}创建试卷{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .question-item {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 10px;
        position: relative;
    }
    .question-item.selected {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    .question-type-badge {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .question-content {
        margin-bottom: 10px;
    }
    .selected-questions-container {
        min-height: 100px;
        border: 1px dashed #ccc;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 20px;
    }
    .selected-question-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        margin-bottom: 5px;
        border: 1px solid #eee;
        border-radius: 4px;
    }
    .question-handle {
        cursor: move;
        margin-right: 10px;
    }
    .search-container {
        margin-bottom: 20px;
    }
    .filter-container {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="hand-drawn">创建试卷</h1>
        <a href="{{ url_for('interaction.exams') }}" class="btn-hand-drawn">返回试卷列表</a>
    </div>

    <div class="card hand-drawn-border mb-4">
        <div class="card-header hand-drawn">
            <h5 class="mb-0">试卷信息</h5>
        </div>
        <div class="card-body">
            <form id="exam-form" method="POST">
                <div class="mb-3">
                    <label for="exam-name" class="form-label">试卷名称</label>
                    <input type="text" class="form-control hand-drawn-border" id="exam-name" name="name" required>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="exam-time-limit" class="form-label">时间限制（分钟）</label>
                        <input type="number" class="form-control hand-drawn-border" id="exam-time-limit" name="time_limit" min="1" value="60">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="exam-total-score" class="form-label">总分</label>
                        <input type="number" class="form-control hand-drawn-border" id="exam-total-score" name="total_score" min="0" step="0.5" value="100" readonly>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card hand-drawn-border">
                <div class="card-header hand-drawn">
                    <h5 class="mb-0">题库</h5>
                </div>
                <div class="card-body">
                    <div class="search-container">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control hand-drawn-border" id="question-search" placeholder="搜索题目...">
                            <button class="btn btn-outline-secondary" type="button" id="search-btn">搜索</button>
                        </div>
                        <div class="filter-container">
                            <select class="form-select hand-drawn-border" id="type-filter">
                                <option value="">所有题型</option>
                                <option value="single">单选题</option>
                                <option value="multiple">多选题</option>
                                <option value="truefalse">判断题</option>
                                <option value="fillblank">填空题</option>
                                <option value="subjective">主观题</option>
                            </select>
                        </div>
                    </div>
                    <div id="question-list">
                        <!-- 题目列表将通过AJAX加载 -->
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card hand-drawn-border">
                <div class="card-header hand-drawn">
                    <h5 class="mb-0">已选题目</h5>
                </div>
                <div class="card-body">
                    <div class="selected-questions-container" id="selected-questions">
                        <!-- 已选题目将在这里显示 -->
                        <div class="text-center text-muted py-4" id="no-questions-message">
                            <p>暂无已选题目，请从左侧题库中选择</p>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            已选: <span id="selected-count">0</span> 题 / 
                            总分: <span id="current-total-score">0</span> 分
                        </div>
                        <button type="button" class="btn-hand-drawn" id="save-exam-btn">保存试卷</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
<script>
    // 初始化拖拽排序
    const selectedQuestionsList = document.getElementById('selected-questions');
    new Sortable(selectedQuestionsList, {
        animation: 150,
        handle: '.question-handle',
        onEnd: function() {
            updateQuestionOrder();
        }
    });

    // 全局变量
    let allQuestions = [];
    let selectedQuestions = [];
    
    // 页面加载完成后获取题目列表
    document.addEventListener('DOMContentLoaded', function() {
        loadQuestions();
    });
    
    // 加载题目列表
    function loadQuestions() {
        fetch('/interaction/api/questions')
            .then(response => response.json())
            .then(data => {
                allQuestions = data.questions;
                renderQuestionList(allQuestions);
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('question-list').innerHTML = '<div class="alert alert-danger">加载题目失败</div>';
            });
    }
    
    // 渲染题目列表
    function renderQuestionList(questions) {
        const questionList = document.getElementById('question-list');
        questionList.innerHTML = '';
        
        if (questions.length === 0) {
            questionList.innerHTML = '<div class="alert alert-info">没有找到符合条件的题目</div>';
            return;
        }
        
        questions.forEach(question => {
            // 检查题目是否已被选择
            const isSelected = selectedQuestions.some(q => q.id === question.id);
            
            const questionItem = document.createElement('div');
            questionItem.className = `question-item ${isSelected ? 'selected' : ''}`;
            questionItem.dataset.id = question.id;
            
            // 获取题型显示文本
            let typeText = '';
            switch(question.type) {
                case 'single': typeText = '单选题'; break;
                case 'multiple': typeText = '多选题'; break;
                case 'truefalse': typeText = '判断题'; break;
                case 'fillblank': typeText = '填空题'; break;
                case 'subjective': typeText = '主观题'; break;
                default: typeText = '未知题型';
            }
            
            questionItem.innerHTML = `
                <span class="badge bg-secondary question-type-badge">${typeText}</span>
                <div class="question-content">${renderQuestionContent(question)}</div>
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">分值: ${question.score}</small>
                    <button class="btn btn-sm ${isSelected ? 'btn-danger remove-btn' : 'btn-primary add-btn'}" data-id="${question.id}">
                        ${isSelected ? '移除' : '添加'}
                    </button>
                </div>
            `;
            
            questionList.appendChild(questionItem);
        });
        
        // 添加事件监听器
        document.querySelectorAll('.add-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const questionId = parseInt(this.dataset.id);
                addQuestion(questionId);
            });
        });
        
        document.querySelectorAll('.remove-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const questionId = parseInt(this.dataset.id);
                removeQuestion(questionId);
            });
        });
    }
    
    // 渲染题目内容
    function renderQuestionContent(question) {
        try {
            const content = JSON.parse(question.content);
            // 简单处理，实际应用中可能需要更复杂的渲染
            return content.ops.map(op => op.insert).join('');
        } catch (e) {
            return question.content || '题目内容解析错误';
        }
    }
    
    // 添加题目到已选列表
    function addQuestion(questionId) {
        const question = allQuestions.find(q => q.id === questionId);
        if (!question) return;
        
        // 检查是否已经添加
        if (selectedQuestions.some(q => q.id === questionId)) return;
        
        // 添加到已选列表
        selectedQuestions.push(question);
        
        // 更新UI
        updateSelectedQuestionsList();
        updateQuestionListUI();
        updateTotalScore();
    }
    
    // 从已选列表中移除题目
    function removeQuestion(questionId) {
        selectedQuestions = selectedQuestions.filter(q => q.id !== questionId);
        
        // 更新UI
        updateSelectedQuestionsList();
        updateQuestionListUI();
        updateTotalScore();
    }
    
    // 更新已选题目列表UI
    function updateSelectedQuestionsList() {
        const selectedQuestionsContainer = document.getElementById('selected-questions');
        const noQuestionsMessage = document.getElementById('no-questions-message');
        
        if (selectedQuestions.length === 0) {
            selectedQuestionsContainer.innerHTML = '';
            selectedQuestionsContainer.appendChild(noQuestionsMessage);
            return;
        }
        
        // 隐藏无题目提示
        if (noQuestionsMessage.parentNode === selectedQuestionsContainer) {
            selectedQuestionsContainer.removeChild(noQuestionsMessage);
        }
        
        // 清空容器
        selectedQuestionsContainer.innerHTML = '';
        
        // 添加已选题目
        selectedQuestions.forEach((question, index) => {
            // 获取题型显示文本
            let typeText = '';
            switch(question.type) {
                case 'single': typeText = '单选题'; break;
                case 'multiple': typeText = '多选题'; break;
                case 'truefalse': typeText = '判断题'; break;
                case 'fillblank': typeText = '填空题'; break;
                case 'subjective': typeText = '主观题'; break;
                default: typeText = '未知题型';
            }
            
            const questionItem = document.createElement('div');
            questionItem.className = 'selected-question-item';
            questionItem.dataset.id = question.id;
            questionItem.dataset.order = index;
            
            questionItem.innerHTML = `
                <div class="d-flex align-items-center">
                    <span class="question-handle me-2"><i class="fas fa-grip-vertical"></i></span>
                    <div>
                        <div class="fw-bold">${index + 1}. [${typeText}] ${renderQuestionContent(question).substring(0, 30)}${renderQuestionContent(question).length > 30 ? '...' : ''}</div>
                        <small class="text-muted">分值: ${question.score}</small>
                    </div>
                </div>
                <button class="btn btn-sm btn-outline-danger remove-selected-btn" data-id="${question.id}">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            selectedQuestionsContainer.appendChild(questionItem);
        });
        
        // 添加事件监听器
        document.querySelectorAll('.remove-selected-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const questionId = parseInt(this.dataset.id);
                removeQuestion(questionId);
            });
        });
        
        // 更新计数
        document.getElementById('selected-count').textContent = selectedQuestions.length;
    }
    
    // 更新题目列表UI中的选中状态
    function updateQuestionListUI() {
        document.querySelectorAll('.question-item').forEach(item => {
            const questionId = parseInt(item.dataset.id);
            const isSelected = selectedQuestions.some(q => q.id === questionId);
            
            if (isSelected) {
                item.classList.add('selected');
                const btn = item.querySelector('button');
                btn.className = 'btn btn-sm btn-danger remove-btn';
                btn.textContent = '移除';
                btn.removeEventListener('click', addQuestion);
                btn.addEventListener('click', function() {
                    removeQuestion(questionId);
                });
            } else {
                item.classList.remove('selected');
                const btn = item.querySelector('button');
                btn.className = 'btn btn-sm btn-primary add-btn';
                btn.textContent = '添加';
                btn.removeEventListener('click', removeQuestion);
                btn.addEventListener('click', function() {
                    addQuestion(questionId);
                });
            }
        });
    }
    
    // 更新题目顺序
    function updateQuestionOrder() {
        const selectedItems = document.querySelectorAll('.selected-question-item');
        const newOrder = [];
        
        selectedItems.forEach((item, index) => {
            const questionId = parseInt(item.dataset.id);
            const question = selectedQuestions.find(q => q.id === questionId);
            if (question) {
                newOrder.push(question);
                
                // 更新显示的序号
                const titleElement = item.querySelector('.fw-bold');
                if (titleElement) {
                    const currentText = titleElement.textContent;
                    titleElement.textContent = currentText.replace(/^\d+\./, `${index + 1}.`);
                }
            }
        });
        
        selectedQuestions = newOrder;
    }
    
    // 更新总分
    function updateTotalScore() {
        const totalScore = selectedQuestions.reduce((sum, question) => sum + parseFloat(question.score), 0);
        document.getElementById('current-total-score').textContent = totalScore;
        document.getElementById('exam-total-score').value = totalScore;
    }
    
    // 搜索和筛选
    document.getElementById('search-btn').addEventListener('click', filterQuestions);
    document.getElementById('type-filter').addEventListener('change', filterQuestions);
    document.getElementById('question-search').addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            filterQuestions();
        }
    });
    
    function filterQuestions() {
        const searchTerm = document.getElementById('question-search').value.toLowerCase();
        const typeFilter = document.getElementById('type-filter').value;
        
        const filteredQuestions = allQuestions.filter(question => {
            const contentMatches = renderQuestionContent(question).toLowerCase().includes(searchTerm);
            const typeMatches = typeFilter === '' || question.type === typeFilter;
            return contentMatches && typeMatches;
        });
        
        renderQuestionList(filteredQuestions);
    }
    
    // 保存试卷
    document.getElementById('save-exam-btn').addEventListener('click', function() {
        const examName = document.getElementById('exam-name').value.trim();
        const timeLimit = parseInt(document.getElementById('exam-time-limit').value);
        const totalScore = parseFloat(document.getElementById('exam-total-score').value);
        
        if (!examName) {
            alert('请输入试卷名称');
            return;
        }
        
        if (selectedQuestions.length === 0) {
            alert('请至少选择一道题目');
            return;
        }
        
        // 准备提交数据
        const examData = {
            name: examName,
            time_limit: timeLimit,
            total_score: totalScore,
            questions: selectedQuestions.map((q, index) => ({
                id: q.id,
                order: index
            }))
        };
        
        // 发送请求
        fetch('/interaction/exams/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(examData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = data.redirect;
            } else {
                alert('保存失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('发生错误，请稍后重试');
        });
    });
</script>
{% endblock %}