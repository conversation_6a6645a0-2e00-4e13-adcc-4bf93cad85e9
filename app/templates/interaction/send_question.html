{% extends 'base.html' %}

{% block title %}发送题目{% endblock %}

{% block styles %}
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
    .question-type-container {
        display: none;
    }
    .question-type-container.active {
        display: block;
    }
    .question-list {
        max-height: 300px;
        overflow-y: auto;
    }
    .question-item {
        cursor: pointer;
        padding: 10px;
        border-bottom: 1px solid #eee;
    }
    .question-item:hover {
        background-color: #f8f9fa;
    }
    .question-item.selected {
        background-color: #e9ecef;
    }
    .selected-questions {
        margin-top: 20px;
    }
    .selected-question-item {
        padding: 10px;
        margin-bottom: 5px;
        background-color: #f8f9fa;
        border-radius: 4px;
        position: relative;
    }
    .remove-question {
        position: absolute;
        right: 10px;
        top: 10px;
        cursor: pointer;
    }
    #screenshot-preview {
        max-width: 100%;
        max-height: 300px;
        margin-top: 10px;
        border: 1px solid #ddd;
        display: none;
    }
    .editor-container {
        height: 200px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>发送题目</h2>
    
    <form id="send-question-form" method="post">
        <div class="card mb-4">
            <div class="card-header">
                <h5>基本信息</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="course_id" class="form-label">选择课程</label>
                    <select class="form-select" id="course_id" name="course_id" required>
                        <option value="">请选择课程</option>
                        {% for course in courses %}
                        <option value="{{ course.id }}">{{ course.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="title" class="form-label">标题</label>
                    <input type="text" class="form-control" id="title" name="title" placeholder="输入题目标题" required>
                </div>
                
                <div class="mb-3">
                    <label for="end_time" class="form-label">答题截止时间（可选）</label>
                    <input type="datetime-local" class="form-control" id="end_time" name="end_time">
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>题目类型</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="question_type" id="existing" value="existing" checked>
                        <label class="form-check-label" for="existing">从题库选择</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="question_type" id="temporary" value="temporary">
                        <label class="form-check-label" for="temporary">临时创建</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="question_type" id="screenshot" value="screenshot">
                        <label class="form-check-label" for="screenshot">截屏作为题目</label>
                    </div>
                </div>
                
                <!-- 从题库选择 -->
                <div id="existing-container" class="question-type-container active">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>可用题目</h6>
                            <div class="question-list">
                                {% for question in questions %}
                                <div class="question-item" data-id="{{ question.id }}" data-type="question">
                                    <strong>{{ question.type | replace('single', '单选题') | replace('multiple', '多选题') | replace('truefalse', '判断题') | replace('fillblank', '填空题') | replace('subjective', '主观题') }}</strong>
                                    <div>{{ question.content | safe }}</div>
                                </div>
                                {% endfor %}
                                
                                {% for exam in exams %}
                                <div class="question-item" data-id="{{ exam.id }}" data-type="exam">
                                    <strong>试卷：{{ exam.name }}</strong>
                                    <div>总分：{{ exam.total_score }}，题目数：{{ exam.questions.count() }}</div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>已选题目</h6>
                            <div id="selected-questions" class="selected-questions">
                                <div class="text-muted">尚未选择题目</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 临时创建 -->
                <div id="temporary-container" class="question-type-container">
                    <div class="mb-3">
                        <label for="temp_question_type" class="form-label">题目类型</label>
                        <select class="form-select" id="temp_question_type" name="temp_question_type">
                            <option value="single">单选题</option>
                            <option value="multiple">多选题</option>
                            <option value="truefalse">判断题</option>
                            <option value="fillblank">填空题</option>
                            <option value="subjective">主观题</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="content" class="form-label">题目内容</label>
                        <div id="content-editor" class="editor-container"></div>
                        <input type="hidden" id="content" name="content">
                    </div>
                    
                    <!-- 选项（单选、多选） -->
                    <div id="options-container" class="mb-3">
                        <label class="form-label">选项</label>
                        <div id="options-list">
                            <div class="input-group mb-2">
                                <span class="input-group-text">A</span>
                                <input type="text" class="form-control option-input" placeholder="选项A">
                                <button type="button" class="btn btn-outline-secondary set-answer">设为答案</button>
                            </div>
                            <div class="input-group mb-2">
                                <span class="input-group-text">B</span>
                                <input type="text" class="form-control option-input" placeholder="选项B">
                                <button type="button" class="btn btn-outline-secondary set-answer">设为答案</button>
                            </div>
                        </div>
                        <button type="button" id="add-option" class="btn btn-sm btn-outline-primary mt-2">添加选项</button>
                        <input type="hidden" id="options" name="options">
                        <input type="hidden" id="answer" name="answer">
                    </div>
                    
                    <!-- 判断题答案 -->
                    <div id="truefalse-container" class="mb-3" style="display: none;">
                        <label class="form-label">答案</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="truefalse_answer" id="truefalse_true" value="true">
                            <label class="form-check-label" for="truefalse_true">正确</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="truefalse_answer" id="truefalse_false" value="false">
                            <label class="form-check-label" for="truefalse_false">错误</label>
                        </div>
                    </div>
                    
                    <!-- 填空题答案 -->
                    <div id="fillblank-container" class="mb-3" style="display: none;">
                        <label for="fillblank_answer" class="form-label">答案（多个答案用逗号分隔）</label>
                        <input type="text" class="form-control" id="fillblank_answer" placeholder="填空题答案">
                    </div>
                    
                    <!-- 主观题答案 -->
                    <div id="subjective-container" class="mb-3" style="display: none;">
                        <label for="subjective-answer" class="form-label">参考答案</label>
                        <textarea class="form-control" id="subjective-answer" name="subjective-answer" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="score" class="form-label">分值</label>
                        <input type="number" class="form-control" id="score" name="score" value="1" min="0" step="0.5">
                    </div>
                </div>
                
                <!-- 截屏作为题目 -->
                <div id="screenshot-container" class="question-type-container">
                    <div class="mb-3">
                        <button type="button" id="take-screenshot" class="btn btn-primary">截取屏幕</button>
                        <p class="text-muted mt-2">点击按钮后，请选择要截取的屏幕区域</p>
                        <img id="screenshot-preview" src="" alt="截屏预览">
                        <input type="hidden" id="screenshot_data" name="screenshot_data">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">题目描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="screenshot-answer" class="form-label">参考答案</label>
                        <textarea class="form-control" id="screenshot-answer" name="answer" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="screenshot-score" class="form-label">分值</label>
                        <input type="number" class="form-control" id="screenshot-score" name="score" value="1" min="0" step="0.5">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
            <button type="button" class="btn btn-secondary me-md-2" onclick="window.history.back()">取消</button>
            <button type="submit" class="btn btn-primary">发送题目</button>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script>
    // 初始化富文本编辑器
    var quill = new Quill('#content-editor', {
        theme: 'snow',
        placeholder: '请输入题目内容...',
        modules: {
            toolbar: [
                ['bold', 'italic', 'underline', 'strike'],
                ['blockquote', 'code-block'],
                [{ 'header': 1 }, { 'header': 2 }],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'script': 'sub'}, { 'script': 'super' }],
                [{ 'indent': '-1'}, { 'indent': '+1' }],
                [{ 'direction': 'rtl' }],
                [{ 'size': ['small', false, 'large', 'huge'] }],
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'font': [] }],
                [{ 'align': [] }],
                ['clean'],
                ['formula', 'image']
            ]
        }
    });
    
    // 题目类型切换
    document.querySelectorAll('input[name="question_type"]').forEach(function(radio) {
        radio.addEventListener('change', function() {
            document.querySelectorAll('.question-type-container').forEach(function(container) {
                container.classList.remove('active');
            });
            document.getElementById(this.value + '-container').classList.add('active');
        });
    });
    
    // 临时题目类型切换
    document.getElementById('temp_question_type').addEventListener('change', function() {
        const type = this.value;
        
        // 隐藏所有答案容器
        document.getElementById('options-container').style.display = 'none';
        document.getElementById('truefalse-container').style.display = 'none';
        document.getElementById('fillblank-container').style.display = 'none';
        document.getElementById('subjective-container').style.display = 'none';
        
        // 显示对应类型的答案容器
        if (type === 'single' || type === 'multiple') {
            document.getElementById('options-container').style.display = 'block';
        } else if (type === 'truefalse') {
            document.getElementById('truefalse-container').style.display = 'block';
        } else if (type === 'fillblank') {
            document.getElementById('fillblank-container').style.display = 'block';
        } else if (type === 'subjective') {
            document.getElementById('subjective-container').style.display = 'block';
        }
    });
    
    // 添加选项
    document.getElementById('add-option').addEventListener('click', function() {
        const optionsList = document.getElementById('options-list');
        const optionsCount = optionsList.children.length;
        const optionLetter = String.fromCharCode(65 + optionsCount); // A, B, C, ...
        
        const optionDiv = document.createElement('div');
        optionDiv.className = 'input-group mb-2';
        optionDiv.innerHTML = `
            <span class="input-group-text">${optionLetter}</span>
            <input type="text" class="form-control option-input" placeholder="选项${optionLetter}">
            <button type="button" class="btn btn-outline-secondary set-answer">设为答案</button>
        `;
        
        optionsList.appendChild(optionDiv);
        
        // 绑定设为答案事件
        optionDiv.querySelector('.set-answer').addEventListener('click', function() {
            setAnswer(this);
        });
    });
    
    // 设为答案
    function setAnswer(button) {
        const type = document.getElementById('temp_question_type').value;
        const optionInput = button.previousElementSibling;
        const optionIndex = Array.from(document.querySelectorAll('.option-input')).indexOf(optionInput);
        
        if (type === 'single') {
            // 单选题只能选一个答案
            document.querySelectorAll('.set-answer').forEach(function(btn) {
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-secondary');
                btn.textContent = '设为答案';
            });
            
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-success');
            button.textContent = '已选择';
            
            document.getElementById('answer').value = JSON.stringify(optionIndex);
        } else if (type === 'multiple') {
            // 多选题可以选多个答案
            if (button.classList.contains('btn-success')) {
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
                button.textContent = '设为答案';
            } else {
                button.classList.remove('btn-outline-secondary');
                button.classList.add('btn-success');
                button.textContent = '已选择';
            }
            
            // 收集所有选中的答案
            const selectedAnswers = [];
            document.querySelectorAll('.set-answer.btn-success').forEach(function(btn) {
                const input = btn.previousElementSibling;
                const index = Array.from(document.querySelectorAll('.option-input')).indexOf(input);
                selectedAnswers.push(index);
            });
            
            document.getElementById('answer').value = JSON.stringify(selectedAnswers);
        }
    }
    
    // 判断题答案
    document.querySelectorAll('input[name="truefalse_answer"]').forEach(function(radio) {
        radio.addEventListener('change', function() {
            document.getElementById('answer').value = JSON.stringify(this.value === 'true');
        });
    });
    
    // 填空题答案
    document.getElementById('fillblank_answer').addEventListener('input', function() {
        const answers = this.value.split(',').map(answer => answer.trim());
        document.getElementById('answer').value = JSON.stringify(answers);
    });
    
    // 从题库选择题目
    const selectedQuestions = [];
    
    document.querySelectorAll('.question-item').forEach(function(item) {
        item.addEventListener('click', function() {
            const id = this.dataset.id;
            const type = this.dataset.type;
            const content = this.innerHTML;
            
            // 检查是否已选择
            const existingIndex = selectedQuestions.findIndex(q => q.id === id && q.type === type);
            
            if (existingIndex === -1) {
                // 添加到已选择列表
                selectedQuestions.push({
                    id: id,
                    type: type,
                    content: content
                });
                
                updateSelectedQuestionsList();
            }
        });
    });
    
    function updateSelectedQuestionsList() {
        const container = document.getElementById('selected-questions');
        
        if (selectedQuestions.length === 0) {
            container.innerHTML = '<div class="text-muted">尚未选择题目</div>';
            return;
        }
        
        container.innerHTML = '';
        
        selectedQuestions.forEach(function(question, index) {
            const div = document.createElement('div');
            div.className = 'selected-question-item';
            div.innerHTML = question.content;
            
            const removeBtn = document.createElement('span');
            removeBtn.className = 'remove-question';
            removeBtn.innerHTML = '&times;';
            removeBtn.addEventListener('click', function() {
                selectedQuestions.splice(index, 1);
                updateSelectedQuestionsList();
            });
            
            div.appendChild(removeBtn);
            container.appendChild(div);
            
            // 添加隐藏字段
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'question_ids';
            input.value = question.id;
            div.appendChild(input);
        });
    }
    
    // 截屏功能
    document.getElementById('take-screenshot').addEventListener('click', function() {
        // 这里应该调用截屏API，但在这个示例中，我们使用文件选择器代替
        // 实际实现中，可以使用MediaDevices API或浏览器扩展来实现截屏
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        fileInput.style.display = 'none';
        
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = document.getElementById('screenshot-preview');
                    img.src = event.target.result;
                    img.style.display = 'block';
                    
                    document.getElementById('screenshot_data').value = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
        
        document.body.appendChild(fileInput);
        fileInput.click();
        document.body.removeChild(fileInput);
    });
    
    // 表单提交
    document.getElementById('send-question-form').addEventListener('submit', function(e) {
        // 获取富文本编辑器内容
        document.getElementById('content').value = JSON.stringify(quill.getContents());
        
        // 收集选项
        if (document.getElementById('temp_question_type').value === 'single' || document.getElementById('temp_question_type').value === 'multiple') {
            const options = [];
            document.querySelectorAll('.option-input').forEach(function(input) {
                options.push(input.value);
            });
            document.getElementById('options').value = JSON.stringify(options);
        }
    });
</script>
{% endblock %}