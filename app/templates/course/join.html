{% extends "base.html" %}

{% block title %}加入课程{% endblock %}

{% block styles %}
{{ super() }}
<style>
    #qr-scanner-container {
        position: relative;
        width: 100%;
        max-width: 500px;
        margin: 20px auto;
    }
    #scanner-video {
        width: 100%;
        height: auto;
        border-radius: 8px;
    }
    #scanner-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        box-shadow: 0 0 20px rgba(0,0,0,0.5) inset;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card mt-5">
                <div class="card-body">
                    <h1 class="card-title text-center">加入课程</h1>
                    <p class="text-center text-muted">请输入教师提供的课程码，或扫描二维码加入。</p>

                    <form method="POST" action="{{ url_for('course.handle_join_request') }}" id="join-course-form">
                        <div class="mb-3">
                            <label for="access_code" class="form-label">课程码</label>
                            <input type="text" class="form-control form-control-lg" id="access_code" name="access_code" required>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">加入课程</button>
                            <button type="button" class="btn btn-secondary" id="scan-qr-btn">扫描二维码</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- QR Scanner Modal -->
<div class="modal fade" id="qrScannerModal" tabindex="-1" aria-labelledby="qrScannerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="qr-scanner-container">
                    <video id="scanner-video" playsinline></video>
                    <div id="scanner-overlay"></div>
                </div>
                <div id="scanner-status" class="text-center mt-2"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const scanQrBtn = document.getElementById('scan-qr-btn');
        const qrScannerModal = new bootstrap.Modal(document.getElementById('qrScannerModal'));
        const video = document.getElementById('scanner-video');
        const statusDiv = document.getElementById('scanner-status');
        const accessCodeInput = document.getElementById('access_code');
        let stream = null;

        scanQrBtn.addEventListener('click', async () => {
            qrScannerModal.show();
            //statusDiv.textContent = '正在启动摄像头...';
            try {
                stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } });
                video.srcObject = stream;
                video.onloadedmetadata = () => {
                    video.play();
                    requestAnimationFrame(tick);
                };
            } catch (err) {
                console.error("摄像头访问失败: ", err);
                statusDiv.textContent = '无法访问摄像头。请检查权限设置。';
            }
        });

        function tick() {
            if (video.readyState === video.HAVE_ENOUGH_DATA) {
                const canvasElement = document.createElement('canvas');
                const canvas = canvasElement.getContext('2d');
                canvasElement.height = video.videoHeight;
                canvasElement.width = video.videoWidth;
                canvas.drawImage(video, 0, 0, canvasElement.width, canvasElement.height);
                const imageData = canvas.getImageData(0, 0, canvasElement.width, canvasElement.height);
                const code = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });

                if (code) {
                    // 成功扫描到二维码
                    handleQrCode(code.data);
                    return; // 停止扫描
                }
            }
            requestAnimationFrame(tick);
        }

        function handleQrCode(data) {
            console.log('扫描到的数据:', data);
            try {
                // 优先尝试解析为JSON
                const qrInfo = JSON.parse(data);
                if (qrInfo && qrInfo.access_code) {
                    accessCodeInput.value = qrInfo.access_code;
                } else {
                    // 如果JSON中没有access_code，则尝试作为URL解析
                    handleAsUrl(data);
                }
            } catch (e) {
                // 如果不是JSON，则尝试作为URL解析
                handleAsUrl(data);
            }
            
            stopScanner();
            qrScannerModal.hide();
            accessCodeInput.focus();
        }

        function handleAsUrl(data) {
            try {
                const url = new URL(data);
                const pathParts = url.pathname.split('/');
                const accessCode = pathParts.pop() || pathParts.pop(); // 处理末尾可能有斜杠的情况
                
                if (accessCode) {
                    accessCodeInput.value = accessCode;
                } else {
                    // 如果URL中也没有课程码，则直接使用原始数据
                    accessCodeInput.value = data;
                }
            } catch (urlError) {
                // 如果也不是有效的URL，则直接使用原始数据
                accessCodeInput.value = data;
            }
        }

        function stopScanner() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                video.srcObject = null;
            }
        }

        // 当模态框关闭时，停止摄像头
        document.getElementById('qrScannerModal').addEventListener('hidden.bs.modal', stopScanner);
    });
</script>
{% endblock %}
