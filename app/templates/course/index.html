{% extends "base.html" %}

{% block title %}
    {% if current_user.is_teacher() %}
        课程管理
    {% else %}
        我的课程
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>
            {% if current_user.is_teacher() %}
                课程管理
            {% else %}
                我的课程
            {% endif %}
        </h1>
        {% if current_user.is_teacher() %}
            <a href="{{ url_for('course.create') }}" class="btn btn-primary">创建新课程</a>
        {% endif %}
    </div>

    {% if courses %}
        <div class="row">
            {% for course in courses %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">{{ course.name }}</h5>
                            <p class="card-text">{{ course.description or '暂无描述' }}</p>
                            <p class="card-text"><small class="text-muted">状态: {{ course.status }}</small></p>
                        </div>
                        <div class="card-footer">
                             <a href="{{ url_for('course.detail', course_id=course.id) }}" class="btn btn-outline-primary">进入课程</a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <p class="lead">
                {% if current_user.is_teacher() %}
                    您还没有创建任何课程。
                {% else %}
                    您还没有加入任何课程。
                {% endif %}
            </p>
        </div>
    {% endif %}
</div>
{% endblock %}