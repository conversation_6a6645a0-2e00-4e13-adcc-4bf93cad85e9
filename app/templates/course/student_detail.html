{% extends "base.html" %}

{% block title %}课程详情: {{ course.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h3>{{ course.name }}</h3>
        </div>
        <div class="card-body">
            <p class="card-text">{{ course.description or '暂无描述' }}</p>
            <p class="card-text">
                <small class="text-muted">教师: {{ course.teacher_id.name }} | 状态: {{ course.status }}</small>
            </p>
            <hr>
            
            <div id="check-in-panel" class="mb-4">
                <h4>课堂签到</h4>
                <button id="check-in-btn" class="btn btn-info">立即签到</button>
                <p id="check-in-status" class="text-muted mt-2"></p>
            </div>

            <hr>

            <h4>课程互动</h4>
            <div class="btn-group">
                <a href="{{ url_for('interaction.question_sessions') }}" class="btn">练习</a>
                <a href="{{ url_for('report.student_report', course_id=course.id) }}" class="btn">学习报告</a>
                <a href="{{ url_for('resource.search', course_id=course.id) }}" class="btn">搜索资料</a>
                <a href="{{ url_for('broadcast.student_broadcast', course_id=course.id) }}" class="btn">查看教师广播</a>
                <a href="{{ url_for('broadcast.student_screen', course_id=course.id) }}" class="btn">投屏</a>
            </div>
            
            <hr>

            <h4>课程资料</h4>
            <p>这里会实时显示教师分享的课程资料和作业。</p>
            <ul id="shared-files-list" class="list-group"></ul>

           <h4>我的小组</h4>                                                                                                                     │
            {% if student_group %}                                                                                                                │
               <p>你所在的小组是: <strong>{{ student_group.name }}</strong></p>                                                                  │
                <a href="{{ url_for('group.whiteboard', group_id=student_group.id) }}" class="btn btn-primary">进入小组白板</a>                   │
            {% else %}                                                                                                                            │
             <p>你还没有被分配到任何小组。</p>                                                                                                 │
            {% endif %} 

        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.socket.io/4.5.2/socket.io.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // --- 签到逻辑---
    const checkInBtn = document.getElementById('check-in-btn');
    const checkInStatus = document.getElementById('check-in-status');
    const courseId = {{ course.id }};

    if (checkInBtn) {
        checkInBtn.addEventListener('click', function() {
            this.disabled = true;
            this.textContent = '正在签到...';

            fetch(`/group/api/check-in/${courseId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    checkInBtn.textContent = '已签到';
                    checkInBtn.classList.remove('btn-info');
                    checkInBtn.classList.add('btn-success');
                    checkInStatus.textContent = `签到成功！时间：${new Date(data.check_in_time).toLocaleString()}`;
                    alert('签到成功！');
                } else {
                    checkInBtn.disabled = false;
                    checkInBtn.textContent = '立即签到';
                    checkInStatus.textContent = `操作失败：${data.message}`;
                    if (data.check_in_time) {
                        checkInBtn.textContent = '今日已签到';
                        checkInBtn.disabled = true;
                    }
                    alert(`签到失败：${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                checkInBtn.disabled = false;
                checkInBtn.textContent = '立即签到';
                checkInStatus.textContent = '签到请求失败，请检查网络连接。';
                alert('签到请求发生错误。');
            });
        });
    }

    // --- WebSocket 文件接收逻辑 ---
    const socket = io();
    const filesList = document.getElementById('shared-files-list');

    // 1. 连接成功后，加入课程房间
    socket.on('connect', function() {
        console.log('WebSocket Connected!');
        socket.emit('join', { room: `course_${courseId}` });
        console.log(`Joined room: course_${courseId}`);
    });

    // 2. 监听文件分享事件
    socket.on('shared_files', function(data) {
        console.log('Received shared files:', data);
        if (data.resources && data.resources.length > 0) {
            data.resources.forEach(function(file) {
                // 创建新的列表项和链接
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
                
                const fileName = document.createElement('span');
                fileName.textContent = file.name;

                const downloadLink = document.createElement('a');
                downloadLink.href = `/static/uploads/${file.url}`;
                downloadLink.textContent = '下载';
                downloadLink.className = 'btn btn-sm btn-outline-primary';
                downloadLink.target = '_blank'; // 在新标签页打开

                listItem.appendChild(fileName);
                listItem.appendChild(downloadLink);

                // 将新文件添加到列表顶部，并高亮显示
                filesList.prepend(listItem);
                listItem.style.backgroundColor = '#e8f5e9'; // 淡绿色高亮
                setTimeout(() => {
                    listItem.style.backgroundColor = ''; // 10秒后移除高亮
                }, 10000);

                alert(`您收到了一个新文件：${file.name}`);
            });
        }
    });

    socket.on('disconnect', function() {
        console.log('WebSocket Disconnected.');
    });
});
</script>
{% endblock %}
