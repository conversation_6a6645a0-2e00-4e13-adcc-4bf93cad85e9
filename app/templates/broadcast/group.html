{% extends "base.html" %}

{% block title %}小组广播 - {{ course.name }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .broadcast-container {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 200px);
        min-height: 500px;
    }
    
    .video-container {
        flex: 1;
        display: flex;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }
    
    .video-container video {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    
    .control-panel {
        padding: 15px;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-top: 15px;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .status-active {
        background-color: #28a745;
    }
    
    .status-inactive {
        background-color: #dc3545;
    }
    
    .status-connecting {
        background-color: #ffc107;
    }
    
    .self-view {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 200px;
        height: 150px;
        border: 2px solid #fff;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        display: none;
    }
    
    .self-view video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .no-broadcast {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #6c757d;
        font-size: 1.5rem;
        text-align: center;
    }
    
    .btn-group {
        margin-bottom: 10px;
    }
    
    .broadcast-target-selector {
        margin-top: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mt-4 mb-4">小组广播 - {{ course.name }}</h1>
    
    <div class="broadcast-container">
        <div class="video-container">
            <video id="localVideo" autoplay muted playsinline></video>
            <div id="noBroadcast" class="no-broadcast">
                <div>
                    <i class="fas fa-broadcast-tower fa-3x mb-3"></i>
                    <p>请选择广播内容并开始广播</p>
                </div>
            </div>
            <div id="selfView" class="self-view">
                <video id="previewVideo" autoplay muted playsinline></video>
            </div>
        </div>
        
        <div class="control-panel">
            <div class="row">
                <div class="col-md-6">
                    <div class="status">
                        状态: <span id="connectionStatus"><span class="status-indicator status-inactive"></span> 未连接</span>
                    </div>
                    <div class="mt-2">
                        连接数: <span id="connectionCount">0</span>
                    </div>
                </div>
                <div class="col-md-6 text-right">
                    <div class="btn-group">
                        <button id="cameraBtn" class="btn btn-info">开启摄像头</button>
                        <button id="screenBtn" class="btn btn-primary">共享屏幕</button>
                        <button id="stopBtn" class="btn btn-warning" disabled>停止共享</button>
                    </div>
                    <div class="btn-group ml-2">
                        <button id="broadcastBtn" class="btn btn-success" disabled>开始广播</button>
                        <button id="stopBroadcastBtn" class="btn btn-danger" disabled>停止广播</button>
                    </div>
                </div>
            </div>
            
            <div class="broadcast-target-selector">
                <div class="form-group">
                    <label for="broadcastTarget">广播目标:</label>
                    <select id="broadcastTarget" class="form-control">
                        <option value="teacher">教师端</option>
                        <option value="all_groups">所有小组</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
<script src="{{ url_for('static', filename='js/webrtc.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const cameraBtn = document.getElementById('cameraBtn');
        const screenBtn = document.getElementById('screenBtn');
        const stopBtn = document.getElementById('stopBtn');
        const broadcastBtn = document.getElementById('broadcastBtn');
        const stopBroadcastBtn = document.getElementById('stopBroadcastBtn');
        const localVideo = document.getElementById('localVideo');
        const previewVideo = document.getElementById('previewVideo');
        const selfView = document.getElementById('selfView');
        const noBroadcast = document.getElementById('noBroadcast');
        const connectionStatus = document.getElementById('connectionStatus');
        const connectionCount = document.getElementById('connectionCount');
        const broadcastTarget = document.getElementById('broadcastTarget');
        
        let webrtcClient = null;
        let isConnected = false;
        let isBroadcasting = false;
        
        // 初始化WebRTC客户端
        function initWebRTCClient() {
            if (webrtcClient) {
                webrtcClient.disconnect();
            }
            
            webrtcClient = new WebRTCClient({
                socketUrl: window.location.origin,
                userId: '{{ current_user.id }}',
                userType: 'group'
            });
            
            // 设置事件处理器
            webrtcClient.onConnected = (clientId) => {
                updateStatus('已连接到信令服务器', 'connecting');
                
                // 加入课程房间
                webrtcClient.joinRoom('course_{{ course.id }}');
            };
            
            webrtcClient.onRoomJoined = (data) => {
                updateStatus('已加入房间', 'active');
                isConnected = true;
                updateConnectionCount(data.peers.length + 1); // +1 包括自己
                updateButtons();
            };
            
            webrtcClient.onPeerJoined = (data) => {
                console.log('新对等端加入:', data);
                updateConnectionCount(Object.keys(webrtcClient.peers).length + 1); // +1 包括自己
            };
            
            webrtcClient.onPeerLeft = (data) => {
                console.log('对等端离开:', data);
                updateConnectionCount(Object.keys(webrtcClient.peers).length + 1); // +1 包括自己
            };
            
            webrtcClient.onBroadcastStarted = (data) => {
                console.log('广播开始:', data);
                isBroadcasting = true;
                updateButtons();
                updateStatus('广播已开始', 'active');
                noBroadcast.style.display = 'none';
            };
            
            webrtcClient.onBroadcastStopped = (data) => {
                console.log('广播停止:', data);
                isBroadcasting = false;
                updateButtons();
                updateStatus('广播已停止', 'connecting');
                noBroadcast.style.display = 'flex';
            };
            
            webrtcClient.onError = (data) => {
                updateStatus(`错误: ${data.message}`, 'inactive');
            };
            
            // 添加小组广播相关事件处理
            webrtcClient.socket.on('group_broadcast_started', (data) => {
                console.log('小组广播开始:', data);
                isBroadcasting = true;
                updateButtons();
                updateStatus('小组广播已开始', 'active');
                noBroadcast.style.display = 'none';
            });
            
            webrtcClient.socket.on('group_broadcast_stopped', (data) => {
                console.log('小组广播停止:', data);
                isBroadcasting = false;
                updateButtons();
                updateStatus('小组广播已停止', 'connecting');
                noBroadcast.style.display = 'flex';
            });
        }
        
        // 更新连接状态
        function updateStatus(message, status) {
            const indicator = connectionStatus.querySelector('.status-indicator');
            indicator.className = `status-indicator status-${status}`;
            connectionStatus.innerHTML = `<span class="status-indicator status-${status}"></span> ${message}`;
        }
        
        // 更新连接数
        function updateConnectionCount(count) {
            connectionCount.textContent = count;
        }
        
        // 更新按钮状态
        function updateButtons() {
            cameraBtn.disabled = !isConnected || webrtcClient.localStream;
            screenBtn.disabled = !isConnected || webrtcClient.localStream;
            stopBtn.disabled = !isConnected || !webrtcClient.localStream;
            broadcastBtn.disabled = !isConnected || !webrtcClient.localStream || isBroadcasting;
            stopBroadcastBtn.disabled = !isConnected || !isBroadcasting;
        }
        
        // 初始化
        initWebRTCClient();
        
        // 事件监听器
        cameraBtn.addEventListener('click', async () => {
            try {
                updateStatus('正在启动摄像头...', 'connecting');
                const stream = await webrtcClient.startCamera();
                localVideo.srcObject = stream;
                previewVideo.srcObject = stream;
                selfView.style.display = 'block';
                noBroadcast.style.display = 'none';
                updateStatus('摄像头已启动', 'active');
                updateButtons();
            } catch (error) {
                updateStatus(`启动摄像头失败: ${error.message}`, 'inactive');
            }
        });
        
        screenBtn.addEventListener('click', async () => {
            try {
                updateStatus('正在启动屏幕共享...', 'connecting');
                const stream = await webrtcClient.startScreenSharing();
                localVideo.srcObject = stream;
                previewVideo.srcObject = stream;
                selfView.style.display = 'block';
                noBroadcast.style.display = 'none';
                updateStatus('屏幕共享已启动', 'active');
                updateButtons();
            } catch (error) {
                updateStatus(`屏幕共享失败: ${error.message}`, 'inactive');
            }
        });
        
        stopBtn.addEventListener('click', () => {
            webrtcClient.stopLocalStream();
            localVideo.srcObject = null;
            previewVideo.srcObject = null;
            selfView.style.display = 'none';
            noBroadcast.style.display = 'flex';
            updateStatus('已连接到房间', 'active');
            updateButtons();
        });
        
        broadcastBtn.addEventListener('click', () => {
            const target = broadcastTarget.value;
            
            // 发送小组广播请求
            fetch(`/broadcast/api/group-broadcast/start/{{ course.id }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    target: target
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    webrtcClient.startBroadcast();
                } else {
                    updateStatus(`开始广播失败: ${data.message}`, 'inactive');
                }
            })
            .catch(error => {
                updateStatus(`开始广播请求失败: ${error.message}`, 'inactive');
            });
        });
        
        stopBroadcastBtn.addEventListener('click', () => {
            // 发送停止小组广播请求
            fetch(`/broadcast/api/group-broadcast/stop/{{ course.id }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    webrtcClient.stopBroadcast();
                } else {
                    updateStatus(`停止广播失败: ${data.message}`, 'inactive');
                }
            })
            .catch(error => {
                updateStatus(`停止广播请求失败: ${error.message}`, 'inactive');
            });
        });
        
        // 页面卸载时断开连接
        window.addEventListener('beforeunload', () => {
            if (webrtcClient) {
                webrtcClient.disconnect();
            }
        });
    });
</script>
{% endblock %}