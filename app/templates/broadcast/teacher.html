{% extends "base.html" %}

{% block title %}教师广播 - {{ course.name }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .broadcast-container {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 200px);
        min-height: 500px;
    }

    .video-container {
        flex: 1;
        display: flex;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }

    .video-container video {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .control-panel {
        padding: 15px;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-top: 15px;
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .status-active {
        background-color: #28a745;
    }

    .status-inactive {
        background-color: #dc3545;
    }

    .status-connecting {
        background-color: #ffc107;
    }

    .student-view {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 200px;
        height: 150px;
        border: 2px solid #fff;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        display: none;
    }

    .student-view video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .btn-group {
        margin-bottom: 10px;
    }

    /* 多画面显示样式 */
    .multi-view-container {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        z-index: 100;
        padding: 20px;
        box-sizing: border-box;
    }

    .multi-view-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        grid-gap: 15px;
        width: 100%;
        height: calc(100% - 60px);
        overflow-y: auto;
    }

    .multi-view-item {
        position: relative;
        background-color: #000;
        border-radius: 4px;
        overflow: hidden;
        aspect-ratio: 16/9;
    }

    .multi-view-item video {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .multi-view-item .student-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.7);
        color: #fff;
        padding: 5px 10px;
        font-size: 14px;
    }

    .multi-view-item .student-actions {
        position: absolute;
        top: 5px;
        right: 5px;
        display: flex;
    }

    .multi-view-item .student-actions button {
        margin-left: 5px;
        background-color: rgba(255, 255, 255, 0.2);
        border: none;
        color: #fff;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .multi-view-item .student-actions button:hover {
        background-color: rgba(255, 255, 255, 0.4);
    }
    
    /* 暂停画面样式 */
    .multi-view-item.paused::before {
        content: "已暂停";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.7);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        z-index: 10;
    }
    
    .compare-view-item.paused::before {
        content: "已暂停";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.7);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        z-index: 10;
    }

    .multi-view-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .multi-view-controls h3 {
        color: #fff;
        margin: 0;
    }

    /* 比较视图样式 */
    .compare-view-container {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        z-index: 100;
        padding: 20px;
        box-sizing: border-box;
    }

    .compare-view-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 15px;
        width: 100%;
        height: calc(100% - 60px);
    }

    .compare-view-item {
        position: relative;
        background-color: #000;
        border-radius: 4px;
        overflow: hidden;
    }

    .compare-view-item video {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .compare-view-item .student-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.7);
        color: #fff;
        padding: 5px 10px;
        font-size: 14px;
    }

    .compare-view-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .compare-view-controls h3 {
        color: #fff;
        margin: 0;
    }

    /* 学生列表样式 */
    .student-list-container {
        display: none;
        position: absolute;
        top: 0;
        right: 0;
        width: 250px;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 90;
        padding: 15px;
        box-sizing: border-box;
        overflow-y: auto;
    }

    .student-list-header {
        color: #fff;
        margin-top: 0;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .student-list-header button {
        background: none;
        border: none;
        color: #fff;
        cursor: pointer;
        font-size: 18px;
    }

    .student-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .student-list-item {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        padding: 10px;
        margin-bottom: 10px;
        color: #fff;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .student-list-item:hover {
        background-color: rgba(255, 255, 255, 0.2);
    }

    .student-list-item.selected {
        background-color: rgba(40, 167, 69, 0.3);
        border-left: 3px solid #28a745;
    }

    .student-list-actions {
        margin-top: 15px;
        display: flex;
        justify-content: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mt-4 mb-4">教师广播 - {{ course.name }}</h1>

    <div class="broadcast-container">
        <div class="video-container">
            <video id="localVideo" autoplay muted playsinline></video>
            <div id="studentView" class="student-view">
                <video id="studentVideo" autoplay playsinline></video>
            </div>

            <!-- 多画面显示容器 -->
            <div id="multiViewContainer" class="multi-view-container">
                <div class="multi-view-controls">
                    <h3>学生画面查看</h3>
                    <div>
                        <button id="compareSelectedBtn" class="btn btn-info" disabled>对比选中画面</button>
                        <button id="closeMultiViewBtn" class="btn btn-secondary">关闭</button>
                    </div>
                </div>
                <div id="multiViewGrid" class="multi-view-grid">
                    <!-- 学生画面将动态添加到这里 -->
                </div>
            </div>

            <!-- 比较视图容器 -->
            <div id="compareViewContainer" class="compare-view-container">
                <div class="compare-view-controls">
                    <h3>画面对比</h3>
                    <div>
                        <button id="closeCompareViewBtn" class="btn btn-secondary">返回</button>
                    </div>
                </div>
                <div id="compareViewGrid" class="compare-view-grid">
                    <!-- 对比画面将动态添加到这里 -->
                </div>
            </div>

            <!-- 学生列表侧边栏 -->
            <div id="studentListContainer" class="student-list-container">
                <div class="student-list-header">
                    <h4>学生列表</h4>
                    <button id="closeStudentListBtn">&times;</button>
                </div>
                <ul id="studentList" class="student-list">
                    <!-- 学生列表将动态添加到这里 -->
                </ul>
                <div class="student-list-actions">
                    <button id="viewSelectedBtn" class="btn btn-sm btn-primary" disabled>查看选中学生</button>
                </div>
            </div>
        </div>

        <div class="control-panel">
            <div class="row">
                <div class="col-md-6">
                    <div class="status">
                        状态: <span id="connectionStatus"><span class="status-indicator status-inactive"></span>
                            未连接</span>
                    </div>
                    <div class="mt-2">
                        连接数: <span id="connectionCount">0</span>
                    </div>
                </div>
                <div class="col-md-6 text-right">
                    <div class="btn-group">
                        <button id="screenBtn" class="btn btn-primary">共享屏幕</button>
                        <button id="cameraBtn" class="btn btn-info">开启摄像头</button>
                        <button id="stopBtn" class="btn btn-warning" disabled>停止共享</button>
                    </div>
                    <div class="btn-group ml-2">
                        <button id="broadcastBtn" class="btn btn-success" disabled>开始广播</button>
                        <button id="stopBroadcastBtn" class="btn btn-danger" disabled>停止广播</button>
                    </div>
                    <div class="btn-group ml-2">
                        <button id="viewStudentsBtn" class="btn btn-info">查看学生画面</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
<script src="{{ url_for('static', filename='js/webrtc.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 基本元素
        const screenBtn = document.getElementById('screenBtn');
        const cameraBtn = document.getElementById('cameraBtn');
        const stopBtn = document.getElementById('stopBtn');
        const broadcastBtn = document.getElementById('broadcastBtn');
        const stopBroadcastBtn = document.getElementById('stopBroadcastBtn');
        const localVideo = document.getElementById('localVideo');
        const studentVideo = document.getElementById('studentVideo');
        const studentView = document.getElementById('studentView');
        const connectionStatus = document.getElementById('connectionStatus');
        const connectionCount = document.getElementById('connectionCount');

        // 学生画面查看元素
        const viewStudentsBtn = document.getElementById('viewStudentsBtn');
        const studentListContainer = document.getElementById('studentListContainer');
        const closeStudentListBtn = document.getElementById('closeStudentListBtn');
        const studentList = document.getElementById('studentList');
        const viewSelectedBtn = document.getElementById('viewSelectedBtn');

        // 多画面显示元素
        const multiViewContainer = document.getElementById('multiViewContainer');
        const multiViewGrid = document.getElementById('multiViewGrid');
        const closeMultiViewBtn = document.getElementById('closeMultiViewBtn');
        const compareSelectedBtn = document.getElementById('compareSelectedBtn');

        // 比较视图元素
        const compareViewContainer = document.getElementById('compareViewContainer');
        const compareViewGrid = document.getElementById('compareViewGrid');
        const closeCompareViewBtn = document.getElementById('closeCompareViewBtn');

        let webrtcClient = null;
        let isConnected = false;
        let isBroadcasting = false;
        let selectedStudents = [];
        let studentStreams = {};
        let paused_screens = [];

        // 初始化WebRTC客户端
        function initWebRTCClient() {
            if (webrtcClient) {
                webrtcClient.disconnect();
            }

            webrtcClient = new WebRTCClient({
                socketUrl: window.location.origin,
                userId: '{{ current_user.id }}',
                userType: 'teacher'
            });

            // 设置事件处理器
            webrtcClient.onConnected = (clientId) => {
                updateStatus('已连接到信令服务器', 'connecting');

                // 加入课程房间
                webrtcClient.joinRoom('course_{{ course.id }}');
            };

            webrtcClient.onRoomJoined = (data) => {
                updateStatus('已加入房间', 'active');
                isConnected = true;
                updateConnectionCount(data.peers.length + 1); // +1 包括自己
                updateButtons();

                // 更新学生列表
                updateStudentList();
            };

            webrtcClient.onPeerJoined = (data) => {
                console.log('新对等端加入:', data);
                updateConnectionCount(Object.keys(webrtcClient.peers).length + 1); // +1 包括自己

                // 更新学生列表
                updateStudentList();
            };

            webrtcClient.onPeerLeft = (data) => {
                console.log('对等端离开:', data);
                updateConnectionCount(Object.keys(webrtcClient.peers).length + 1); // +1 包括自己

                // 更新学生列表
                updateStudentList();

                // 如果学生离开，移除其流
                if (studentStreams[data.client_id]) {
                    delete studentStreams[data.client_id];
                }

                // 如果学生在选中列表中，移除
                const index = selectedStudents.indexOf(data.client_id);
                if (index !== -1) {
                    selectedStudents.splice(index, 1);
                    updateViewSelectedButton();
                }

                // 更新多画面显示
                if (multiViewContainer.style.display === 'block') {
                    updateMultiView();
                }

                // 更新比较视图
                if (compareViewContainer.style.display === 'block') {
                    updateCompareView();
                }
            };

            webrtcClient.onRemoteStream = (peerId, stream) => {
                console.log('接收到远程流:', peerId);

                // 存储学生流
                studentStreams[peerId] = stream;

                // 如果是当前显示的学生，更新视图
                if (studentView.style.display === 'block' && studentVideo.dataset.studentId === peerId) {
                    studentVideo.srcObject = stream;
                }

                // 更新多画面显示
                if (multiViewContainer.style.display === 'block') {
                    updateMultiView();
                }

                // 更新比较视图
                if (compareViewContainer.style.display === 'block') {
                    updateCompareView();
                }
            };

            webrtcClient.onBroadcastStarted = (data) => {
                console.log('广播开始:', data);
                isBroadcasting = true;
                updateButtons();
            };

            webrtcClient.onBroadcastStopped = (data) => {
                console.log('广播停止:', data);
                isBroadcasting = false;
                updateButtons();
            };

            webrtcClient.onError = (data) => {
                updateStatus(`错误: ${data.message}`, 'inactive');
            };

            // 添加学生画面选择事件处理
            webrtcClient.socket.on('student_screen_selected', (data) => {
                console.log('学生画面已选择:', data);

                // 更新选中的学生列表
                selectedStudents = data.selected_screens;

                // 更新学生列表
                updateStudentList();

                // 更新多画面显示
                if (multiViewContainer.style.display === 'block') {
                    updateMultiView();
                }
            });

            webrtcClient.socket.on('student_screen_deselected', (data) => {
                console.log('学生画面已取消选择:', data);

                // 更新选中的学生列表
                selectedStudents = data.selected_screens;

                // 更新学生列表
                updateStudentList();

                // 更新多画面显示
                if (multiViewContainer.style.display === 'block') {
                    updateMultiView();
                }
            });

            webrtcClient.socket.on('student_screens_comparison', (data) => {
                console.log('比较学生画面:', data);

                // 更新选中的学生列表
                selectedStudents = data.student_ids;

                // 更新学生列表
                updateStudentList();

                // 显示比较视图
                showCompareView();
            });
            
            // 添加画面暂停/恢复事件处理
            webrtcClient.socket.on('screen_paused', (data) => {
                console.log('画面已暂停:', data);
                
                // 更新暂停的画面列表
                paused_screens = data.paused_screens;
                
                // 更新多画面显示
                if (multiViewContainer.style.display === 'block') {
                    updateMultiView();
                }
                
                // 更新比较视图
                if (compareViewContainer.style.display === 'block') {
                    updateCompareView();
                }
            });
            
            webrtcClient.socket.on('screen_resumed', (data) => {
                console.log('画面已恢复:', data);
                
                // 更新暂停的画面列表
                paused_screens = data.paused_screens;
                
                // 更新多画面显示
                if (multiViewContainer.style.display === 'block') {
                    updateMultiView();
                }
                
                // 更新比较视图
                if (compareViewContainer.style.display === 'block') {
                    updateCompareView();
                }
            });
        }

        // 更新连接状态
        function updateStatus(message, status) {
            const indicator = connectionStatus.querySelector('.status-indicator');
            indicator.className = `status-indicator status-${status}`;
            connectionStatus.innerHTML = `<span class="status-indicator status-${status}"></span> ${message}`;
        }

        // 更新连接数
        function updateConnectionCount(count) {
            connectionCount.textContent = count;
        }

        // 更新按钮状态
        function updateButtons() {
            screenBtn.disabled = !isConnected || webrtcClient.localStream;
            cameraBtn.disabled = !isConnected || webrtcClient.localStream;
            stopBtn.disabled = !isConnected || !webrtcClient.localStream;
            broadcastBtn.disabled = !isConnected || !webrtcClient.localStream || isBroadcasting;
            stopBroadcastBtn.disabled = !isConnected || !isBroadcasting;
            viewStudentsBtn.disabled = !isConnected;
        }

        // 更新学生列表
        function updateStudentList() {
            // 清空列表
            studentList.innerHTML = '';

            // 获取所有学生
            const students = [];
            for (const peerId in webrtcClient.peers) {
                // 跳过教师
                if (webrtcClient.peers[peerId].connection && peerId !== webrtcClient.clientId) {
                    students.push({
                        id: peerId,
                        selected: selectedStudents.includes(peerId)
                    });
                }
            }

            // 如果没有学生，显示提示
            if (students.length === 0) {
                const li = document.createElement('li');
                li.className = 'student-list-item';
                li.textContent = '没有学生连接';
                studentList.appendChild(li);
                viewSelectedBtn.disabled = true;
                return;
            }

            // 添加学生到列表
            students.forEach((student, index) => {
                const li = document.createElement('li');
                li.className = 'student-list-item';
                if (student.selected) {
                    li.className += ' selected';
                }
                li.textContent = `学生 ${index + 1}`;
                li.dataset.studentId = student.id;

                // 点击选择/取消选择学生
                li.addEventListener('click', () => {
                    if (li.classList.contains('selected')) {
                        // 取消选择
                        deselectStudent(student.id);
                    } else {
                        // 选择
                        selectStudent(student.id);
                    }
                });

                studentList.appendChild(li);
            });

            // 更新查看选中按钮状态
            updateViewSelectedButton();
        }

        // 更新查看选中按钮状态
        function updateViewSelectedButton() {
            viewSelectedBtn.disabled = selectedStudents.length === 0;
            compareSelectedBtn.disabled = selectedStudents.length < 2;
        }

        // 选择学生
        function selectStudent(studentId) {
            // 发送选择请求
            fetch(`/broadcast/api/broadcast/select-student/{{ course.id }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    student_id: studentId
                })
            })
                .then(response => response.json())
                .then(data => {
                    console.log('选择学生响应:', data);
                    if (data.status === 'success') {
                        // 添加到选中列表
                        if (!selectedStudents.includes(studentId)) {
                            selectedStudents.push(studentId);
                        }

                        // 更新学生列表
                        updateStudentList();
                    }
                })
                .catch(error => {
                    console.error('选择学生失败:', error);
                });
        }

        // 取消选择学生
        function deselectStudent(studentId) {
            // 发送取消选择请求
            fetch(`/broadcast/api/broadcast/deselect-student/{{ course.id }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    student_id: studentId
                })
            })
                .then(response => response.json())
                .then(data => {
                    console.log('取消选择学生响应:', data);
                    if (data.status === 'success') {
                        // 从选中列表中移除
                        const index = selectedStudents.indexOf(studentId);
                        if (index !== -1) {
                            selectedStudents.splice(index, 1);
                        }

                        // 更新学生列表
                        updateStudentList();
                    }
                })
                .catch(error => {
                    console.error('取消选择学生失败:', error);
                });
        }

        // 显示多画面视图
        function showMultiView() {
            // 隐藏学生列表
            studentListContainer.style.display = 'none';

            // 显示多画面容器
            multiViewContainer.style.display = 'block';

            // 更新多画面显示
            updateMultiView();
        }

        // 更新多画面显示
        function updateMultiView() {
            // 清空网格
            multiViewGrid.innerHTML = '';

            // 如果没有选中的学生，显示提示
            if (selectedStudents.length === 0) {
                const div = document.createElement('div');
                div.className = 'multi-view-item';
                div.innerHTML = '<div class="student-info">没有选中的学生画面</div>';
                multiViewGrid.appendChild(div);
                return;
            }

            // 添加选中的学生画面
            selectedStudents.forEach((studentId, index) => {
                const div = document.createElement('div');
                div.className = 'multi-view-item';

                // 创建视频元素
                const video = document.createElement('video');
                video.autoplay = true;
                video.playsinline = true;

                // 如果有学生流，设置视频源
                if (studentStreams[studentId]) {
                    video.srcObject = studentStreams[studentId];
                }

                // 创建学生信息
                const info = document.createElement('div');
                info.className = 'student-info';
                info.textContent = `学生 ${index + 1}`;

                // 创建操作按钮
                const actions = document.createElement('div');
                actions.className = 'student-actions';

                // 全屏按钮
                const fullscreenBtn = document.createElement('button');
                fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
                fullscreenBtn.title = '全屏查看';
                fullscreenBtn.addEventListener('click', () => {
                    // 显示单个学生视图
                    studentVideo.srcObject = studentStreams[studentId];
                    studentVideo.dataset.studentId = studentId;
                    studentView.style.display = 'block';

                    // 隐藏多画面视图
                    multiViewContainer.style.display = 'none';
                });

                // 暂停/恢复按钮
                const pauseBtn = document.createElement('button');
                // 检查是否已暂停
                const isPaused = paused_screens.includes(studentId);
                pauseBtn.innerHTML = isPaused ? '<i class="fas fa-play"></i>' : '<i class="fas fa-pause"></i>';
                pauseBtn.title = isPaused ? '恢复画面' : '暂停画面';
                pauseBtn.addEventListener('click', () => {
                    if (isPaused) {
                        resumeStudentScreen(studentId);
                    } else {
                        pauseStudentScreen(studentId);
                    }
                });

                // 取消选择按钮
                const deselectBtn = document.createElement('button');
                deselectBtn.innerHTML = '<i class="fas fa-times"></i>';
                deselectBtn.title = '取消选择';
                deselectBtn.addEventListener('click', () => {
                    deselectStudent(studentId);
                });

                // 添加按钮到操作区
                actions.appendChild(fullscreenBtn);
                actions.appendChild(pauseBtn);
                actions.appendChild(deselectBtn);
                
                // 如果画面已暂停，添加暂停样式
                if (isPaused) {
                    div.classList.add('paused');
                }

                // 添加元素到画面项
                div.appendChild(video);
                div.appendChild(info);
                div.appendChild(actions);

                // 添加到网格
                multiViewGrid.appendChild(div);
            });
        }

        // 显示比较视图
        function showCompareView() {
            // 隐藏多画面视图
            multiViewContainer.style.display = 'none';

            // 显示比较视图容器
            compareViewContainer.style.display = 'block';

            // 更新比较视图
            updateCompareView();
        }

        // 更新比较视图
        function updateCompareView() {
            // 清空网格
            compareViewGrid.innerHTML = '';

            // 如果选中的学生少于2个，显示提示
            if (selectedStudents.length < 2) {
                const div = document.createElement('div');
                div.className = 'compare-view-item';
                div.innerHTML = '<div class="student-info">需要至少选择2个学生画面进行比较</div>';
                compareViewGrid.appendChild(div);
                return;
            }

            // 添加选中的学生画面（最多2个）
            const studentsToCompare = selectedStudents.slice(0, 2);

            studentsToCompare.forEach((studentId, index) => {
                const div = document.createElement('div');
                div.className = 'compare-view-item';
                
                // 检查是否已暂停
                const isPaused = paused_screens.includes(studentId);
                if (isPaused) {
                    div.classList.add('paused');
                }

                // 创建视频元素
                const video = document.createElement('video');
                video.autoplay = true;
                video.playsinline = true;

                // 如果有学生流，设置视频源
                if (studentStreams[studentId]) {
                    video.srcObject = studentStreams[studentId];
                }

                // 创建学生信息
                const info = document.createElement('div');
                info.className = 'student-info';
                info.textContent = `学生 ${index + 1}`;
                
                // 创建操作按钮
                const actions = document.createElement('div');
                actions.className = 'student-actions';
                
                // 暂停/恢复按钮
                const pauseBtn = document.createElement('button');
                pauseBtn.innerHTML = isPaused ? '<i class="fas fa-play"></i>' : '<i class="fas fa-pause"></i>';
                pauseBtn.title = isPaused ? '恢复画面' : '暂停画面';
                pauseBtn.addEventListener('click', () => {
                    if (isPaused) {
                        resumeStudentScreen(studentId);
                    } else {
                        pauseStudentScreen(studentId);
                    }
                });
                
                // 添加按钮到操作区
                actions.appendChild(pauseBtn);

                // 添加元素到画面项
                div.appendChild(video);
                div.appendChild(info);
                div.appendChild(actions);

                // 添加到网格
                compareViewGrid.appendChild(div);
            });
        }

        // 初始化
        initWebRTCClient();

        // 基本功能事件监听器
        screenBtn.addEventListener('click', async () => {
            try {
                updateStatus('正在启动屏幕共享...', 'connecting');
                const stream = await webrtcClient.startScreenSharing();
                localVideo.srcObject = stream;
                updateStatus('屏幕共享已启动', 'active');
                updateButtons();
            } catch (error) {
                updateStatus(`屏幕共享失败: ${error.message}`, 'inactive');
            }
        });

        cameraBtn.addEventListener('click', async () => {
            try {
                updateStatus('正在启动摄像头...', 'connecting');
                const stream = await webrtcClient.startCamera();
                localVideo.srcObject = stream;
                updateStatus('摄像头已启动', 'active');
                updateButtons();
            } catch (error) {
                updateStatus(`启动摄像头失败: ${error.message}`, 'inactive');
            }
        });

        stopBtn.addEventListener('click', () => {
            webrtcClient.stopLocalStream();
            localVideo.srcObject = null;
            updateStatus('媒体流已停止', 'connecting');
            updateButtons();
        });

        broadcastBtn.addEventListener('click', () => {
            webrtcClient.startBroadcast();
            updateStatus('正在开始广播...', 'connecting');
        });

        stopBroadcastBtn.addEventListener('click', () => {
            webrtcClient.stopBroadcast();
            updateStatus('正在停止广播...', 'connecting');
        });

        // 学生画面查看事件监听器
        viewStudentsBtn.addEventListener('click', () => {
            // 显示学生列表
            studentListContainer.style.display = 'block';

            // 更新学生列表
            updateStudentList();
        });

        closeStudentListBtn.addEventListener('click', () => {
            // 隐藏学生列表
            studentListContainer.style.display = 'none';
        });

        viewSelectedBtn.addEventListener('click', () => {
            // 显示多画面视图
            showMultiView();
        });

        closeMultiViewBtn.addEventListener('click', () => {
            // 隐藏多画面视图
            multiViewContainer.style.display = 'none';
        });

        compareSelectedBtn.addEventListener('click', () => {
            // 发送比较请求
            fetch(`/broadcast/api/broadcast/compare-students/{{ course.id }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    student_ids: selectedStudents.slice(0, 2)
                })
            })
                .then(response => response.json())
                .then(data => {
                    console.log('比较学生响应:', data);
                    if (data.status === 'success') {
                        // 显示比较视图
                        showCompareView();
                    }
                })
                .catch(error => {
                    console.error('比较学生失败:', error);
                });
        });

        closeCompareViewBtn.addEventListener('click', () => {
            // 隐藏比较视图
            compareViewContainer.style.display = 'none';

            // 显示多画面视图
            showMultiView();
        });

        // 暂停学生画面
        function pauseStudentScreen(studentId) {
            // 发送暂停请求
            fetch(`/broadcast/api/broadcast/pause-screen/{{ course.id }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    student_id: studentId
                })
            })
                .then(response => response.json())
                .then(data => {
                    console.log('暂停画面响应:', data);
                })
                .catch(error => {
                    console.error('暂停画面失败:', error);
                });
        }
        
        // 恢复学生画面
        function resumeStudentScreen(studentId) {
            // 发送恢复请求
            fetch(`/broadcast/api/broadcast/resume-screen/{{ course.id }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    student_id: studentId
                })
            })
                .then(response => response.json())
                .then(data => {
                    console.log('恢复画面响应:', data);
                })
                .catch(error => {
                    console.error('恢复画面失败:', error);
                });
        }
        
        // 获取暂停的画面列表
        function getPausedScreens() {
            fetch(`/broadcast/api/broadcast/paused-screens/{{ course.id }}`)
                .then(response => response.json())
                .then(data => {
                    console.log('暂停画面列表:', data);
                    if (data.status === 'success') {
                        paused_screens = data.paused_screens;
                        
                        // 更新多画面显示
                        if (multiViewContainer.style.display === 'block') {
                            updateMultiView();
                        }
                        
                        // 更新比较视图
                        if (compareViewContainer.style.display === 'block') {
                            updateCompareView();
                        }
                    }
                })
                .catch(error => {
                    console.error('获取暂停画面列表失败:', error);
                });
        }
        
        // 初始化时获取暂停的画面列表
        document.addEventListener('DOMContentLoaded', function() {
            // 在连接成功后获取暂停的画面列表
            setTimeout(() => {
                if (isConnected) {
                    getPausedScreens();
                }
            }, 2000);
        });

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', () => {
            if (webrtcClient) {
                webrtcClient.disconnect();
            }
        });
    });
</script>
{% endblock %}