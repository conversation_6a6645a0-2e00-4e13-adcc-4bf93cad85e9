{% extends 'base.html' %}

{% block title %}上传资源{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>上传资源</h1>
    
    <div class="card">
        <div class="card-body">
            <ul class="nav nav-tabs" id="uploadTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="regular-tab" data-bs-toggle="tab" data-bs-target="#regular" type="button" role="tab" aria-controls="regular" aria-selected="true">普通上传</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="chunked-tab" data-bs-toggle="tab" data-bs-target="#chunked" type="button" role="tab" aria-controls="chunked" aria-selected="false">大文件上传</button>
                </li>
            </ul>
            
            <div class="tab-content mt-3" id="uploadTabsContent">
                <!-- 普通上传 -->
                <div class="tab-pane fade show active" id="regular" role="tabpanel" aria-labelledby="regular-tab">
                    <form action="{{ url_for('resource.upload') }}" method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="file" class="form-label">选择文件</label>
                            <input type="file" class="form-control" id="file" name="file" required>
                            <div class="form-text">
                                支持的文件类型：
                                <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#supportedFormats" aria-expanded="false" aria-controls="supportedFormats">
                                    查看详细支持的格式
                                </button>
                            </div>
                            <div class="collapse mt-2" id="supportedFormats">
                                <div class="card card-body">
                                    <h6>支持的文件格式：</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>文档：</strong> PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT, RTF, ODT, ODS, ODP, CSV, MD, TEX</li>
                                        <li><strong>图片：</strong> JPG, JPEG, PNG, GIF, BMP, SVG, WEBP, TIFF, TIF, ICO, HEIC, RAW</li>
                                        <li><strong>视频：</strong> MP4, AVI, MOV, WMV, FLV, MKV, WEBM, M4V, 3GP, MPEG, MPG, TS</li>
                                        <li><strong>音频：</strong> MP3, WAV, OGG, FLAC, AAC, M4A, WMA, OPUS, AIFF, ALAC</li>
                                        <li><strong>压缩文件：</strong> ZIP, RAR, 7Z, TAR, GZ, BZ2, XZ, ISO</li>
                                        <li><strong>代码文件：</strong> PY, JS, HTML, CSS, JAVA, C, CPP, H, PHP, RB, GO, JSON, XML, SQL</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="course_id" class="form-label">关联课程（可选）</label>
                            <select class="form-select" id="course_id" name="course_id">
                                <option value="">不关联课程</option>
                                {% for course in courses %}
                                <option value="{{ course.id }}">{{ course.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">上传</button>
                        <a href="{{ url_for('resource.index') }}" class="btn btn-secondary">取消</a>
                    </form>
                </div>
                
                <!-- 大文件上传 -->
                <div class="tab-pane fade" id="chunked" role="tabpanel" aria-labelledby="chunked-tab">
                    <div class="mb-3">
                        <label for="chunkedFile" class="form-label">选择大文件</label>
                        <input type="file" class="form-control" id="chunkedFile">
                        <div class="form-text">
                            适用于大文件上传，支持断点续传和并行上传
                        </div>
                        <div class="alert alert-info mt-2">
                            <small>
                                <i class="fas fa-info-circle"></i> 大文件上传功能特点：
                                <ul class="mb-0">
                                    <li>支持暂停和继续上传</li>
                                    <li>自动分块并行上传，提高上传速度</li>
                                    <li>支持与普通上传相同的所有文件格式</li>
                                    <li>推荐用于上传超过10MB的大文件</li>
                                </ul>
                            </small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="chunkedCourseId" class="form-label">关联课程（可选）</label>
                        <select class="form-select" id="chunkedCourseId">
                            <option value="">不关联课程</option>
                            {% for course in courses %}
                            <option value="{{ course.id }}">{{ course.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="progress mb-3 d-none" id="uploadProgress">
                        <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    
                    <button type="button" class="btn btn-primary" id="startUploadBtn">开始上传</button>
                    <button type="button" class="btn btn-warning d-none" id="pauseUploadBtn">暂停</button>
                    <button type="button" class="btn btn-success d-none" id="resumeUploadBtn">继续</button>
                    <a href="{{ url_for('resource.index') }}" class="btn btn-secondary">取消</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 课程列表已通过服务器端渲染加载，不需要再通过API加载
        
        // 大文件分块上传
        const chunkSize = 5 * 1024 * 1024; // 5MB 每块
        let file = null;
        let chunks = [];
        let currentChunk = 0;
        let totalChunks = 0;
        let isPaused = false;
        let fileId = null;
        let uploadQueue = [];
        let concurrentUploads = 5; // 同时上传的分块数
        let activeUploads = 0;
        
        const chunkedFileInput = document.getElementById('chunkedFile');
        const startUploadBtn = document.getElementById('startUploadBtn');
        const pauseUploadBtn = document.getElementById('pauseUploadBtn');
        const resumeUploadBtn = document.getElementById('resumeUploadBtn');
        const progressBar = document.getElementById('uploadProgress');
        const progressBarInner = progressBar.querySelector('.progress-bar');
        
        chunkedFileInput.addEventListener('change', function(e) {
            file = e.target.files[0];
            if (file) {
                totalChunks = Math.ceil(file.size / chunkSize);
                chunks = Array(totalChunks).fill(false); // 跟踪已上传的块
                currentChunk = 0;
                fileId = generateUUID();
                
                // 显示文件信息
                const fileInfo = document.createElement('div');
                fileInfo.className = 'alert alert-info';
                fileInfo.innerHTML = `
                    <strong>文件信息:</strong><br>
                    名称: ${file.name}<br>
                    大小: ${formatFileSize(file.size)}<br>
                    类型: ${file.type}<br>
                    分块数: ${totalChunks}
                `;
                
                const infoContainer = document.getElementById('fileInfoContainer') || document.createElement('div');
                infoContainer.id = 'fileInfoContainer';
                infoContainer.innerHTML = '';
                infoContainer.appendChild(fileInfo);
                
                if (!document.getElementById('fileInfoContainer')) {
                    document.getElementById('chunked').insertBefore(infoContainer, document.getElementById('uploadProgress'));
                }
            }
        });
        
        startUploadBtn.addEventListener('click', function() {
            if (!file) {
                alert('请先选择文件');
                return;
            }
            
            // 显示进度条和控制按钮
            progressBar.classList.remove('d-none');
            pauseUploadBtn.classList.remove('d-none');
            startUploadBtn.classList.add('d-none');
            
            // 准备上传队列
            uploadQueue = [];
            for (let i = 0; i < totalChunks; i++) {
                uploadQueue.push(i);
            }
            
            // 开始上传
            isPaused = false;
            activeUploads = 0;
            
            // 启动多个并行上传
            for (let i = 0; i < Math.min(concurrentUploads, totalChunks); i++) {
                uploadNextChunk();
            }
        });
        
        pauseUploadBtn.addEventListener('click', function() {
            isPaused = true;
            pauseUploadBtn.classList.add('d-none');
            resumeUploadBtn.classList.remove('d-none');
        });
        
        resumeUploadBtn.addEventListener('click', function() {
            isPaused = false;
            resumeUploadBtn.classList.add('d-none');
            pauseUploadBtn.classList.remove('d-none');
            
            // 如果当前没有活跃上传，重新启动上传
            if (activeUploads === 0) {
                for (let i = 0; i < Math.min(concurrentUploads, uploadQueue.length); i++) {
                    uploadNextChunk();
                }
            }
        });
        
        function uploadNextChunk() {
            if (isPaused || uploadQueue.length === 0) return;
            
            activeUploads++;
            const chunkIndex = uploadQueue.shift();
            const start = chunkIndex * chunkSize;
            const end = Math.min(file.size, start + chunkSize);
            const chunk = file.slice(start, end);
            
            const formData = new FormData();
            formData.append('file', chunk);
            formData.append('filename', file.name);
            formData.append('chunkNumber', chunkIndex);
            formData.append('totalChunks', totalChunks);
            formData.append('fileId', fileId);
            
            const courseId = document.getElementById('chunkedCourseId').value;
            if (courseId) {
                formData.append('course_id', courseId);
            }
            
            fetch('/resource/upload/chunked', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                activeUploads--;
                
                if (data.success) {
                    chunks[chunkIndex] = true;
                    
                    // 更新进度
                    const completedChunks = chunks.filter(Boolean).length;
                    const progress = Math.round((completedChunks / totalChunks) * 100);
                    progressBarInner.style.width = progress + '%';
                    progressBarInner.textContent = progress + '%';
                    progressBarInner.setAttribute('aria-valuenow', progress);
                    
                    // 添加进度条动画效果
                    if (!progressBarInner.classList.contains('progress-bar-striped')) {
                        progressBarInner.classList.add('progress-bar-striped', 'progress-bar-animated');
                    }
                    
                    // 检查是否全部完成
                    if (completedChunks === totalChunks) {
                        // 上传完成
                        progressBarInner.classList.remove('progress-bar-striped', 'progress-bar-animated');
                        progressBarInner.classList.add('bg-success');
                        
                        // 显示成功消息
                        const successAlert = document.createElement('div');
                        successAlert.className = 'alert alert-success mt-3';
                        successAlert.innerHTML = `
                            <strong>上传成功!</strong> 文件 "${file.name}" 已成功上传。
                            <a href="/resource" class="btn btn-sm btn-primary ms-2">查看资源列表</a>
                        `;
                        document.getElementById('chunked').appendChild(successAlert);
                        
                        // 隐藏暂停按钮
                        pauseUploadBtn.classList.add('d-none');
                    } else if (!isPaused && uploadQueue.length > 0) {
                        // 继续上传下一个分块
                        uploadNextChunk();
                    }
                } else {
                    // 上传失败，将分块放回队列
                    uploadQueue.push(chunkIndex);
                    console.error('Chunk upload failed:', data.message);
                    
                    // 如果不是暂停状态，尝试重新上传
                    if (!isPaused && uploadQueue.length > 0) {
                        setTimeout(() => {
                            uploadNextChunk();
                        }, 1000); // 延迟1秒后重试
                    }
                }
            })
            .catch(error => {
                activeUploads--;
                console.error('Error:', error);
                
                // 上传失败，将分块放回队列
                uploadQueue.push(chunkIndex);
                
                // 如果不是暂停状态，尝试重新上传
                if (!isPaused && uploadQueue.length > 0) {
                    setTimeout(() => {
                        uploadNextChunk();
                    }, 1000); // 延迟1秒后重试
                }
            });
        }
        
        // 辅助函数：生成UUID
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        // 辅助函数：格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    });
</script>
{% endblock %}