{% extends 'base.html' %}

{% block title %}资源详情{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>资源详情</h1>
    
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h5 class="card-title">{{ resource.name }}</h5>
                    <h6 class="card-subtitle mb-2 text-muted">
                        {% if resource.type == 'document' %}
                            <i class="fas fa-file-alt"></i> 文档
                        {% elif resource.type == 'image' %}
                            <i class="fas fa-image"></i> 图片
                        {% elif resource.type == 'video' %}
                            <i class="fas fa-video"></i> 视频
                        {% elif resource.type == 'audio' %}
                            <i class="fas fa-music"></i> 音频
                        {% else %}
                            <i class="fas fa-file"></i> 其他
                        {% endif %}
                    </h6>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('resource.download', resource_id=resource.id) }}" class="btn btn-primary">
                        <i class="fas fa-download"></i> 下载
                    </a>
                    <button type="button" class="btn btn-secondary rename-btn" data-id="{{ resource.id }}" data-name="{{ resource.name }}">
                        <i class="fas fa-edit"></i> 重命名
                    </button>
                    <button type="button" class="btn btn-danger delete-btn" data-id="{{ resource.id }}" data-name="{{ resource.name }}">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
            
            <hr>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <p><strong>文件大小：</strong> {{ (resource.size / 1024)|round(1) }} KB</p>
                    <p><strong>文件格式：</strong> {{ resource.format }}</p>
                    <p><strong>上传时间：</strong> {{ resource.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>上传者：</strong> {{ resource.owner.username }}</p>
                    {% if resource.course %}
                    <p><strong>关联课程：</strong> {{ resource.course.name }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="mt-4">
                {% if resource.type == 'image' %}
                <div class="text-center">
                    <img src="{{ url_for('static', filename='uploads/' + resource.url) }}" class="img-fluid" alt="{{ resource.name }}">
                </div>
                {% elif resource.type == 'video' %}
                <div class="text-center">
                    <video controls class="img-fluid">
                        <source src="{{ url_for('static', filename='uploads/' + resource.url) }}" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                </div>
                {% elif resource.type == 'audio' %}
                <div class="text-center">
                    <audio controls>
                        <source src="{{ url_for('static', filename='uploads/' + resource.url) }}" type="audio/mpeg">
                        您的浏览器不支持音频播放。
                    </audio>
                </div>
                {% elif resource.type == 'document' and resource.format == 'pdf' %}
                <div class="text-center">
                    <embed src="{{ url_for('static', filename='uploads/' + resource.url) }}" type="application/pdf" width="100%" height="600px">
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 此文件类型不支持在线预览，请下载后查看。
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="mt-3">
        <a href="{{ url_for('resource.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回资源列表
        </a>
    </div>
</div>

<!-- 重命名模态框 -->
<div class="modal fade" id="renameModal" tabindex="-1" aria-labelledby="renameModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="renameModalLabel">重命名资源</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="renameForm">
                    <input type="hidden" id="resourceId" name="resourceId">
                    <div class="mb-3">
                        <label for="newName" class="form-label">新名称</label>
                        <input type="text" class="form-control" id="newName" name="newName" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveRenameBtn">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除资源 <span id="deleteResourceName"></span> 吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST">
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 重命名功能
        const renameButtons = document.querySelectorAll('.rename-btn');
        const renameModal = new bootstrap.Modal(document.getElementById('renameModal'));
        const resourceIdInput = document.getElementById('resourceId');
        const newNameInput = document.getElementById('newName');
        const saveRenameBtn = document.getElementById('saveRenameBtn');
        
        renameButtons.forEach(button => {
            button.addEventListener('click', function() {
                const resourceId = this.dataset.id;
                const resourceName = this.dataset.name;
                
                resourceIdInput.value = resourceId;
                newNameInput.value = resourceName;
                
                renameModal.show();
            });
        });
        
        saveRenameBtn.addEventListener('click', function() {
            const resourceId = resourceIdInput.value;
            const newName = newNameInput.value;
            
            if (!newName.trim()) {
                alert('请输入有效的名称');
                return;
            }
            
            fetch(`/resource/${resourceId}/rename`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `new_name=${encodeURIComponent(newName)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message || '重命名失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发生错误，请稍后重试');
            });
        });
        
        // 删除功能
        const deleteButtons = document.querySelectorAll('.delete-btn');
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        const deleteResourceName = document.getElementById('deleteResourceName');
        const deleteForm = document.getElementById('deleteForm');
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const resourceId = this.dataset.id;
                const resourceName = this.dataset.name;
                
                deleteResourceName.textContent = resourceName;
                deleteForm.action = `/resource/${resourceId}/delete`;
                
                deleteModal.show();
            });
        });
    });
</script>
{% endblock %}