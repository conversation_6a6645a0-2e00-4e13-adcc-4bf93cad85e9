{% extends 'base.html' %}

{% block title %}搜索资源{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>搜索资源</h1>

    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ url_for('resource.search') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="q" class="form-label">关键词</label>
                    <input type="text" class="form-control" id="q" name="q" value="{{ query }}">
                </div>
                <div class="col-md-3">
                    <label for="type" class="form-label">资源类型</label>
                    <select class="form-select" id="type" name="type">
                        <option value="" {% if not type %}selected{% endif %}>全部类型</option>
                        <option value="document" {% if type=='document' %}selected{% endif %}>文档</option>
                        <option value="image" {% if type=='image' %}selected{% endif %}>图片</option>
                        <option value="video" {% if type=='video' %}selected{% endif %}>视频</option>
                        <option value="audio" {% if type=='audio' %}selected{% endif %}>音频</option>
                        <option value="archive" {% if type=='archive' %}selected{% endif %}>压缩文件</option>
                        <option value="code" {% if type=='code' %}selected{% endif %}>代码文件</option>
                        <option value="other" {% if type=='other' %}selected{% endif %}>其他</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="course_id" class="form-label">课程</label>
                    <select class="form-select" id="course_id" name="course_id">
                        <option value="" {% if not course_id %}selected{% endif %}>全部课程</option>
                        {% for course in courses %}
                        <option value="{{ course.id }}" {% if course_id|int==course.id %}selected{% endif %}>{{
                            course.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>

                <div class="col-md-6">
                    <label class="form-label">排序方式</label>
                    <div class="d-flex">
                        <select class="form-select me-2" id="sort_by" name="sort_by">
                            <option value="uploaded_at" {% if sort_by=='uploaded_at' %}selected{% endif %}>上传时间</option>
                            <option value="name" {% if sort_by=='name' %}selected{% endif %}>名称</option>
                            <option value="type" {% if sort_by=='type' %}selected{% endif %}>类型</option>
                            <option value="size" {% if sort_by=='size' %}selected{% endif %}>大小</option>
                        </select>
                        <select class="form-select" id="sort_order" name="sort_order">
                            <option value="desc" {% if sort_order=='desc' %}selected{% endif %}>降序</option>
                            <option value="asc" {% if sort_order=='asc' %}selected{% endif %}>升序</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% if resources %}
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">搜索结果 ({{ resources|length }})</h5>
            <div class="btn-group">
                <button class="btn btn-sm btn-outline-secondary" id="view-grid">
                    <i class="fas fa-th"></i> 网格视图
                </button>
                <button class="btn btn-sm btn-outline-secondary active" id="view-list">
                    <i class="fas fa-list"></i> 列表视图
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- 列表视图 -->
            <div id="list-view" class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>类型</th>
                            <th>课程</th>
                            <th>大小</th>
                            <th>上传时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for resource in resources %}
                        <tr>
                            <td>
                                <a href="{{ url_for('resource.detail', resource_id=resource.id) }}">
                                    {{ resource.name }}
                                </a>
                            </td>
                            <td>
                                {% if resource.type == 'document' %}
                                <i class="fas fa-file-alt text-primary"></i> 文档
                                {% elif resource.type == 'image' %}
                                <i class="fas fa-image text-success"></i> 图片
                                {% elif resource.type == 'video' %}
                                <i class="fas fa-video text-danger"></i> 视频
                                {% elif resource.type == 'audio' %}
                                <i class="fas fa-music text-warning"></i> 音频
                                {% elif resource.type == 'archive' %}
                                <i class="fas fa-file-archive text-secondary"></i> 压缩文件
                                {% elif resource.type == 'code' %}
                                <i class="fas fa-code text-info"></i> 代码文件
                                {% else %}
                                <i class="fas fa-file text-dark"></i> 其他
                                {% endif %}
                            </td>
                            <td>
                                {% if resource.course %}
                                {{ resource.course.name }}
                                {% else %}
                                <span class="text-muted">个人资源</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if resource.size < 1024 %} {{ resource.size }} B {% elif resource.size < 1024 * 1024
                                    %} {{ (resource.size / 1024)|round(1) }} KB {% else %} {{ (resource.size / (1024 *
                                    1024))|round(1) }} MB {% endif %} </td>
                            <td>{{ resource.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('resource.download', resource_id=resource.id) }}"
                                        class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-download"></i> 下载
                                    </a>
                                    <a href="{{ url_for('resource.detail', resource_id=resource.id) }}"
                                        class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i> 查看
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 网格视图 -->
            <div id="grid-view" class="row row-cols-1 row-cols-md-3 g-4" style="display: none;">
                {% for resource in resources %}
                <div class="col">
                    <div class="card h-100">
                        <div class="card-header">
                            {% if resource.type == 'document' %}
                            <i class="fas fa-file-alt text-primary fa-2x"></i>
                            {% elif resource.type == 'image' %}
                            <i class="fas fa-image text-success fa-2x"></i>
                            {% elif resource.type == 'video' %}
                            <i class="fas fa-video text-danger fa-2x"></i>
                            {% elif resource.type == 'audio' %}
                            <i class="fas fa-music text-warning fa-2x"></i>
                            {% elif resource.type == 'archive' %}
                            <i class="fas fa-file-archive text-secondary fa-2x"></i>
                            {% elif resource.type == 'code' %}
                            <i class="fas fa-code text-info fa-2x"></i>
                            {% else %}
                            <i class="fas fa-file text-dark fa-2x"></i>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{{ resource.name }}</h5>
                            <p class="card-text">
                                {% if resource.course %}
                                <small class="text-muted">课程: {{ resource.course.name }}</small><br>
                                {% endif %}
                                <small class="text-muted">上传时间: {{ resource.uploaded_at.strftime('%Y-%m-%d %H:%M')
                                    }}</small><br>
                                <small class="text-muted">大小:
                                    {% if resource.size < 1024 %} {{ resource.size }} B {% elif resource.size < 1024 *
                                        1024 %} {{ (resource.size / 1024)|round(1) }} KB {% else %} {{ (resource.size /
                                        (1024 * 1024))|round(1) }} MB {% endif %} </small>
                            </p>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100">
                                <a href="{{ url_for('resource.download', resource_id=resource.id) }}"
                                    class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-download"></i> 下载
                                </a>
                                <a href="{{ url_for('resource.detail', resource_id=resource.id) }}"
                                    class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-eye"></i> 查看
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> 没有找到符合条件的资源。
    </div>
    {% endif %}

    <div class="mt-3">
        <a href="{{ url_for('resource.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回资源列表
        </a>
    </div>
</div>

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 视图切换
        const gridView = document.getElementById('grid-view');
        const listView = document.getElementById('list-view');
        const gridBtn = document.getElementById('view-grid');
        const listBtn = document.getElementById('view-list');

        gridBtn.addEventListener('click', function () {
            gridView.style.display = 'flex';
            listView.style.display = 'none';
            gridBtn.classList.add('active');
            listBtn.classList.remove('active');
        });

        listBtn.addEventListener('click', function () {
            gridView.style.display = 'none';
            listView.style.display = 'block';
            listBtn.classList.add('active');
            gridBtn.classList.remove('active');
        });

        // 表单自动提交
        const sortBy = document.getElementById('sort_by');
        const sortOrder = document.getElementById('sort_order');

        sortBy.addEventListener('change', function () {
            this.form.submit();
        });

        sortOrder.addEventListener('change', function () {
            this.form.submit();
        });
    });
</script>
{% endblock %}
{% endblock %}