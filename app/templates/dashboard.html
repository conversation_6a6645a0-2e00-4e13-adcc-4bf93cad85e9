{% extends "base.html" %}

{% block title %}仪表盘 - 智慧教室系统{% endblock %}

{% block content %}
<div class="hand-drawn" style="margin-top: 2rem;">
    <h1>欢迎，{{ current_user.username }}！</h1>
    
    <div class="grid" style="margin-top: 2rem;">
        {% if current_user.is_teacher() %}
            <div class="card">

            </div>
        {% else %}
            <div class="card">
                <div class="card-header">学生功能</div>
                <div class="card-body">
                    <p>作为学生，您可以：</p>
                    <ul>
                        <li>加入课程</li>
                        <li>参与课堂互动</li>
                        <li>查看课程资料</li>
                        <li>参与小组讨论</li>
                        <li>查看个人报告</li>
                    </ul>
                </div>
            </div>
        {% endif %}
        
        <div class="card">
            <div class="card-header">个人信息</div>
            <div class="card-body">
                <p><strong>用户名：</strong>{{ current_user.username }}</p>
                <p><strong>邮箱：</strong>{{ current_user.email }}</p>
                <p><strong>角色：</strong>{{ '教师' if current_user.is_teacher() else '学生' }}</p>
                <p><strong>注册时间：</strong>{{ current_user.created_at.strftime('%Y-%m-%d %H:%M') if current_user.created_at else '未知' }}</p>
                {% if current_user.last_login %}
                    <p><strong>上次登录：</strong>{{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}