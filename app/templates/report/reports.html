{% extends 'base.html' %}

{% block title %}课堂报告 - {{ course.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>课堂报告 - {{ course.name }}</h2>
    
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">报告列表</h5>
                <div>
                    <a href="{{ url_for('report.activities', course_id=course.id) }}" class="btn btn-info btn-sm mr-2">
                        <i class="fas fa-history"></i> 查看活动记录
                    </a>
                    {% if current_user.id == course.teacher_id %}
                    <form action="{{ url_for('report.generate_report', course_id=course.id) }}" method="post" class="d-inline">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 生成今日报告
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if reports %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>标题</th>
                                <th>签到人数</th>
                                <th>题目数量</th>
                                <th>互动次数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in reports %}
                                <tr>
                                    <td>{{ report.date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ report.title }}</td>
                                    <td>{{ report.attendance_count }}</td>
                                    <td>{{ report.question_count }}</td>
                                    <td>{{ report.interaction_count }}</td>
                                    <td>
                                        <a href="{{ url_for('report.report_detail', report_id=report.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> 查看详情
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    暂无课堂报告
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}