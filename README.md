# 智慧教室系统 - Web应用

这是智慧教室系统的Web应用部分，基于Flask框架开发。

## 项目结构

```
smart_classroom_web/
├── app/                    # 应用主目录
│   ├── __init__.py         # 应用初始化
│   ├── models/             # 数据模型
│   ├── controllers/        # 控制器/路由
│   ├── services/           # 业务逻辑
│   ├── static/             # 静态文件(CSS, JS, 图片)
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   ├── templates/          # HTML模板
│   └── utils/              # 工具函数
├── tests/                  # 测试目录
│   ├── unit/               # 单元测试
│   └── integration/        # 集成测试
├── docs/                   # 文档
├── config.py               # 配置文件
├── requirements.txt        # 依赖包
└── run.py                  # 应用入口
```

## 安装与运行

1. 创建虚拟环境:
```
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

2. 安装依赖:
```
pip install -r requirements.txt
```

3. 运行应用:
```
python run.py
```

## 测试

运行测试:
```
pytest
```