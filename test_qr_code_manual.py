"""
手动测试二维码生成功能

使用方法：
1. 确保已安装所有依赖（pip install -r requirements.txt）
2. 运行此脚本（python test_qr_code_manual.py）
3. 查看生成的二维码图片（qr_code_test.png）
"""

import qrcode
import json
import io
import base64
from PIL import Image

def generate_test_qr_code():
    """生成测试二维码"""
    # 构建二维码数据
    qr_data = {
        "course_id": 123,
        "access_code": "123456",
        "join_url": "http://example.com/course/join/123456"
    }
    
    # 将数据转换为JSON字符串
    qr_content = json.dumps(qr_data)
    
    # 创建二维码实例
    qr = qrcode.QRCode(
        version=1,  # 控制二维码大小，1是最小的
        error_correction=qrcode.constants.ERROR_CORRECT_L,  # 错误纠正级别
        box_size=10,  # 每个方格的像素数
        border=4,  # 边框大小
    )
    
    # 添加数据
    qr.add_data(qr_content)
    qr.make(fit=True)
    
    # 创建二维码图片
    qr_img = qr.make_image(fill_color="black", back_color="white")
    
    # 保存图片
    qr_img.save("qr_code_test.png")
    
    # 将图片转换为base64编码
    img_buffer = io.BytesIO()
    qr_img.save(img_buffer, format='PNG')
    img_buffer.seek(0)
    
    # 编码为base64
    qr_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
    
    return f"data:image/png;base64,{qr_base64}"

def decode_qr_code(qr_base64):
    """解码二维码（仅用于演示）"""
    # 从base64数据中提取图片数据
    if qr_base64.startswith('data:image/png;base64,'):
        base64_data = qr_base64.split(',')[1]
    else:
        base64_data = qr_base64
    
    # 解码base64数据
    decoded_data = base64.b64decode(base64_data)
    
    # 打开图片
    img = Image.open(io.BytesIO(decoded_data))
    
    # 注意：这里我们不实际解码二维码内容，因为需要额外的库
    # 在实际应用中，可以使用pyzbar或其他库来解码二维码
    print("二维码图片已生成，请使用二维码扫描器扫描 qr_code_test.png 文件")
    
    return img

if __name__ == "__main__":
    print("生成测试二维码...")
    qr_code_data = generate_test_qr_code()
    print(f"二维码数据长度: {len(qr_code_data)} 字符")
    print("二维码图片已保存为 qr_code_test.png")
    
    # 解码二维码（仅用于演示）
    decode_qr_code(qr_code_data)