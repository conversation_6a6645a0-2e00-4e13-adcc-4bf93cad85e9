#!/usr/bin/env python3
"""
手动测试课程创建API的脚本
"""

import requests
import json
from datetime import datetime

# 测试配置
BASE_URL = 'http://localhost:5000'
API_URL = f'{BASE_URL}/course/api/courses'

def test_course_creation_api():
    """测试课程创建API"""
    print("=== 课程创建API测试 ===")
    
    # 测试数据
    test_cases = [
        {
            'name': '测试课程 - 4位码',
            'description': '这是一个测试课程，使用4位访问码',
            'code_length': 4,
            'start_time': '2025-01-01T09:00:00',
            'end_time': '2025-06-30T18:00:00'
        },
        {
            'name': '测试课程 - 6位码',
            'description': '这是一个测试课程，使用6位访问码',
            'code_length': 6,
            'start_time': '2025-02-01T10:00:00',
            'end_time': '2025-07-31T17:00:00'
        },
        {
            'name': '测试课程 - 9位码',
            'description': '这是一个测试课程，使用9位访问码',
            'code_length': 9
        },
        {
            'name': '测试课程 - 默认码长',
            'description': '这是一个测试课程，使用默认访问码长度'
        },
        {
            'name': '测试课程 - 无效码长',
            'description': '这是一个测试课程，使用无效访问码长度',
            'code_length': 5  # 无效长度，应该默认为4位
        }
    ]
    
    print("注意：此脚本需要服务器运行并且用户已登录")
    print("请确保：")
    print("1. Flask服务器正在运行 (python run.py)")
    print("2. 已有教师用户登录")
    print("3. 会话cookie有效")
    print()
    
    for i, test_data in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_data['name']}")
        print(f"数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        
        try:
            response = requests.post(
                API_URL,
                json=test_data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 201:
                result = response.json()
                print("✅ 创建成功!")
                print(f"课程ID: {result.get('id')}")
                print(f"访问码: {result.get('access_code')} (长度: {len(result.get('access_code', ''))})")
                print(f"教师ID: {result.get('teacher_id')}")
                print(f"状态: {result.get('status')}")
            else:
                print("❌ 创建失败!")
                try:
                    error = response.json()
                    print(f"错误信息: {error.get('error', '未知错误')}")
                except:
                    print(f"响应内容: {response.text}")
        
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败! 请确保Flask服务器正在运行")
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
        
        print("-" * 50)

def test_code_generation_algorithm():
    """测试数字码生成算法"""
    print("\n=== 数字码生成算法测试 ===")
    
    from app import create_app, db
    from app.models.user import User
    from app.models.course import Course
    
    app = create_app('testing')
    with app.app_context():
        db.create_all()
        
        # 创建测试教师
        teacher = User(username='test_teacher', email='<EMAIL>', 
                      password='password', role='teacher')
        db.session.add(teacher)
        db.session.commit()
        
        print("测试不同长度的访问码生成:")
        
        for length in [4, 6, 9]:
            course = Course(name=f'Test Course {length}', teacher_id=teacher.id)
            code = course.generate_access_code(length)
            
            print(f"长度 {length}: {code} (实际长度: {len(code)})")
            assert len(code) == length, f"期望长度 {length}，实际长度 {len(code)}"
            assert code.isdigit(), f"访问码应该只包含数字，实际: {code}"
        
        print("\n测试无效长度默认为4位:")
        for invalid_length in [0, 1, 3, 5, 7, 8, 10, -1]:
            course = Course(name=f'Test Course Invalid {invalid_length}', teacher_id=teacher.id)
            code = course.generate_access_code(invalid_length)
            
            print(f"无效长度 {invalid_length}: {code} (实际长度: {len(code)})")
            assert len(code) == 4, f"无效长度应该默认为4位，实际长度 {len(code)}"
            assert code.isdigit(), f"访问码应该只包含数字，实际: {code}"
        
        print("\n测试访问码唯一性:")
        codes = set()
        for i in range(20):
            course = Course(name=f'Unique Test {i}', teacher_id=teacher.id)
            code = course.generate_access_code(4)
            
            assert code not in codes, f"访问码重复: {code}"
            codes.add(code)
            print(f"第 {i+1} 个访问码: {code}")
        
        print("✅ 所有算法测试通过!")

if __name__ == '__main__':
    print("智慧教室课程创建API测试")
    print("=" * 50)
    
    # 测试数字码生成算法
    test_code_generation_algorithm()
    
    # 测试API（需要服务器运行）
    print("\n" + "=" * 50)
    test_course_creation_api()